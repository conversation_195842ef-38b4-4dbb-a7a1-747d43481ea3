"""
混沌测试监控数据仓库
"""
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete, and_, func, desc

from app.repositories.base import BaseRepository
from app.models.chaos.chaos_monitor_data import ChaosMonitorData
from app.schemas.chaos.chaos_monitor import ChaosMonitorDataCreate, ChaosMonitorDataUpdate, ChaosMonitorDataResponse


class ChaosMonitorRepository(BaseRepository[ChaosMonitorData, ChaosMonitorDataCreate, ChaosMonitorDataUpdate]):
    """监控数据仓库"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(ChaosMonitorData, db)
    
    async def batch_insert_metrics(self, metrics_data: List[Dict[str, Any]]) -> List[ChaosMonitorData]:
        """批量插入监控数据"""
        if not metrics_data:
            return []
        
        # 创建监控数据对象
        monitor_objects = []
        for data in metrics_data:
            monitor_obj = ChaosMonitorData(**data)
            monitor_objects.append(monitor_obj)
            self.db.add(monitor_obj)
        
        await self.db.commit()
        
        # 刷新对象以获取ID
        for obj in monitor_objects:
            await self.db.refresh(obj)
        
        return monitor_objects
    
    async def get_metrics_by_time_range(
        self,
        environment_id: int,
        host_id: int,
        start_time: datetime,
        end_time: datetime
    ) -> List[ChaosMonitorData]:
        """根据时间范围获取监控数据"""
        query = (
            select(ChaosMonitorData)
            .where(
                and_(
                    ChaosMonitorData.environment_id == environment_id,
                    ChaosMonitorData.host_id == host_id,
                    ChaosMonitorData.collected_at >= start_time,
                    ChaosMonitorData.collected_at <= end_time
                )
            )
            .order_by(ChaosMonitorData.collected_at)
        )

        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_latest_metrics(
        self,
        environment_id: int,
        host_id: int,
        limit: int = 100
    ) -> List[ChaosMonitorData]:
        """获取最新的监控数据"""
        query = (
            select(ChaosMonitorData)
            .where(
                and_(
                    ChaosMonitorData.environment_id == environment_id,
                    ChaosMonitorData.host_id == host_id
                )
            )
            .order_by(desc(ChaosMonitorData.collected_at))
            .limit(limit)
        )

        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_metrics_by_type(
        self,
        environment_id: int,
        host_id: int,
        metric_type: str,
        hours: int = 1
    ) -> List[ChaosMonitorData]:
        """根据指标类型获取数据"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        query = (
            select(ChaosMonitorData)
            .where(
                and_(
                    ChaosMonitorData.environment_id == environment_id,
                    ChaosMonitorData.host_id == host_id,
                    ChaosMonitorData.metric_type == metric_type,
                    ChaosMonitorData.collected_at >= start_time,
                    ChaosMonitorData.collected_at <= end_time
                )
            )
            .order_by(ChaosMonitorData.collected_at)
        )

        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_metrics_summary(
        self,
        environment_id: int,
        host_id: int,
        hours: int = 1
    ) -> Dict[str, Dict[str, float]]:
        """获取监控数据摘要统计"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        # 查询各指标的统计信息
        query = (
            select(
                ChaosMonitorData.metric_type,
                func.avg(ChaosMonitorData.metric_value).label('avg_value'),
                func.max(ChaosMonitorData.metric_value).label('max_value'),
                func.min(ChaosMonitorData.metric_value).label('min_value'),
                func.count(ChaosMonitorData.id).label('data_points')
            )
            .where(
                and_(
                    ChaosMonitorData.environment_id == environment_id,
                    ChaosMonitorData.host_id == host_id,
                    ChaosMonitorData.collected_at >= start_time,
                    ChaosMonitorData.collected_at <= end_time
                )
            )
            .group_by(ChaosMonitorData.metric_type)
        )
        
        result = await self.db.execute(query)
        rows = result.all()
        
        summary = {}
        for row in rows:
            summary[row.metric_type] = {
                'avg_value': float(row.avg_value) if row.avg_value else 0.0,
                'max_value': float(row.max_value) if row.max_value else 0.0,
                'min_value': float(row.min_value) if row.min_value else 0.0,
                'data_points': row.data_points
            }
        
        return summary
    
    async def delete_data_before(self, cutoff_time: datetime) -> int:
        """删除指定时间之前的数据"""
        query = delete(ChaosMonitorData).where(
            ChaosMonitorData.collected_at < cutoff_time
        )
        
        result = await self.db.execute(query)
        await self.db.commit()
        
        return result.rowcount
    
    async def get_environment_monitor_status(self, environment_id: int) -> Dict[str, Any]:
        """获取环境的监控状态"""
        # 查询监控数据数量和时间范围
        query = (
            select(
                func.count(ChaosMonitorData.id).label('total_points'),
                func.min(ChaosMonitorData.collected_at).label('start_time'),
                func.max(ChaosMonitorData.collected_at).label('end_time'),
                func.count(func.distinct(ChaosMonitorData.host_id)).label('host_count')
            )
            .where(ChaosMonitorData.environment_id == environment_id)
        )
        
        result = await self.db.execute(query)
        row = result.first()
        
        if not row or row.total_points == 0:
            return {
                'has_data': False,
                'total_points': 0,
                'host_count': 0,
                'start_time': None,
                'end_time': None,
                'duration_minutes': 0
            }
        
        duration_minutes = 0
        if row.start_time and row.end_time:
            duration = row.end_time - row.start_time
            duration_minutes = int(duration.total_seconds() / 60)
        
        return {
            'has_data': True,
            'total_points': row.total_points,
            'host_count': row.host_count,
            'start_time': row.start_time,
            'end_time': row.end_time,
            'duration_minutes': duration_minutes
        }
    
    async def cleanup_old_data(self, hours: int = 24) -> int:
        """清理旧数据"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return await self.delete_data_before(cutoff_time)
