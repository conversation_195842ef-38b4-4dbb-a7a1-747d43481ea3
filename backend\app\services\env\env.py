"""
环境管理服务层
处理环境管理相关的业务逻辑
"""
from typing import List, Tuple, Optional, Dict, Any, Type
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import ConfigurableService
from app.repositories.env.env import EnvironmentRepository
from app.repositories.user.user import UserRepository
from app.schemas.env.env import EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse, ConnectionTestRequest, ConnectionTestResponse
from app.schemas.common import SearchParams
from app.models.env.env import Environment
from app.core.config_manager import config_manager
from app.core.exceptions import raise_validation_error, raise_not_found


class EnvironmentService(ConfigurableService[Environment, EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse]):
    """环境管理业务服务"""

    def __init__(self, db: AsyncSession):
        repository = EnvironmentRepository(db)
        self.user_repository = UserRepository(db)

        # 使用配置管理器
        config = {
            'config_manager': config_manager,
            'client_factory': config_manager.get_client_factory()
        }

        super().__init__(db, repository, config)

    @property
    def model_class(self) -> Type[Environment]:
        """返回环境模型类"""
        return Environment

    @property
    def response_schema_class(self) -> Type[EnvironmentResponse]:
        """返回环境响应Schema类"""
        return EnvironmentResponse

    @property
    def config_manager(self):
        """获取配置管理器"""
        return self.get_config_value('config_manager')

    @property
    def client_factory(self):
        """获取客户端工厂"""
        return self.get_config_value('client_factory')

    @property
    def supported_types(self) -> set:
        """获取支持的环境类型"""
        return set(self.config_manager.get_supported_types())

    #  BaseService钩子方法 

    async def _validate_before_create(self, create_data: EnvironmentCreate, **kwargs) -> None:
        """创建环境前的验证"""
        # 检查名称是否重复
        existing = await self.repository.get_by_name(create_data.name)
        if existing:
            raise_validation_error(f"环境名称 '{create_data.name}' 已存在")

        # 检查环境类型是否支持
        if create_data.type not in self.supported_types:
            raise_validation_error(f"不支持的环境类型: {create_data.type}")

    async def _validate_before_update(self, env: Environment, update_data: EnvironmentUpdate, **kwargs) -> None:
        """更新环境前的验证"""
        # 如果要更新名称，检查是否重复
        if update_data.name and update_data.name != env.name:
            existing = await self.repository.get_by_name(update_data.name)
            if existing:
                raise_validation_error(f"环境名称 '{update_data.name}' 已存在")

        # 如果要更新类型，检查是否支持
        if update_data.type and update_data.type not in self.supported_types:
            raise_validation_error(f"不支持的环境类型: {update_data.type}")

    async def _build_list_filters(self, params: SearchParams) -> Dict[str, Any]:
        """构建环境列表查询过滤条件"""
        filters = {}
        if params.keyword:
            filters['keyword'] = params.keyword
        return filters

    # 基类钩子方法实现特殊业务逻辑

    async def _convert_to_response(self, obj: Environment) -> EnvironmentResponse:
        """转换为响应对象 - 包含用户昵称信息"""
        return await self._convert_to_response_with_user_info(obj)

    async def create_environment(self,env_data: EnvironmentCreate,current_user: str) -> EnvironmentResponse:
        """创建环境 - 使用基类通用方法"""
        return await self.create(env_data, current_user)

    async def update_environment(self,env_id: int,env_data: EnvironmentUpdate,current_user: str) -> EnvironmentResponse:
        """更新环境 - 使用基类通用方法"""
        return await self.update(env_id, env_data, current_user)

    async def get_environment(self, env_id: int) -> EnvironmentResponse:
        """获取环境详情 - 使用基类通用方法"""
        return await self.get_by_id(env_id)

    async def _build_list_filters(self, params: SearchParams) -> Dict[str, Any]:
        """构建环境列表查询过滤条件"""
        filters = await super()._build_list_filters(params)
        # 从 params 中提取额外的过滤条件
        return filters

    async def list_environments( self,params: SearchParams,env_type: Optional[str] = None,status: Optional[str] = None,tags: Optional[str] = None) -> Tuple[List[EnvironmentResponse], int]:
        """获取环境列表"""
        # 暂时使用原有的仓储方法，因为需要处理额外的过滤参数
        offset = (params.current - 1) * params.size
        environments, total = await self.repository.search_environments(offset=offset,limit=params.size,keyword=params.keyword,env_type=env_type,status=status,tags=tags)
        # 转换为响应模型，包含用户昵称信息
        env_responses = []
        for env in environments:
            env_response = await self._convert_to_response_with_user_info(env)
            env_responses.append(env_response)
        return env_responses, total

    async def delete_environment(self, env_id: int) -> None:
        """删除环境 - 使用基类通用方法"""
        await self.delete(env_id)

    async def test_environment_connection(self, env_id: int, test_request: ConnectionTestRequest) -> ConnectionTestResponse:
        """测试指定环境的连接"""
        from datetime import datetime

        # 获取环境信息
        environment = await self.repository.get(env_id)
        if not environment:
            raise_not_found(f"环境ID {env_id} 不存在")

        # 构建测试请求
        base_config = {
            'host': environment.host,
            'port': environment.port,
        }

        # 安全地合并配置
        if environment.config:
            base_config.update(environment.config)

        if test_request.config:
            base_config.update(test_request.config)

        connection_test_request = ConnectionTestRequest(
            type=environment.type,
            config=base_config,
            timeout=test_request.timeout
        )

        # 调用通用测试方法
        result = await self.test_connection(connection_test_request)

        # 更新环境状态和最后测试时间
        try:
            # 使用北京时间
            from datetime import timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = datetime.now(beijing_tz)

            update_data = {
                'status': 'connected' if result.success else 'failed',
                'last_test_time': beijing_time,
                'updated_at': beijing_time
            }

            await self.repository.update(db_obj=environment, obj_in=update_data)

        except Exception as e:
            # 记录更新失败，但不影响测试结果返回
            print(f"更新环境状态失败: {str(e)}")

        return result

    async def test_connection(self, test_request: ConnectionTestRequest) -> ConnectionTestResponse:
        """测试环境连接"""

        # 使用客户端工厂创建客户端
        try:
            client = self.client_factory.create_client(test_request.type, test_request.config)
            result = await client.test_connection()

            return ConnectionTestResponse(
                success=result.success,
                message=result.message,
                duration=result.duration,
                details=result.details
            )
        except Exception as e:
            return ConnectionTestResponse(
                success=False,
                message=f"连接测试失败: {str(e)}",
                duration=0,
                details={"error": str(e)}
            )

    async def batch_test_connections(self, env_ids: List[int]) -> List[Dict[str, Any]]:
        """批量测试环境连接"""
        
        results = []
        environments = await self.repository.get_environments_by_ids(env_ids)
        
        for env in environments:
            try:
                test_request = ConnectionTestRequest(
                    type=env.type,
                    config={
                        'host': env.host,
                        'port': env.port,
                        **env.config
                    }
                )
                
                result = await self.test_connection(test_request)
                
                results.append({
                    'id': env.id,
                    'name': env.name,
                    'success': result.success,
                    'message': result.message,
                    'latency': result.latency
                })
                
                # 更新连接状态和测试时间
                await self.repository.update_status(
                    env.id, 
                    'active' if result.success else 'inactive'
                )
                
            except Exception as e:
                results.append({
                    'id': env.id,
                    'name': env.name,
                    'success': False,
                    'message': f"测试失败: {str(e)}",
                    'latency': None
                })
        
        return results

    async def get_environment_stats(self) -> Dict[str, Any]:
        """获取环境统计信息"""

        # 基础类型统计
        type_stats = await self.repository.get_env_types_stats()

        # 状态统计
        status_stats = await self.repository.get_status_stats()

        # 详细类型统计（包含状态分布）
        detailed_type_stats = await self.repository.get_detailed_type_stats()

        # 最近环境
        recent_envs = await self.repository.get_recent_environments(limit=5)
        recent_env_responses = [EnvironmentResponse.model_validate(env) for env in recent_envs]

        return {
            'type_stats': type_stats,
            'status_stats': status_stats,
            'detailed_type_stats': detailed_type_stats,
            'recent_environments': recent_env_responses
        }

    async def get_supported_types(self) -> List[Dict[str, Any]]:
        """获取支持的环境类型详细信息"""
        configs = self.config_manager.get_all_environment_configs()
        return [
            {
                'type': config.name,
                'display_name': config.display_name,
                'description': config.description,
                'default_port': config.default_port,
                'required_fields': config.required_fields,
                'optional_fields': config.optional_fields,
                'icon': config.icon,
                'category': config.category
            }
            for config in configs.values()
        ]

    def _convert_to_response(self, obj: Environment) -> EnvironmentResponse:
        """
        将环境模型对象转换为响应对象
        """
        # 使用基础转换
        return EnvironmentResponse.model_validate(obj)

    async def _convert_to_response_with_user_info(self, obj: Environment) -> EnvironmentResponse:
        """
        将环境模型对象转换为响应对象，包含用户昵称
        """
        # 先使用基础转换
        response_data = obj.__dict__.copy()

        # 查询创建者昵称
        if obj.created_by:
            try:
                # 尝试将created_by作为用户ID查询
                if obj.created_by.isdigit():
                    creator = await self.user_repository.get(int(obj.created_by))
                    if creator and creator.nickname:
                        response_data['created_by'] = creator.nickname
                    elif creator:
                        response_data['created_by'] = creator.username
                else:
                    # 如果不是数字ID，尝试作为用户名查询
                    creator = await self.user_repository.get_by_username(obj.created_by)
                    if creator and creator.nickname:
                        response_data['created_by'] = creator.nickname
                    elif creator:
                        response_data['created_by'] = creator.username
            except Exception:
                # 查询失败时保持原值
                pass

        # 查询更新者昵称
        if obj.updated_by:
            try:
                # 尝试将updated_by作为用户ID查询
                if obj.updated_by.isdigit():
                    updater = await self.user_repository.get(int(obj.updated_by))
                    if updater and updater.nickname:
                        response_data['updated_by'] = updater.nickname
                    elif updater:
                        response_data['updated_by'] = updater.username
                else:
                    # 如果不是数字ID，尝试作为用户名查询
                    updater = await self.user_repository.get_by_username(obj.updated_by)
                    if updater and updater.nickname:
                        response_data['updated_by'] = updater.nickname
                    elif updater:
                        response_data['updated_by'] = updater.username
            except Exception:
                # 查询失败时保持原值
                pass

        # 创建响应对象
        return EnvironmentResponse(**response_data)



