<template>
  <div class="environment-page art-full-height">
    <!-- 搜索栏 -->
    <EnvironmentSearch v-model:filter="defaultFilter" @reset="resetSearch" @search="handleSearch" />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton @click="showDialog('add')" type="primary">新增环境</ElButton>
          <ElButton
            v-if="selectedRows.length > 0"
            @click="batchTestConnections"
            :loading="isLoading"
          >
            批量连接测试
          </ElButton>
        </template>
        <template #right>
          <ElButton @click="showStatsDialog"  type="success" style="margin-left: 5px;">统计信息</ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        @selection-change="handleSelectionChange"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
      </ArtTable>

      <!-- 环境对话框 -->
      <EnvironmentDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :environment="currentEnvironment"
        @submit="handleDialogSubmit"
      />

      <!-- 统计信息对话框 -->
      <EnvironmentStatsDialog
        v-model:visible="statsDialogVisible"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
import { ElMessage, ElMessageBox, ElTag, ElButton } from 'element-plus'
import { useTable } from '@/composables/useTable'
import { EnvironmentService } from '@/api/envApi'
import { EnvironmentListItem } from '@/types/api/environment'
import { useEnvironmentStore } from '@/store/business/environment/index'
import EnvironmentSearch from './modules/environment-search.vue'
import EnvironmentDialog from './modules/environment-dialog.vue'
import EnvironmentStatsDialog from './modules/environment-stats-dialog.vue'

defineOptions({ name: 'Environment' })
const { width } = useWindowSize()

const environmentStore = useEnvironmentStore()

// 弹窗相关
const dialogType = ref<Form.DialogType>('add')
const dialogVisible = ref(false)
const statsDialogVisible = ref(false)
const currentEnvironment = ref<EnvironmentListItem | null>(null)

// 选中行
const selectedRows = ref<EnvironmentListItem[]>([])

// 表单搜索初始值
const defaultFilter = ref({
  keyword: undefined,
  env_type: undefined,
  status: undefined,
  tags: undefined
})

// 环境状态配置
const ENV_STATUS_CONFIG = {
  'connected': { type: 'success' as const, text: '已连接' },
  'failed': { type: 'danger' as const, text: '连接失败' },
  'unknown': { type: 'info' as const, text: '未知' }
} as const

// 环境类型配置
const ENV_TYPE_CONFIG = {
  database: { type: 'primary' as const, text: '数据库' },
  redis: { type: 'danger' as const, text: 'Redis' },
  ssh: { type: 'warning' as const, text: 'SSH' },
  k8s: { type: 'info' as const, text: 'K8s' },
  api: { type: 'success' as const, text: 'API' }
} as const

/**
 * 获取环境状态配置
 */
const getEnvironmentStatusConfig = (status: string) => {
  return (
    ENV_STATUS_CONFIG[status as keyof typeof ENV_STATUS_CONFIG] || {
      type: 'info' as const,
      text: '未知'
    }
  )
}

/**
 * 获取环境类型配置
 */
const getEnvironmentTypeConfig = (type: string) => {
  return (
    ENV_TYPE_CONFIG[type as keyof typeof ENV_TYPE_CONFIG] || {
      type: 'primary' as const,
      text: type
    }
  )
}

/**
 * API适配函数 - 将环境API适配为useTable需要的格式
 */
const getEnvironmentListForTable = async (params: any) => {
  const response = await EnvironmentService.getEnvironmentList(params)
  return {
    records: response.records,
    total: response.total,
    current: response.current,
    size: response.size
  }
}

const {
  columns,
  columnChecks,
  tableData,
  isLoading,
  paginationState,
  searchData,
  searchState,
  resetSearch,
  onPageSizeChange,
  onCurrentPageChange,
  refreshAll
} = useTable<EnvironmentListItem>({
  // 核心配置
  core: {
    apiFn: getEnvironmentListForTable,
    apiParams: {
      page: 1,
      size: 20,
      ...defaultFilter.value
    },
    columnsFactory: () => [
      { type: 'selection' }, // 勾选列
      { type: 'index', width: 60, label: '序号' }, // 序号
      {
        prop: 'name',
        label: '环境名称',
        minWidth: width.value < 500 ? 180 : 150,
        formatter: (row) => {
          return h('div', { class: 'env-name' }, [
            h('div', { style: 'font-weight: 500; margin-bottom: 4px;' }, row.name),
            h('div', { 
              style: 'font-size: 12px; color: #999; line-height: 1.2;'
            }, row.description || '-')
          ])
        }
      },
      {
        prop: 'type',
        label: '类型',
        width: 100,
        formatter: (row) => {
          const typeConfig = getEnvironmentTypeConfig(row.type)
          return h(ElTag, { type: typeConfig.type }, () => typeConfig.text)
        }
      },
      {
        prop: 'host',
        label: '主机地址',
        minWidth: 100,
        formatter: (row) => {
          return row.host ? `${row.host}${row.port ? ':' + row.port : ''}` : '-'
        }
      },
      {
        prop: 'status',
        label: '连接状态',
        width: 100,
        formatter: (row) => {
          const statusConfig = getEnvironmentStatusConfig(row.status)
          return h(ElTag, { type: statusConfig.type }, () => statusConfig.text)
        }
      },
      {
        prop: 'tags',
        label: '标签',
        minWidth: 120,
        formatter: (row) => {

          if (row.tag_list && Array.isArray(row.tag_list) && row.tag_list.length > 0) {
            const tags = row.tag_list.slice(0, 2).map((tag: string) =>
              h(ElTag, {
                size: 'small',
                style: 'margin-right: 4px;'
              }, () => tag)
            )
            if (row.tag_list.length > 2) {
              tags.push(h('span', { style: 'color: #999; font-size: 12px;' }, `+${row.tag_list.length - 2}`))
            }
            return h('div', tags)
          }

          // 如果有tags字符串但没有tag_list，尝试手动解析
          if (row.tags && typeof row.tags === 'string' && row.tags.trim()) {
            const tagArray = row.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
            if (tagArray.length > 0) {
              const tags = tagArray.slice(0, 2).map((tag: string) =>
                h(ElTag, {
                  size: 'small',
                  style: 'margin-right: 4px;'
                }, () => tag)
              )
              if (tagArray.length > 2) {
                tags.push(h('span', { style: 'color: #999; font-size: 12px;' }, `+${tagArray.length - 2}`))
              }
              return h('div', tags)
            }
          }

          return '-'
        }
      },
      {
        prop: 'last_test_time',
        label: '最后测试',
        width: 160,
        formatter: (row) => {
          if (row.last_test_time) {
            const date = new Date(row.last_test_time)
            return h('span', date.toLocaleString('zh-CN'))          }
          return '-'
        }
      },
      {
        prop: 'updated_by',
        label: '更新者',
        width: 160,
        formatter: (row) => {
          return row.updated_by ? row.updated_by : '-'
        }
      },
      {
        prop: 'updated_at',
        label: '更新时间',
        width: 160,
        formatter: (row) => {
          if (row.updated_at) {
            const date = new Date(row.updated_at)
            return h('span', date.toLocaleString('zh-CN'))
          }
          return '-'
        }
      },
      {
        prop: 'operation',
        label: '操作',
        width: 200,
        fixed: 'right', // 固定列
        formatter: (row) =>
          h('div', { style: 'display: flex; gap: 8px;' }, [
            h(ArtButtonTable, {
              type: 'test',           
              loading: (row as any).testing,
              onClick: () => handleTest(row)
            }),
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => showDialog('edit', row)
            }),
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => deleteEnvironment(row)
            })
          ])
      }
    ]
  },
  // 数据处理
  transform: {
  }
})

/**
 * 搜索处理
 * @param params 参数
 */
const handleSearch = (params: Record<string, any>) => {
  // 搜索参数赋值
  Object.assign(searchState, params)
  searchData()
}

/**
 * 显示环境弹窗
 */
const showDialog = (type: Form.DialogType, row?: EnvironmentListItem): void => {
  dialogType.value = type
  if (type === 'edit') {
    currentEnvironment.value = row || null
  } else {
    currentEnvironment.value = null
  }
  nextTick(() => {
    dialogVisible.value = true
  })
}

/**
 * 测试环境连接
 */
const handleTest = async (environment: EnvironmentListItem) => {
  const env = environment as any
  env.testing = true
  try {
    const result = await EnvironmentService.testEnvironmentConnection(environment.id)
    if (result.success) {
      ElMessage.success(`连接测试成功 (${result.duration.toFixed(2)}s)`)
    } else {
      ElMessage.error(`连接测试失败: ${result.message}`)
    }
    // 重新获取列表以更新状态
    await refreshAll()
  } catch (error) {
    ElMessage.error('连接测试失败')
  } finally {
    env.testing = false
  }
}

/**
 * 删除环境
 */
const deleteEnvironment = (row: EnvironmentListItem): void => {
  ElMessageBox.confirm(`确定要删除环境 "${row.name}" 吗？此操作不可恢复。`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await EnvironmentService.deleteEnvironment(row.id)
      ElMessage.success('删除成功')
      await refreshAll()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

/**
 * 处理弹窗提交事件
 */
const handleDialogSubmit = async () => {
  try {
    // 关闭弹窗
    dialogVisible.value = false
    
    // 清空当前环境数据
    currentEnvironment.value = null
    
    // 刷新环境列表
    await refreshAll()
    
    ElMessage.success(dialogType.value === 'add' ? '环境添加成功' : '环境更新成功')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

/**
 * 处理表格行选择变化
 */
const handleSelectionChange = (selection: EnvironmentListItem[]): void => {
  selectedRows.value = selection
}

/**
 * 显示统计信息对话框
 */
const showStatsDialog = () => {
  statsDialogVisible.value = true
}

/**
 * 批量测试连接
 */
const batchTestConnections = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要测试的环境')
    return
  }

  try {
    const envIds = selectedRows.value.map(env => env.id)
    await environmentStore.batchTestConnections(envIds)
    await refreshAll() // 刷新列表
  } catch (error) {
    console.error('批量测试连接失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.environment-page {
  :deep(.env-name) {
    .env-description {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }
}
</style> 