"""修改混沌任务表结构_将env_id和host_ids改为env_ids

Revision ID: 3d51e20f883f
Revises: 0dad6fe84a7b
Create Date: 2025-07-29 02:35:49.876756

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3d51e20f883f'
down_revision: Union[str, None] = '0dad6fe84a7b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chaos_tasks', sa.Column('env_ids', sa.JSON(), nullable=False, comment='目标环境ID列表'))
    op.drop_constraint('chaos_tasks_ibfk_1', 'chaos_tasks', type_='foreignkey')
    op.drop_column('chaos_tasks', 'env_id')
    op.drop_column('chaos_tasks', 'host_ids')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chaos_tasks', sa.Column('host_ids', mysql.JSON(), nullable=False, comment='目标主机ID列表'))
    op.add_column('chaos_tasks', sa.Column('env_id', mysql.INTEGER(), autoincrement=False, nullable=False, comment='环境ID'))
    op.create_foreign_key('chaos_tasks_ibfk_1', 'chaos_tasks', 'environment', ['env_id'], ['id'])
    op.drop_column('chaos_tasks', 'env_ids')
    # ### end Alembic commands ###
