/**
 * 通用分页状态管理
 * 提供可复用的分页逻辑
 */
import { ref, computed, reactive } from 'vue'
import type { PaginationData, PaginationParams } from '@/types/api/common'

/**
 * 分页状态接口
 */
export interface PaginationState {
  current: number
  size: number
  total: number
}

/**
 * 分页配置选项
 */
export interface PaginationOptions {
  defaultSize?: number
  pageSizes?: number[]
  showSizeChanger?: boolean
  showQuickJumper?: boolean
}

/**
 * 创建分页状态管理
 */
export function usePagination(options: PaginationOptions = {}) {
  const {
    defaultSize = 20,
    pageSizes = [10, 20, 50, 100],
    showSizeChanger = true,
    showQuickJumper = true
  } = options

  // 分页状态
  const pagination = reactive<PaginationState>({
    current: 1,
    size: defaultSize,
    total: 0
  })

  // 计算属性
  const totalPages = computed(() => Math.ceil(pagination.total / pagination.size))
  const hasNext = computed(() => pagination.current < totalPages.value)
  const hasPrev = computed(() => pagination.current > 1)
  const startIndex = computed(() => (pagination.current - 1) * pagination.size + 1)
  const endIndex = computed(() => Math.min(pagination.current * pagination.size, pagination.total))

  // 分页参数
  const params = computed<PaginationParams>(() => ({
    current: pagination.current,
    size: pagination.size
  }))

  // 方法
  const setPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      pagination.current = page
    }
  }

  const setSize = (size: number) => {
    pagination.size = size
    pagination.current = 1 // 重置到第一页
  }

  const setTotal = (total: number) => {
    pagination.total = total
  }

  const reset = () => {
    pagination.current = 1
    pagination.size = defaultSize
    pagination.total = 0
  }

  const updateFromResponse = <T>(response: PaginationData<T>) => {
    pagination.current = response.current
    pagination.size = response.size
    pagination.total = response.total
  }

  const nextPage = () => {
    if (hasNext.value) {
      pagination.current++
    }
  }

  const prevPage = () => {
    if (hasPrev.value) {
      pagination.current--
    }
  }

  const goToFirst = () => {
    pagination.current = 1
  }

  const goToLast = () => {
    pagination.current = totalPages.value
  }

  return {
    // 状态
    pagination: readonly(pagination),
    
    // 计算属性
    totalPages,
    hasNext,
    hasPrev,
    startIndex,
    endIndex,
    params,
    
    // 方法
    setPage,
    setSize,
    setTotal,
    reset,
    updateFromResponse,
    nextPage,
    prevPage,
    goToFirst,
    goToLast,
    
    // 配置
    pageSizes,
    showSizeChanger,
    showQuickJumper
  }
}

/**
 * 分页事件处理器
 */
export interface PaginationHandlers {
  onPageChange: (page: number) => void
  onSizeChange: (size: number) => void
  onRefresh: () => void
}

/**
 * 创建分页事件处理器
 */
export function createPaginationHandlers(
  pagination: ReturnType<typeof usePagination>,
  onDataLoad: () => Promise<void> | void
): PaginationHandlers {
  const onPageChange = async (page: number) => {
    pagination.setPage(page)
    await onDataLoad()
  }

  const onSizeChange = async (size: number) => {
    pagination.setSize(size)
    await onDataLoad()
  }

  const onRefresh = async () => {
    await onDataLoad()
  }

  return {
    onPageChange,
    onSizeChange,
    onRefresh
  }
}
