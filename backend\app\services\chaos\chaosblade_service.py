"""
ChaosBlade集成服务
负责与ChaosBlade工具的交互和命令执行
"""
import asyncio
import json
import uuid
import os
import platform
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

from app.core.exceptions import raise_validation_error
from app.utils.logger import setup_logger
from app.utils.clients.ssh_client import SSHClient

logger = setup_logger()


class ChaosBladeService:
    """
    ChaosBlade执行引擎服务
    封装ChaosBlade命令行工具的调用
    """

    def __init__(self):
        self.default_timeout = 300  # 默认超时时间（秒）
        # 使用绝对路径
        self.local_packages_dir = Path(__file__).parent.parent.parent.parent / "uploads" / "avatars" / "chaosblade"

        # 固定使用用户主目录安装
        self.install_path = "~/chaosblade"

    async def check_installation(self, host_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查ChaosBlade是否已安装

        Args:
            host_info: 主机连接信息

        Returns:
            安装状态信息
        """
        try:
            # 创建SSH连接来展开路径
            ssh_config = {
                "host": host_info["host"],
                "port": host_info.get("port", 22),
                "username": host_info["username"],
                "password": host_info.get("password"),
                "private_key": host_info.get("private_key_path")
            }
            ssh_client = SSHClient(ssh_config)

            try:
                # 连接SSH
                connect_result = await ssh_client.connect()
                if not connect_result.success:
                    return {
                        "installed": False,
                        "error_message": f"SSH连接失败: {connect_result.message}",
                        "last_check_time": datetime.now().isoformat()
                    }

                # 展开安装路径
                install_path = await self._expand_user_path(ssh_client, self.install_path)
                blade_path = f"{install_path}/blade"

                # 检查blade命令是否存在
                check_cmd = f"ls {blade_path} && {blade_path} version"
                result = await ssh_client.execute_command(check_cmd)

                if result.get("success", False) and result.get("exit_status", -1) == 0:
                    # 解析版本信息
                    version_info = self._parse_version_output(result.get("stdout", ""))
                    return {
                        "installed": True,
                        "version": version_info.get("version", "unknown"),
                        "install_path": blade_path,
                        "last_check_time": datetime.now().isoformat()
                    }
                else:
                    return {
                        "installed": False,
                        "error_message": result.get("stderr", result.get("error", "检查失败")),
                        "last_check_time": datetime.now().isoformat()
                    }
            finally:
                await ssh_client.disconnect()

        except Exception as e:
            logger.error(f"检查ChaosBlade安装状态失败: {str(e)}")
            return {
                "installed": False,
                "error_message": str(e),
                "last_check_time": datetime.now().isoformat()
            }

    async def install_chaosblade(self, host_info: Dict[str, Any], force_reinstall: bool = False) -> Dict[str, Any]:
        """
        在目标主机安装ChaosBlade
        
        Args:
            host_info: 主机连接信息
            force_reinstall: 是否强制重新安装
            
        Returns:
            安装结果
        """
        try:
            # 如果不是强制重新安装，先检查是否已安装
            if not force_reinstall:
                status = await self.check_installation(host_info)
                if status["installed"]:
                    return {
                        "success": True,
                        "message": "ChaosBlade已安装",
                        "version": status.get("version"),
                        "skipped": True
                    }

            # 下载和安装ChaosBlade
            install_commands = [
                "mkdir -p /opt/chaosblade",
                "cd /opt/chaosblade",
                "wget -O chaosblade.tar.gz https://github.com/chaosblade-io/chaosblade/releases/download/v1.7.2/chaosblade-1.7.2-linux-amd64.tar.gz",
                "tar -xzf chaosblade.tar.gz --strip-components=1",
                "chmod +x blade",
                "./blade version"
            ]
            
            install_cmd = " && ".join(install_commands)
            result = await self._execute_ssh_command(host_info, install_cmd, timeout=600)
            
            if result["exit_code"] == 0:
                version_info = self._parse_version_output(result["output"])
                return {
                    "success": True,
                    "message": "ChaosBlade安装成功",
                    "version": version_info.get("version", "unknown"),
                    "install_path": self.blade_path,
                    "output": result["output"]
                }
            else:
                return {
                    "success": False,
                    "message": "ChaosBlade安装失败",
                    "error": result["error"],
                    "output": result["output"]
                }
                
        except Exception as e:
            logger.error(f"安装ChaosBlade失败: {str(e)}")
            return {
                "success": False,
                "message": "ChaosBlade安装失败",
                "error": str(e)
            }

    async def install_chaosblade_via_sftp(self, host_info: Dict[str, Any], force_reinstall: bool = False) -> Dict[str, Any]:
        """
        通过SFTP上传本地包安装ChaosBlade

        Args:
            host_info: 主机连接信息
            force_reinstall: 是否强制重新安装

        Returns:
            安装结果
        """
        try:
            # 如果不是强制重新安装，先检查是否已安装
            if not force_reinstall:
                status = await self.check_installation(host_info)
                if status["installed"]:
                    return {
                        "success": True,
                        "message": "ChaosBlade已安装",
                        "version": status.get("version"),
                        "skipped": True
                    }

            # 选择合适的安装包
            package_path = self._select_package_for_host(host_info)
            if not package_path:
                return {
                    "success": False,
                    "message": "未找到适合的ChaosBlade安装包",
                    "error": "No suitable package found"
                }

            # 通过SFTP上传并安装
            result = await self._upload_and_install_package(host_info, package_path)
            return result

        except Exception as e:
            logger.error(f"通过SFTP安装ChaosBlade失败: {str(e)}")
            return {
                "success": False,
                "message": "ChaosBlade安装失败",
                "error": str(e)
            }

    async def execute_fault(self, host_info: Dict[str, Any], fault_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行故障注入
        
        Args:
            host_info: 主机连接信息
            fault_config: 故障配置
            
        Returns:
            执行结果
        """
        try:
            # 构建ChaosBlade命令
            command = self._build_fault_command(fault_config)
            logger.info(f"执行故障注入命令: {command}")
            
            # 执行命令
            result = await self._execute_ssh_command(host_info, command)
            
            if result["exit_code"] == 0:
                # 解析执行结果，获取UID
                chaos_uid = self._parse_chaos_uid(result["output"])
                return {
                    "success": True,
                    "chaos_uid": chaos_uid,
                    "command": command,
                    "output": result["output"],
                    "start_time": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "command": command,
                    "error": result["error"],
                    "output": result["output"],
                    "exit_code": result["exit_code"]
                }
                
        except Exception as e:
            logger.error(f"执行故障注入失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def destroy_fault(self, host_info: Dict[str, Any], chaos_uid: str) -> Dict[str, Any]:
        """
        销毁故障注入

        Args:
            host_info: 主机连接信息
            chaos_uid: ChaosBlade执行UID

        Returns:
            销毁结果
        """
        try:
            # 获取实际的blade路径
            blade_path = await self._get_blade_path(host_info)
            command = f"{blade_path} destroy {chaos_uid}"
            logger.info(f"销毁故障注入命令: {command}")

            result = await self._execute_ssh_command(host_info, command)

            return {
                "success": result["exit_code"] == 0,
                "command": command,
                "output": result["output"],
                "error": result.get("error"),
                "exit_code": result["exit_code"],
                "destroy_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"销毁故障注入失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def query_fault_status(self, host_info: Dict[str, Any], chaos_uid: str) -> Dict[str, Any]:
        """
        查询故障状态

        Args:
            host_info: 主机连接信息
            chaos_uid: ChaosBlade执行UID

        Returns:
            状态信息
        """
        try:
            # 获取实际的blade路径
            blade_path = await self._get_blade_path(host_info)
            command = f"{blade_path} status --type create --uid {chaos_uid}"
            result = await self._execute_ssh_command(host_info, command)

            if result["exit_code"] == 0:
                status_info = self._parse_status_output(result["output"])
                return {
                    "success": True,
                    "status": status_info,
                    "output": result["output"]
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "output": result["output"]
                }

        except Exception as e:
            logger.error(f"查询故障状态失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def _build_fault_command(self, fault_config: Dict[str, Any], blade_path: str = None) -> str:
        """
        构建故障注入命令

        Args:
            fault_config: 故障配置
            blade_path: blade可执行文件路径

        Returns:
            ChaosBlade命令
        """
        fault_type = fault_config.get("fault_type")
        # 参数可能在params字段中，也可能直接在fault_config中
        params = fault_config.get("params", {})
        if not params:
            # 如果params为空，则从fault_config中提取参数（排除fault_type）
            params = {k: v for k, v in fault_config.items() if k != "fault_type"}

        # 基础命令（使用传入的路径或默认路径）
        blade_exec = blade_path or f"{self.install_path}/blade"
        cmd_parts = [blade_exec, "create"]
        
        # 根据故障类型构建命令
        if fault_type == "cpu":
            cmd_parts.extend(["cpu", "load"])
            if "cpu-percent" in params:
                cmd_parts.extend(["--cpu-percent", str(params["cpu-percent"])])
            if "timeout" in params:
                cmd_parts.extend(["--timeout", str(params["timeout"])])
                
        elif fault_type == "memory":
            cmd_parts.extend(["mem", "load"])
            if "mem-percent" in params:
                cmd_parts.extend(["--mem-percent", str(params["mem-percent"])])
            if "timeout" in params:
                cmd_parts.extend(["--timeout", str(params["timeout"])])
                
        elif fault_type == "network":
            if params.get("action") == "delay":
                cmd_parts.extend(["network", "delay"])
                if "time" in params:
                    cmd_parts.extend(["--time", str(params["time"])])
                if "interface" in params:
                    cmd_parts.extend(["--interface", params["interface"]])
            elif params.get("action") == "loss":
                cmd_parts.extend(["network", "loss"])
                if "percent" in params:
                    cmd_parts.extend(["--percent", str(params["percent"])])
                if "interface" in params:
                    cmd_parts.extend(["--interface", params["interface"]])
                    
        elif fault_type == "disk":
            cmd_parts.extend(["disk", "burn"])
            if "read" in params and params["read"]:
                cmd_parts.append("--read")
            if "write" in params and params["write"]:
                cmd_parts.append("--write")
            if "size" in params:
                cmd_parts.extend(["--size", params["size"]])
            if "timeout" in params:
                cmd_parts.extend(["--timeout", str(params["timeout"])])
                
        elif fault_type == "process":
            if params.get("action") == "kill":
                cmd_parts.extend(["process", "kill"])
                if "process" in params:
                    cmd_parts.extend(["--process", params["process"]])
            elif params.get("action") == "stop":
                cmd_parts.extend(["process", "stop"])
                if "process" in params:
                    cmd_parts.extend(["--process", params["process"]])
        
        return " ".join(cmd_parts)

    async def _execute_ssh_command(self, host_info: Dict[str, Any], command: str, timeout: int = None) -> Dict[str, Any]:
        """
        通过SSH执行命令

        Args:
            host_info: 主机连接信息
            command: 要执行的命令
            timeout: 超时时间

        Returns:
            执行结果
        """
        ssh_client = None
        try:
            # 创建SSH连接
            ssh_config = {
                "host": host_info["host"],
                "port": host_info.get("port", 22),
                "username": host_info["username"],
                "password": host_info.get("password"),
                "private_key": host_info.get("private_key_path")
            }
            logger.info(f"执行SSH命令: {command} 在主机 {host_info['host']}:{host_info.get('port', 22)}")
            ssh_client = SSHClient(ssh_config)

            # 连接到主机
            connect_result = await ssh_client.connect()
            if not connect_result.success:
                logger.error(f"SSH连接失败: {connect_result.message}")
                return {
                    "exit_code": -1,
                    "output": "",
                    "error": f"SSH连接失败: {connect_result.message}"
                }

            # 执行命令
            result = await ssh_client.execute_command(command, timeout or 60)
            logger.info(f"命令执行结果: success={result.get('success')}, exit_status={result.get('exit_status')}")
            logger.info(f"命令输出: {result.get('stdout', '')[:200]}...")  # 只记录前200字符

            return {
                "exit_code": result.get("exit_status", -1),
                "output": result.get("stdout", ""),
                "error": result.get("stderr", result.get("error", ""))
            }

        except Exception as e:
            logger.error(f"SSH命令执行异常: {str(e)}")
            return {
                "exit_code": -1,
                "output": "",
                "error": str(e)
            }
        finally:
            if ssh_client:
                await ssh_client.disconnect()

    def _select_package_for_host(self, host_info: Dict[str, Any]) -> Optional[Path]:
        """
        为主机选择合适的ChaosBlade安装包

        Args:
            host_info: 主机连接信息

        Returns:
            安装包路径或None
        """
        try:
            # 检查本地包目录是否存在
            if not self.local_packages_dir.exists():
                logger.warning(f"本地包目录不存在: {self.local_packages_dir}")
                return None

            # 获取主机架构信息（这里简化处理，实际可以通过SSH获取）
            # 默认使用amd64架构
            arch = "amd64"

            # 查找匹配的包文件
            pattern = f"chaosblade-*-linux-{arch}.tar.gz"
            packages = list(self.local_packages_dir.glob(pattern))

            logger.info(f"搜索模式: {pattern}")
            logger.info(f"本地包目录: {self.local_packages_dir}")
            logger.info(f"找到的包: {packages}")

            if packages:
                # 返回最新版本的包（按文件名排序）
                latest_package = sorted(packages)[-1]
                logger.info(f"选择安装包: {latest_package}")
                return latest_package
            else:
                # 如果没找到amd64包，尝试查找所有可用的包
                all_packages = list(self.local_packages_dir.glob("chaosblade-*.tar.gz"))
                logger.warning(f"未找到amd64包，所有可用包: {all_packages}")

                if all_packages:
                    # 优先选择amd64，其次arm64
                    for pkg in all_packages:
                        if "amd64" in pkg.name:
                            logger.info(f"找到amd64包: {pkg}")
                            return pkg

                    # 如果没有amd64，使用第一个可用的包
                    selected_pkg = all_packages[0]
                    logger.info(f"使用第一个可用包: {selected_pkg}")
                    return selected_pkg

                logger.error(f"未找到任何ChaosBlade安装包")
                return None

        except Exception as e:
            logger.error(f"选择安装包失败: {str(e)}")
            return None

    async def _expand_user_path(self, ssh_client, path: str) -> str:
        """
        展开用户主目录路径

        Args:
            ssh_client: SSH客户端
            path: 路径（可能包含~）

        Returns:
            str: 展开后的绝对路径
        """
        if path.startswith("~/"):
            try:
                # 获取用户主目录
                result = await ssh_client.execute_command("echo $HOME", timeout=5)
                if result.get("success"):
                    home_dir = result.get("stdout", "").strip()
                    if home_dir:
                        expanded_path = path.replace("~", home_dir)
                        logger.info(f"展开路径: {path} -> {expanded_path}")
                        return expanded_path
            except Exception as e:
                logger.warning(f"展开用户路径失败: {str(e)}")

        return path

    async def _get_blade_path(self, host_info: Dict[str, Any]) -> str:
        """
        获取实际的blade可执行文件路径

        Args:
            host_info: 主机连接信息

        Returns:
            str: blade可执行文件的完整路径
        """
        ssh_config = {
            "host": host_info["host"],
            "port": host_info.get("port", 22),
            "username": host_info["username"],
            "password": host_info.get("password"),
            "private_key": host_info.get("private_key_path")
        }
        ssh_client = SSHClient(ssh_config)

        try:
            connect_result = await ssh_client.connect()
            if connect_result.success:
                install_path = await self._expand_user_path(ssh_client, self.install_path)
                return f"{install_path}/blade"
            else:
                # 连接失败时返回默认路径
                return f"{self.install_path}/blade"
        finally:
            await ssh_client.disconnect()

    async def _upload_and_install_package(self, host_info: Dict[str, Any], package_path: Path) -> Dict[str, Any]:
        """
        上传并安装ChaosBlade包

        Args:
            host_info: 主机连接信息
            package_path: 本地包路径

        Returns:
            安装结果
        """
        ssh_client = None
        try:
            # 创建SSH连接
            ssh_config = {
                "host": host_info["host"],
                "port": host_info.get("port", 22),
                "username": host_info["username"],
                "password": host_info.get("password"),
                "private_key": host_info.get("private_key_path")
            }
            logger.info(f"开始SSH连接到 {host_info['host']}:{host_info.get('port', 22)}")
            ssh_client = SSHClient(ssh_config)

            # 连接到主机
            connect_result = await ssh_client.connect()
            logger.info(f"SSH连接结果: success={connect_result.success}, message={connect_result.message}")
            if not connect_result.success:
                return {
                    "success": False,
                    "message": "SSH连接失败",
                    "error": connect_result.message
                }

            # 展开用户主目录路径
            install_path = await self._expand_user_path(ssh_client, self.install_path)

            # 创建远程临时目录
            logger.info("创建远程临时目录 /tmp/chaosblade_install")
            mkdir_result = await ssh_client.execute_command("mkdir -p /tmp/chaosblade_install")
            logger.info(f"创建临时目录结果: {mkdir_result}")

            # 上传安装包
            remote_package_path = f"/tmp/chaosblade_install/{package_path.name}"
            logger.info(f"开始上传文件: {package_path} -> {remote_package_path}")
            upload_result = await ssh_client.upload_file(str(package_path), remote_package_path)
            logger.info(f"文件上传结果: {upload_result}")

            if not upload_result["success"]:
                return {
                    "success": False,
                    "message": "上传安装包失败",
                    "error": upload_result.get("error", "上传失败")
                }

            # 执行安装命令（用户主目录，无需sudo）
            install_commands = [
                f"mkdir -p {install_path}",
                f"cd {install_path}",
                f"tar -xzf {remote_package_path} --strip-components=1",
                "chmod +x blade",
                "./blade version",
                f"rm -f {remote_package_path}"  # 清理临时文件
            ]

            install_cmd = " && ".join(install_commands)
            logger.info(f"执行安装命令: {install_cmd}")
            logger.info(f"安装路径: {install_path}")
            result = await ssh_client.execute_command(install_cmd, timeout=300)
            logger.info(f"安装命令执行结果: {result}")

            if result.get("success", False) and result.get("exit_status", -1) == 0:
                version_info = self._parse_version_output(result.get("stdout", ""))
                blade_path = f"{install_path}/blade"
                return {
                    "success": True,
                    "message": f"ChaosBlade安装成功 (路径: {install_path})",
                    "version": version_info.get("version", "unknown"),
                    "install_path": blade_path,
                    "output": result.get("stdout", ""),
                    "method": "sftp"
                }
            else:
                return {
                    "success": False,
                    "message": "ChaosBlade安装失败",
                    "error": result.get("error", result.get("stderr", "安装失败")),
                    "output": result.get("stdout", ""),
                    "install_path": install_path
                }

        except Exception as e:
            logger.error(f"上传安装失败: {str(e)}")
            return {
                "success": False,
                "message": "上传安装失败",
                "error": str(e)
            }
        finally:
            if ssh_client:
                await ssh_client.disconnect()

    def _parse_version_output(self, output: str) -> Dict[str, Any]:
        """解析版本信息输出"""
        version_info = {"version": "unknown"}
        
        if "version" in output.lower():
            # 简单的版本解析
            parts = output.split()
            for i, part in enumerate(parts):
                if "version" in part.lower() and i + 1 < len(parts):
                    version_info["version"] = parts[i + 1]
                    break
        
        return version_info

    def _parse_chaos_uid(self, output: str) -> Optional[str]:
        """解析ChaosBlade执行UID"""
        try:
            # 首先尝试解析JSON格式
            if output.strip().startswith('{'):
                import json
                result = json.loads(output.strip())

                # 检查不同的JSON格式
                if isinstance(result, dict):
                    # 格式1: {"code":200,"success":true,"result":"uid"}
                    if "result" in result and isinstance(result["result"], str):
                        return result["result"]

                    # 格式2: {"uid": "xxx", ...}
                    if "uid" in result:
                        return result["uid"]

                    # 格式3: {"data": {"uid": "xxx"}}
                    if "data" in result and isinstance(result["data"], dict) and "uid" in result["data"]:
                        return result["data"]["uid"]

            # 如果不是JSON，尝试解析文本格式
            if "uid:" in output:
                # 提取UID
                parts = output.split("uid:")
                if len(parts) > 1:
                    uid = parts[1].strip().split()[0]
                    return uid

            # 尝试解析 "Create chaos successfully, uid: xxx" 格式
            if "Create chaos successfully" in output and "uid" in output:
                import re
                match = re.search(r'uid[:\s]+([a-f0-9]+)', output, re.IGNORECASE)
                if match:
                    return match.group(1)

        except Exception as e:
            logger.error(f"解析chaos_uid失败: {str(e)}, output: {output[:200]}")

        return None

    def _parse_status_output(self, output: str) -> Dict[str, Any]:
        """解析状态查询输出"""
        try:
            # 尝试解析JSON格式的状态输出
            status_data = json.loads(output)
            if status_data.get("success") and status_data.get("result"):
                result = status_data["result"][0]
                return {
                    "uid": result.get("uid"),
                    "status": result.get("status"),
                    "success": True
                }
        except (json.JSONDecodeError, KeyError, IndexError):
            pass
        
        return {
            "status": "unknown",
            "success": False,
            "raw_output": output
        }

    async def uninstall_chaosblade(self, host_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        卸载ChaosBlade

        Args:
            host_info: 主机连接信息

        Returns:
            卸载结果
        """
        try:
            # 创建SSH连接
            ssh_config = {
                "host": host_info["host"],
                "port": host_info.get("port", 22),
                "username": host_info["username"],
                "password": host_info.get("password"),
                "private_key": host_info.get("private_key_path")
            }
            logger.info(f"开始卸载ChaosBlade: {host_info['host']}:{host_info.get('port', 22)}")
            ssh_client = SSHClient(ssh_config)

            try:
                # 连接到主机
                connect_result = await ssh_client.connect()
                if not connect_result.success:
                    return {
                        "success": False,
                        "message": "SSH连接失败",
                        "error": connect_result.message
                    }

                # 展开安装路径
                install_path = await self._expand_user_path(ssh_client, self.install_path)
                blade_path = f"{install_path}/blade"

                logger.info(f"检查ChaosBlade安装路径: {install_path}")
                logger.info(f"检查blade可执行文件: {blade_path}")

                # 检查ChaosBlade目录是否存在
                check_dir_cmd = f"ls -la {install_path}"
                dir_result = await ssh_client.execute_command(check_dir_cmd)
                logger.info(f"目录检查结果: {dir_result}")

                # 检查blade文件是否存在
                check_cmd = f"ls -la {blade_path}"
                check_result = await ssh_client.execute_command(check_cmd)
                logger.info(f"blade文件检查结果: {check_result}")

                # 如果目录不存在，说明已经卸载
                if not dir_result.get("success", False) or dir_result.get("exit_status", -1) != 0:
                    return {
                        "success": True,
                        "message": "ChaosBlade未安装或已卸载",
                        "output": "未找到ChaosBlade安装目录"
                    }

                # 尝试停止所有正在运行的故障注入（如果blade文件存在）
                if check_result.get("success", False) and check_result.get("exit_status", -1) == 0:
                    logger.info("停止所有正在运行的故障注入...")
                    stop_cmd = f"{blade_path} destroy --all"
                    stop_result = await ssh_client.execute_command(stop_cmd, timeout=60)
                    logger.info(f"停止故障注入结果: {stop_result}")
                else:
                    logger.info("blade文件不存在，跳过停止故障注入步骤")

                # 删除ChaosBlade安装目录
                logger.info(f"删除ChaosBlade安装目录: {install_path}")
                uninstall_cmd = f"rm -rf {install_path}"
                uninstall_result = await ssh_client.execute_command(uninstall_cmd, timeout=30)
                logger.info(f"卸载命令执行结果: {uninstall_result}")

                if uninstall_result.get("success", False) and uninstall_result.get("exit_status", -1) == 0:
                    return {
                        "success": True,
                        "message": f"ChaosBlade卸载成功 (路径: {install_path})",
                        "output": uninstall_result.get("stdout", ""),
                        "install_path": install_path
                    }
                else:
                    return {
                        "success": False,
                        "message": "ChaosBlade卸载失败",
                        "error": uninstall_result.get("stderr", uninstall_result.get("error", "卸载失败")),
                        "output": uninstall_result.get("stdout", "")
                    }

            finally:
                await ssh_client.disconnect()

        except Exception as e:
            logger.error(f"卸载ChaosBlade失败: {str(e)}")
            return {
                "success": False,
                "message": "卸载失败",
                "error": str(e)
            }


