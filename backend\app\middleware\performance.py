"""
性能监控中间件
"""
import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.logger import setup_logger

logger = setup_logger()


class PerformanceMiddleware(BaseHTTPMiddleware):
    """
    性能监控中间件
    监控API请求的响应时间和性能指标
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并监控性能
        
        Args:
            request: HTTP请求
            call_next: 下一个处理函数
            
        Returns:
            HTTP响应
        """
        # 记录开始时间
        start_time = time.time()
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 添加响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        # 记录性能日志
        if process_time > 1.0:  # 超过1秒的请求记录警告
            logger.warning(
                f"慢请求警告 - {request.method} {request.url.path} - "
                f"处理时间: {process_time:.3f}s"
            )
        elif process_time > 0.5:  # 超过0.5秒的请求记录信息
            logger.info(
                f"性能提醒 - {request.method} {request.url.path} - "
                f"处理时间: {process_time:.3f}s"
            )
        
        return response 