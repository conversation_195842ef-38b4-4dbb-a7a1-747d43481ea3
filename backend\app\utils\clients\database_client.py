"""
数据库连接客户端
支持MySQL、PostgreSQL、SQLite等数据库类型
"""
import time
import asyncio
from typing import Dict, Any, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from .base_client import BaseClient, ConnectionResult


class DatabaseClient(BaseClient):
    """
    数据库连接客户端
    支持多种数据库类型的连接和测试
    """

    # 支持的数据库类型及其对应的驱动
    SUPPORTED_DB_TYPES = {
        'mysql': {
            'sync_driver': 'pymysql',
            'async_driver': 'aiomysql',
            'default_port': 3306,
            'test_query': 'SELECT 1'
        },
        'postgresql': {
            'sync_driver': 'psycopg2',
            'async_driver': 'asyncpg',
            'default_port': 5432,
            'test_query': 'SELECT 1'
        },
        'sqlite': {
            'sync_driver': 'sqlite3',
            'async_driver': 'aiosqlite',
            'default_port': None,
            'test_query': 'SELECT 1'
        }
    }

    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据库客户端
        
        Args:
            config: 数据库连接配置
                - db_type: 数据库类型 (mysql/postgresql/sqlite)
                - host: 主机地址
                - port: 端口号
                - database_name: 数据库名
                - username: 用户名
                - password: 密码
        """
        super().__init__(config)
        self.engine = None
        
        # 验证必要配置
        if 'db_type' not in config:
            raise ValueError("数据库类型 (db_type) 是必需的")
        
        if config['db_type'] not in self.SUPPORTED_DB_TYPES:
            raise ValueError(f"不支持的数据库类型: {config['db_type']}")

    def _build_connection_url(self, use_async: bool = False) -> str:
        """
        构建数据库连接URL
        
        Args:
            use_async: 是否使用异步驱动
            
        Returns:
            str: 连接URL
        """
        db_type = self.config['db_type']
        db_info = self.SUPPORTED_DB_TYPES[db_type]
        
        if db_type == 'sqlite':
            # SQLite 连接
            database_path = self.config.get('database_name', 'test.db')
            if use_async:
                return f"sqlite+aiosqlite:///{database_path}"
            else:
                return f"sqlite:///{database_path}"
        
        # 其他数据库连接
        driver = db_info['async_driver'] if use_async else db_info['sync_driver']
        host = self.config.get('host', 'localhost')
        port = self.config.get('port', db_info['default_port'])
        database = self.config.get('database_name', '')
        username = self.config.get('username', '')
        password = self.config.get('password', '')
        
        if db_type == 'mysql':
            prefix = f"mysql+{driver}"
        elif db_type == 'postgresql':
            prefix = f"postgresql+{driver}"
        else:
            prefix = db_type
        
        # 构建连接URL
        if username and password:
            auth = f"{username}:{password}"
        elif username:
            auth = username
        else:
            auth = ""

        # 如果没有指定数据库名，使用默认数据库
        if not database:
            if db_type == 'mysql':
                database = 'mysql'  # MySQL默认系统数据库
            elif db_type == 'postgresql':
                database = 'postgres'  # PostgreSQL默认数据库
            # SQLite不需要数据库名

        if auth:
            return f"{prefix}://{auth}@{host}:{port}/{database}"
        else:
            return f"{prefix}://{host}:{port}/{database}"

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立数据库连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 创建异步引擎
            connection_url = self._build_connection_url(use_async=True)
            self.engine = create_async_engine(
                connection_url,
                pool_pre_ping=True,
                pool_recycle=3600,
                connect_args={"server_settings": {"application_name": "DpTestPlatform"}} 
                if self.config['db_type'] == 'postgresql' else {}
            )
            
            # 测试连接
            async with self.engine.begin() as conn:
                db_info = self.SUPPORTED_DB_TYPES[self.config['db_type']]
                result = await conn.execute(text(db_info['test_query']))
                result.fetchone()
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到 {self.config['db_type']} 数据库",
                duration=duration,
                details={
                    "database_type": self.config['db_type'],
                    "database_name": self.config.get('database_name'),
                    "host": self.config.get('host'),
                    "port": self.config.get('port')
                }
            )
            
        except SQLAlchemyError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"数据库连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "SQLAlchemyError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开数据库连接"""
        try:
            if self.engine:
                await self.engine.dispose()
                self.engine = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试数据库连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        start_time = time.time()
        
        try:
            # 如果没有建立连接，先建立连接
            if not self.engine:
                connect_result = await self.connect(timeout)
                if not connect_result.success:
                    return connect_result
            
            # 执行测试查询
            db_info = self.SUPPORTED_DB_TYPES[self.config['db_type']]
            
            async with self.engine.begin() as conn:
                # 执行基础测试查询
                result = await conn.execute(text(db_info['test_query']))
                test_result = result.fetchone()

                # 获取数据库版本信息
                if self.config['db_type'] == 'mysql':
                    version_result = await conn.execute(text("SELECT VERSION()"))
                elif self.config['db_type'] == 'postgresql':
                    version_result = await conn.execute(text("SELECT version()"))
                else:  # sqlite
                    version_result = await conn.execute(text("SELECT sqlite_version()"))

                version_info = version_result.fetchone()
            
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"{self.config['db_type']} 数据库连接测试成功",
                duration=duration,
                details={
                    "database_type": self.config['db_type'],
                    "database_name": self.config.get('database_name'),
                    "version": str(version_info[0]) if version_info and len(version_info) > 0 else "未知",
                    "test_query_result": str(test_result[0]) if test_result and len(test_result) > 0 else None
                }
            )
            
        except SQLAlchemyError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"数据库测试失败: {str(e)}",
                duration=duration,
                details={"error_type": "SQLAlchemyError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"测试异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        执行SQL查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            Dict: 查询结果
        """
        if not self.engine:
            raise RuntimeError("数据库未连接")
        
        try:
            async with self.engine.begin() as conn:
                result = await conn.execute(text(query), params or {})
                
                if result.returns_rows:
                    rows = await result.fetchall()
                    return {
                        "success": True,
                        "rows": [dict(row._mapping) for row in rows],
                        "row_count": len(rows)
                    }
                else:
                    return {
                        "success": True,
                        "affected_rows": result.rowcount
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            } 