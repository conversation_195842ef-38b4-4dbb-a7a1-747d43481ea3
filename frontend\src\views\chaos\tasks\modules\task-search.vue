<template>
  <ArtSearchBar
    :filter="filter as Record<string, any>"
    :items="searchItems"
    @search="handleSearch"
    @reset="handleReset"
    @update:filter="$emit('update:filter', $event as ChaosTaskSearchParams)"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import { useEnvironmentStore } from '@/store/business/environment/index'
import type { ChaosTaskSearchParams } from '@/types/api/chaos'
import type { SearchFormItem } from '@/types/component'

defineOptions({ name: 'TaskSearch' })

interface Props {
  filter: ChaosTaskSearchParams
}

interface Emits {
  (e: 'search'): void
  (e: 'reset'): void
  (e: 'update:filter', value: ChaosTaskSearchParams): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const environmentStore = useEnvironmentStore()
const environments = ref<any[]>([])

// 搜索项配置
const searchItems = ref<SearchFormItem[]>([
  {
    prop: 'keyword',
    label: '关键词',
    type: 'input' as const,
    placeholder: '搜索任务名称或描述'
  },
  {
    prop: 'env_ids',
    label: '环境',
    type: 'select' as const,
    placeholder: '选择环境',
    multiple: true,
    options: () => environments.value
  },
  {
    prop: 'fault_type',
    label: '故障类型',
    type: 'select' as const,
    placeholder: '选择故障类型',
    options: [
      { label: 'CPU故障', value: 'cpu' },
      { label: '内存故障', value: 'memory' },
      { label: '网络故障', value: 'network' },
      { label: '磁盘故障', value: 'disk' },
      { label: '进程故障', value: 'process' },
      { label: 'K8S故障', value: 'k8s' },
      { label: 'JVM故障', value: 'jvm' },
      { label: 'Docker故障', value: 'docker' }
    ]
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    placeholder: '选择状态',
    options: [
      { label: '待执行', value: 'pending' },
      { label: '运行中', value: 'running' },
      { label: '已暂停', value: 'paused' },
      { label: '已完成', value: 'completed' },
      { label: '已失败', value: 'failed' },
      { label: '已取消', value: 'cancelled' }
    ]
  }
])

onMounted(async () => {
  try {
    const result = await environmentStore.fetchEnvironments()
    if (result) {
      environments.value = result.records.map(env => ({
        label: env.name,
        value: env.id
      }))
    }
  } catch (error) {
    console.error('加载环境列表失败:', error)
  }
})

const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}
</script>
