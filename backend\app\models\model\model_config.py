"""
模型配置数据模型
支持多种AI模型平台的统一管理
"""
from sqlalchemy import Column, String, Integer, Text
from app.models.base import BaseModel


class ModelConfig(BaseModel):
    """
    模型配置模型
    支持多种AI模型平台的统一管理和调用
    """
    __tablename__ = "model_config"

    # 基础信息
    name = Column(String(100), unique=True, index=True, nullable=False, comment="模型名称（唯一标识）")
    platform = Column(String(50), nullable=False, index=True, comment="所属平台（local/deepseek/qwen等）")
    description = Column(Text, nullable=True, comment="模型描述")

    # 连接信息
    api_url = Column(String(500), nullable=False, comment="API地址")
    api_key_encrypted = Column(String(255), nullable=True, comment="加密的API Key（本地模型可为空）")
    timeout_seconds = Column(Integer, default=30, comment="请求超时时间（秒）")

    # 模型配置
    model_name = Column(String(100), nullable=False, comment="实际模型名称")
    max_tokens = Column(Integer, default=2048, comment="最大令牌数")
    prompt = Column(Text, nullable=True, comment="系统提示词")

    # 状态管理
    status = Column(
        String(20),
        default='enabled',
        comment="状态：enabled-启用，disabled-停用"
    )
    health_status = Column(
        String(20),
        default='unknown',
        comment="健康状态：unknown-未知，healthy-健康，unhealthy-不健康"
    )
    last_health_check = Column(String(50), nullable=True, comment="最后健康检查时间")

    def __repr__(self) -> str:
        return f"<ModelConfig(id={self.id}, name={self.name}, platform={self.platform})>"

    @property
    def is_enabled(self) -> bool:
        """检查模型是否启用"""
        return self.status == "enabled"

    @property
    def is_healthy(self) -> bool:
        """检查模型是否健康"""
        return self.health_status == "healthy"

    @property
    def is_available(self) -> bool:
        """检查模型是否可用（启用且健康）"""
        return self.is_enabled and self.is_healthy

    def enable(self) -> None:
        """启用模型"""
        self.status = "enabled"

    def disable(self) -> None:
        """停用模型"""
        self.status = "disabled"

    def set_health_status(self, status: str) -> None:
        """设置健康状态"""
        if status in ['unknown', 'healthy', 'unhealthy']:
            self.health_status = status


