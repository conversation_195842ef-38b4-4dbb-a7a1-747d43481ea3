/**
 * 模型管理API接口
 */
import request from '@/utils/http'
import type {
  ModelConfigCreateRequest,
  ModelConfigUpdateRequest,
  ModelConfigResponse,
  ModelConfigListResponse,
  ModelConfigListParams,
  ModelHealthCheckRequest,
  ModelHealthCheckResponse,
  ModelCallRequest,
  ModelCallResponse,
  ModelStatsResponse
} from '@/types/api/model'

// 导入通用分页类型
import type { PaginationData } from '@/types/api/common'

// 重新导出类型以保持向后兼容
export type {
  ModelConfigCreateRequest as ModelConfigCreate,
  ModelConfigUpdateRequest as ModelConfigUpdate,
  ModelConfigResponse,
  ModelConfigListResponse,
  ModelConfigListParams,
  ModelHealthCheckRequest,
  ModelHealthCheckResponse,
  ModelCallRequest,
  ModelCallResponse,
  ModelStatsResponse
}

// 请求参数类型
export interface ModelConfigListRequest {
  current?: number
  size?: number
  name?: string
  platform?: string
  status?: string
  health_status?: string
}

export class ModelConfigService {
  /**
   * 获取模型配置列表
   */
  static async getModelConfigList(params: ModelConfigListRequest = {}): Promise<PaginationData<ModelConfigResponse>> {
    const response = await request.get<PaginationData<ModelConfigResponse>>({
      url: '/api/model/list',
      params
    })
    return response
  }

  /**
   * 获取模型配置详情
   */
  static async getModelConfigDetail(modelId: number): Promise<ModelConfigResponse> {
    const response = await request.get<ModelConfigResponse>({
      url: `/api/model/${modelId}`
    })
    return response
  }

  /**
   * 创建模型配置
   */
  static async createModelConfig(data: ModelConfigCreateRequest): Promise<ModelConfigResponse> {
    const response = await request.post<ModelConfigResponse>({
      url: '/api/model',
      params: data
    })
    return response
  }

  /**
   * 更新模型配置
   */
  static async updateModelConfig(modelId: number, data: ModelConfigUpdateRequest): Promise<ModelConfigResponse> {
    const response = await request.put<ModelConfigResponse>({
      url: `/api/model/${modelId}`,
      params: data
    })
    return response
  }

  /**
   * 删除模型配置
   */
  static async deleteModelConfig(modelId: number): Promise<boolean> {
    const response = await request.del<boolean>({
      url: `/api/model/${modelId}`
    })
    return response
  }

  /**
   * 启用模型
   */
  static async enableModel(modelId: number): Promise<ModelConfigResponse> {
    const response = await request.post<ModelConfigResponse>({
      url: `/api/model/${modelId}/enable`
    })
    return response
  }

  /**
   * 停用模型
   */
  static async disableModel(modelId: number): Promise<ModelConfigResponse> {
    const response = await request.post<ModelConfigResponse>({
      url: `/api/model/${modelId}/disable`
    })
    return response
  }

  /**
   * 获取可用模型列表
   */
  static async getAvailableModels(): Promise<ModelConfigResponse[]> {
    const response = await request.get<ModelConfigResponse[]>({
      url: '/api/model/available/list'
    })
    return response
  }

  /**
   * 单个模型健康检查
   */
  static async healthCheckModel(modelId: number, timeoutSeconds?: number): Promise<ModelHealthCheckResponse> {
    const params: any = {}
    if (timeoutSeconds) {
      params.timeout_seconds = timeoutSeconds
    }
    
    const response = await request.post<ModelHealthCheckResponse>({
      url: `/api/model/${modelId}/health-check`,
      params
    })
    return response
  }

  /**
   * 批量健康检查
   */
  static async batchHealthCheck(modelIds?: number[], timeoutSeconds?: number): Promise<ModelHealthCheckResponse[]> {
    const params: any = {}
    if (modelIds && modelIds.length > 0) {
      params.model_ids = modelIds
    }
    if (timeoutSeconds) {
      params.timeout_seconds = timeoutSeconds
    }
    
    const response = await request.post<ModelHealthCheckResponse[]>({
      url: '/api/model/health-check/batch',
      params
    })
    return response
  }

  /**
   * 调用模型
   */
  static async callModel(data: ModelCallRequest): Promise<ModelCallResponse> {
    const response = await request.post<ModelCallResponse>({
      url: '/api/model/call/stream',
      params: data
    })
    return response
  }

  /**
   * 测试模型配置连接
   */
  static async testModelConnection(data: ModelConfigCreateRequest): Promise<{ success: boolean; message: string; details?: any }> {
    const response = await request.post<{ success: boolean; message: string; details?: any }>({
      url: '/api/model/test',
      params: data
    })
    return response
  }

  /**
   * 获取模型统计信息
   */
  static async getModelStats(): Promise<ModelStatsResponse> {
    const response = await request.get<ModelStatsResponse>({
      url: '/api/model/stats/overview'
    })
    return response
  }

  /**
   * 批量删除模型配置
   */
  static async batchDeleteModelConfigs(modelIds: number[]): Promise<boolean> {
    const promises = modelIds.map(id => this.deleteModelConfig(id))
    await Promise.all(promises)
    return true
  }

  /**
   * 批量启用模型
   */
  static async batchEnableModels(modelIds: number[]): Promise<ModelConfigResponse[]> {
    const promises = modelIds.map(id => this.enableModel(id))
    return Promise.all(promises)
  }

  /**
   * 批量停用模型
   */
  static async batchDisableModels(modelIds: number[]): Promise<ModelConfigResponse[]> {
    const promises = modelIds.map(id => this.disableModel(id))
    return Promise.all(promises)
  }
}

// 导出默认实例
export default ModelConfigService
