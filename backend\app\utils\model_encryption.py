"""
模型API Key加密工具
使用 AES 对称加密安全地存储和解密 API 密钥
"""
from app.utils.encryption import (
    encrypt_api_key as _encrypt_api_key,
    decrypt_api_key as _decrypt_api_key,
    safe_decrypt_api_key as _safe_decrypt_api_key,
    is_api_key_encrypted as _is_api_key_encrypted,
    mask_api_key as _mask_api_key
)
from app.utils.security import get_password_hash, verify_password
from app.utils.logger import setup_logger

logger = setup_logger()


def encrypt_api_key(api_key: str) -> str:
    """
    加密API Key

    Args:
        api_key: 明文API Key

    Returns:
        加密后的API Key
    """
    return _encrypt_api_key(api_key)


def decrypt_api_key(encrypted_api_key: str) -> str:
    """
    解密API Key

    Args:
        encrypted_api_key: 加密的API Key

    Returns:
        解密后的明文API Key
    """
    return _decrypt_api_key(encrypted_api_key)


def safe_decrypt_api_key(encrypted_api_key: str) -> str:
    """
    安全解密API Key

    如果解密失败，返回原文（向后兼容旧的哈希格式）

    Args:
        encrypted_api_key: 加密的API Key

    Returns:
        解密后的明文API Key，如果解密失败则返回原文
    """
    return _safe_decrypt_api_key(encrypted_api_key)


def verify_api_key(plain_api_key: str, encrypted_api_key: str) -> bool:
    """
    验证API Key

    支持新的加密格式和旧的哈希格式（向后兼容）

    Args:
        plain_api_key: 明文API Key
        encrypted_api_key: 加密的API Key

    Returns:
        验证结果
    """
    try:
        # 尝试新的加密格式
        if _is_api_key_encrypted(encrypted_api_key):
            decrypted_key = _decrypt_api_key(encrypted_api_key)
            return plain_api_key == decrypted_key
        else:
            # 回退到旧的哈希格式（向后兼容）
            logger.warning("使用旧的哈希格式验证API Key，建议更新为新的加密格式")
            return verify_password(plain_api_key, encrypted_api_key)
    except Exception as e:
        logger.error(f"API Key验证失败: {str(e)}")
        return False


def is_api_key_encrypted(api_key: str) -> bool:
    """
    检查API Key是否是加密格式

    Args:
        api_key: API Key

    Returns:
        True 如果是加密格式，False 否则
    """
    return _is_api_key_encrypted(api_key)


def mask_api_key(api_key: str) -> str:
    """
    脱敏显示API Key

    Args:
        api_key: API Key（明文或密文）

    Returns:
        脱敏后的API Key
    """
    return _mask_api_key(api_key)
