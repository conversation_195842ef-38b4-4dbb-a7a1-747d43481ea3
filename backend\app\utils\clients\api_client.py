"""
HTTP API连接客户端
支持HTTP/HTTPS API的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse

from .base_client import BaseClient, ConnectionResult

try:
    import aiohttp
    import ssl
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False


class APIClient(BaseClient):
    """
    HTTP API连接客户端
    支持HTTP/HTTPS API的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化API客户端
        
        Args:
            config: API连接配置
                - url: API基础URL (必需)
                - method: 测试请求方法 (默认: GET)
                - headers: 请求头 (可选)
                - auth_type: 认证类型 (none/basic/bearer/api_key)
                - username: 用户名 (basic认证)
                - password: 密码 (basic认证)
                - token: Bearer令牌 (bearer认证)
                - api_key: API密钥 (api_key认证)
                - api_key_header: API密钥头名称 (默认: X-API-Key)
                - verify_ssl: 是否验证SSL (默认: True)
                - timeout: 请求超时时间 (默认: 30秒)
        """
        super().__init__(config)
        
        if not AIOHTTP_AVAILABLE:
            raise ImportError("aiohttp库未安装，请运行: pip install aiohttp")
        
        # 验证必要配置
        if 'url' not in config:
            raise ValueError("url 是必需的")
        
        # 设置默认值
        self.url = config['url'].rstrip('/')
        self.method = config.get('method', 'GET').upper()
        self.headers = config.get('headers', {})
        self.auth_type = config.get('auth_type', 'none')
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.token = config.get('token', '')
        self.api_key = config.get('api_key', '')
        self.api_key_header = config.get('api_key_header', 'X-API-Key')
        self.verify_ssl = config.get('verify_ssl', True)
        self.timeout_seconds = config.get('timeout', 30)
        
        self.session = None
        
        # 解析URL
        parsed_url = urlparse(self.url)
        self.scheme = parsed_url.scheme
        self.host = parsed_url.hostname
        self.port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        self.path = parsed_url.path or '/'

    def _prepare_headers(self) -> Dict[str, str]:
        """
        准备请求头
        
        Returns:
            Dict: 请求头字典
        """
        headers = self.headers.copy()
        
        # 添加认证头
        if self.auth_type == 'basic' and self.username and self.password:
            import base64
            credentials = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
            headers['Authorization'] = f"Basic {credentials}"
        elif self.auth_type == 'bearer' and self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        elif self.auth_type == 'api_key' and self.api_key:
            headers[self.api_key_header] = self.api_key
        
        # 添加默认头
        if 'User-Agent' not in headers:
            headers['User-Agent'] = 'DpTestPlatform-APIClient/1.0'
        
        return headers

    def _create_ssl_context(self) -> Optional[ssl.SSLContext]:
        """
        创建SSL上下文
        
        Returns:
            Optional[ssl.SSLContext]: SSL上下文
        """
        if self.scheme != 'https':
            return None
        
        if not self.verify_ssl:
            # 不验证SSL证书
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            return ssl_context
        
        # 使用默认SSL上下文
        return ssl.create_default_context()

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立API连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 创建SSL上下文
            ssl_context = self._create_ssl_context()
            
            # 创建连接器
            connector = aiohttp.TCPConnector(
                ssl=ssl_context,
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            # 创建会话
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout_config,
                headers=self._prepare_headers()
            )
            
            # 测试连接 - 发送测试请求
            test_url = self.url
            if self.path and self.path != '/':
                test_url = f"{self.url}{self.path}"
            
            async with self.session.request(self.method, test_url) as response:
                status_code = response.status
                content_type = response.headers.get('Content-Type', '')
                server = response.headers.get('Server', '')
                
                # 读取少量响应内容用于分析
                content_preview = ""
                try:
                    content_bytes = await response.read()
                    if len(content_bytes) > 0:
                        content_preview = content_bytes[:200].decode('utf-8', errors='ignore')
                except:
                    pass
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到API {self.url}",
                duration=duration,
                details={
                    "url": self.url,
                    "method": self.method,
                    "status_code": status_code,
                    "content_type": content_type,
                    "server": server,
                    "scheme": self.scheme,
                    "host": self.host,
                    "port": self.port,
                    "content_preview": content_preview[:100] if content_preview else ""
                }
            )
            
        except aiohttp.ClientConnectorError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"API连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "ClientConnectorError"}
            )
        except aiohttp.ClientTimeout as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"API连接超时: {str(e)}",
                duration=duration,
                details={"error_type": "ClientTimeout"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"API连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开API连接"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试API连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        start_time = time.time()
        
        try:
            # 如果没有建立连接，先建立连接
            if not self.session:
                connect_result = await self.connect(timeout)
                if not connect_result.success:
                    return connect_result
            
            # 执行多个测试请求
            test_results = []
            
            # 测试基础URL
            test_url = self.url
            async with self.session.request(self.method, test_url) as response:
                test_results.append({
                    "url": test_url,
                    "status_code": response.status,
                    "response_time": response.headers.get('X-Response-Time', ''),
                    "content_length": response.headers.get('Content-Length', ''),
                    "success": 200 <= response.status < 400
                })
            
            # 如果有健康检查端点，也测试一下
            health_endpoints = ['/health', '/ping', '/status', '/api/health']
            for endpoint in health_endpoints:
                try:
                    health_url = f"{self.url}{endpoint}"
                    async with self.session.request('GET', health_url) as response:
                        if 200 <= response.status < 400:
                            test_results.append({
                                "url": health_url,
                                "status_code": response.status,
                                "endpoint_type": "health_check",
                                "success": True
                            })
                            break
                except:
                    continue
            
            duration = time.time() - start_time
            
            # 判断整体测试是否成功
            overall_success = any(result.get('success', False) for result in test_results)
            
            return ConnectionResult(
                success=overall_success,
                message=f"API连接测试{'成功' if overall_success else '失败'}",
                duration=duration,
                details={
                    "url": self.url,
                    "method": self.method,
                    "auth_type": self.auth_type,
                    "test_results": test_results
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"API连接测试异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def make_request(self, method: str, endpoint: str = "", **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: 端点路径
            **kwargs: 其他请求参数
            
        Returns:
            Dict: 请求结果
        """
        if not self.session:
            raise RuntimeError("API未连接")
        
        try:
            url = f"{self.url}{endpoint}" if endpoint else self.url
            
            async with self.session.request(method, url, **kwargs) as response:
                content = await response.text()
                
                return {
                    "success": True,
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "content": content,
                    "url": str(response.url)
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": f"{self.url}{endpoint}" if endpoint else self.url
            }

    async def get_api_info(self) -> Dict[str, Any]:
        """
        获取API信息
        
        Returns:
            Dict: API信息
        """
        if not self.session:
            raise RuntimeError("API未连接")
        
        try:
            # 尝试获取API文档或信息
            info_endpoints = ['/docs', '/swagger', '/api-docs', '/openapi.json', '/info']
            api_info = {}
            
            for endpoint in info_endpoints:
                try:
                    result = await self.make_request('GET', endpoint)
                    if result.get('success') and 200 <= result.get('status_code', 0) < 400:
                        api_info[endpoint] = {
                            "status_code": result['status_code'],
                            "content_type": result['headers'].get('content-type', ''),
                            "available": True
                        }
                except:
                    continue
            
            return api_info
        except Exception as e:
            raise RuntimeError(f"获取API信息失败: {str(e)}")
