"""
文件上传 API 路由
"""
from typing import Dict, Any
from fastapi import APIRouter, Depends, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pathlib import Path

from app.core.exceptions import BusinessException, exception_handler
from app.api.deps import get_current_active_user
from app.schemas.base import APIResponse
from app.models.user.user import User
from app.utils.file_handler import file_handler
from app.core.responses import response_builder

router = APIRouter()


@router.post("/avatar", response_model=APIResponse[Dict[str, Any]], summary="上传头像")
async def upload_avatar(
    file: UploadFile = File(..., description="头像图片文件"),
    current_user: User = Depends(get_current_active_user)
) -> APIResponse[Dict[str, Any]]:
    """
    上传用户头像
    
    Args:
        file: 上传的头像文件
        current_user: 当前用户
        
    Returns:
        上传结果，包含文件URL
        
    Raises:
        ValidationError: 文件验证失败
        BusinessException: 上传失败
    """
    try:
        # 保存文件
        file_url = await file_handler.save_avatar(file)
        
        # 获取文件信息
        file_info = file_handler.get_file_info(file_url)
        
        response_data = {
            "url": file_url,
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file_info.get("size", 0) if file_info else 0
        }
        
        return response_builder.success(
            data=response_data,
            message="头像上传成功"
        )
        
    except BusinessException as e:
        raise exception_handler.to_http_exception(e)


@router.get("/info", response_model=APIResponse[Dict[str, Any]], summary="获取文件信息")
async def get_file_info(
    file_url: str,
    current_user: User = Depends(get_current_active_user)
) -> APIResponse[Dict[str, Any]]:
    """
    获取文件信息
    
    Args:
        file_url: 文件URL
        current_user: 当前用户
        
    Returns:
        文件信息
    """
    try:
        file_info = file_handler.get_file_info(file_url)
        
        if not file_info:
            raise BusinessException("文件不存在", 404)
        
        return response_builder.success(
            data=file_info,
            message="获取文件信息成功"
        )
        
    except BusinessException as e:
        raise exception_handler.to_http_exception(e)


@router.delete("/delete", response_model=APIResponse[None], summary="删除文件")
async def delete_file(
    file_url: str,
    current_user: User = Depends(get_current_active_user)
) -> APIResponse[None]:
    """
    删除文件
    
    Args:
        file_url: 文件URL
        current_user: 当前用户
        
    Returns:
        删除结果
    """
    try:
        success = await file_handler.delete_file(file_url)
        
        if not success:
            raise BusinessException("文件删除失败或文件不存在", 404)
        
        return response_builder.success(
            data=None,
            message="文件删除成功"
        )
        
    except BusinessException as e:
        raise exception_handler.to_http_exception(e) 