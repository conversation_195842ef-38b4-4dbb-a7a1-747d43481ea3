"""
混沌测试任务业务服务
"""
from typing import Dict, Any, List, Optional, Type
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.chaos.chaos_task_repository import ChaosTaskRepository
from app.repositories.chaos.chaos_execution_repository import ChaosExecutionRepository
from app.repositories.env.env import EnvironmentRepository
from app.repositories.user.user import UserRepository
from app.models.chaos.chaos_task import ChaosTask
from app.models.chaos.chaos_execution import ChaosExecution
from app.schemas.chaos.chaos_task import (
    ChaosTask<PERSON>reate, ChaosTaskUpdate, ChaosTaskResponse, ChaosTaskListResponse,
    ChaosTaskSearchParams, ChaosTaskStatistics, ChaosTaskExecuteRequest
)
from app.schemas.base import PaginationData
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.logger import setup_logger
from .chaosblade_service import ChaosBladeService

logger = setup_logger()


class ChaosTaskService(BaseService[<PERSON>Task, Chaos<PERSON>ask<PERSON><PERSON>, ChaosTaskUpdate, ChaosTaskResponse]):
    """
    混沌测试任务业务服务
    继承BaseService，提供任务管理的核心业务逻辑
    """

    def __init__(self, db: AsyncSession):
        self.repository = ChaosTaskRepository(db)
        self.execution_repository = ChaosExecutionRepository(db)
        self.env_repository = EnvironmentRepository(db)
        self.user_repository = UserRepository(db)
        self.chaosblade_service = ChaosBladeService()
        super().__init__(db, self.repository)

    @property
    def model_class(self) -> Type[ChaosTask]:
        return ChaosTask

    @property
    def response_schema_class(self) -> Type[ChaosTaskResponse]:
        return ChaosTaskResponse

    # ==================== 钩子方法实现 ====================

    async def _validate_before_create(self, create_data: ChaosTaskCreate, **kwargs) -> None:
        """创建前业务验证"""
        # 验证环境是否存在
        for env_id in create_data.env_ids:
            env = await self.env_repository.get(env_id)
            if not env:
                raise_validation_error(f"环境ID {env_id} 不存在")

        # 验证任务名称是否重复
        existing_task = await self.repository.get_by_field("name", create_data.name, unique=True)
        if existing_task:
            raise_validation_error(f"任务名称 '{create_data.name}' 已存在")

        # 验证环境ID列表
        if not create_data.env_ids:
            raise_validation_error("必须指定至少一个目标环境")

        # 验证故障参数
        self._validate_fault_params(create_data.fault_type, create_data.fault_params)

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前数据处理"""
        # 设置默认状态
        create_dict["status"] = "pending"
        
        # 处理定时任务
        if create_dict.get("execution_type") == "scheduled":
            if not create_dict.get("scheduled_time"):
                raise_validation_error("定时执行必须设置执行时间")
        
        return create_dict

    async def _process_after_create(self, obj: ChaosTask, create_data: ChaosTaskCreate) -> None:
        """创建后处理"""
        logger.info(f"混沌测试任务创建成功: {obj.name} (ID: {obj.id})")

    def _convert_to_response(self, obj: ChaosTask) -> ChaosTaskResponse:
        """转换为响应对象"""
        # 获取环境名称列表
        env_names = []
        try:
            if obj.env_ids:
                # 暂时使用环境ID作为占位符，实际应该异步查询环境名称
                env_names = [f"环境{env_id}" for env_id in obj.env_ids]
        except Exception:
            env_names = []

        return ChaosTaskResponse(
            id=obj.id,
            name=obj.name,
            description=obj.description,
            env_ids=obj.env_ids or [],
            fault_type=obj.fault_type,
            fault_params=obj.fault_params or {},
            execution_type=obj.execution_type,
            scheduled_time=obj.scheduled_time,
            periodic_config=obj.periodic_config,
            status=obj.status,
            execution_result=obj.execution_result,
            auto_destroy=obj.auto_destroy,
            max_duration=obj.max_duration,
            created_at=obj.created_at,
            updated_at=obj.updated_at,
            created_by=obj.created_by,
            updated_by=obj.updated_by,
            environment_names=env_names,
            env_count=len(obj.env_ids) if obj.env_ids else 0,
            can_execute=obj.can_execute,
            can_pause=obj.can_pause,
            can_terminate=obj.can_terminate
        )

    async def _convert_to_response_with_env_names(self, obj: ChaosTask) -> ChaosTaskResponse:
        """转换为响应对象（包含真实环境名称和用户昵称）"""
        # 获取环境名称列表
        env_names = []
        try:
            if obj.env_ids:
                environments = await self.env_repository.get_environments_by_ids(obj.env_ids)
                env_names = [env.name for env in environments]
                # 如果某些环境ID没有找到对应的环境，用占位符填充
                if len(env_names) < len(obj.env_ids):
                    found_ids = {env.id for env in environments}
                    for env_id in obj.env_ids:
                        if env_id not in found_ids:
                            env_names.append(f"环境{env_id}")
        except Exception:
            env_names = [f"环境{env_id}" for env_id in (obj.env_ids or [])]

        # 获取创建者和更新者昵称
        created_by_name = obj.created_by
        updated_by_name = obj.updated_by

        try:
            if obj.created_by and obj.created_by.isdigit():
                creator = await self.user_repository.get(int(obj.created_by))
                if creator:
                    created_by_name = creator.nickname or creator.username
        except Exception:
            pass

        try:
            if obj.updated_by and obj.updated_by.isdigit():
                updater = await self.user_repository.get(int(obj.updated_by))
                if updater:
                    updated_by_name = updater.nickname or updater.username
        except Exception:
            pass

        return ChaosTaskResponse(
            id=obj.id,
            name=obj.name,
            description=obj.description,
            env_ids=obj.env_ids or [],
            fault_type=obj.fault_type,
            fault_params=obj.fault_params or {},
            execution_type=obj.execution_type,
            scheduled_time=obj.scheduled_time,
            periodic_config=obj.periodic_config,
            status=obj.status,
            execution_result=obj.execution_result,
            auto_destroy=obj.auto_destroy,
            max_duration=obj.max_duration,
            created_at=obj.created_at,
            updated_at=obj.updated_at,
            created_by=created_by_name,
            updated_by=updated_by_name,
            environment_names=env_names,
            env_count=len(obj.env_ids) if obj.env_ids else 0,
            can_execute=obj.can_execute,
            can_pause=obj.can_pause,
            can_terminate=obj.can_terminate
        )

    # ==================== 业务方法 ====================

    async def create_task(self, task_data: ChaosTaskCreate, current_user_id: int) -> ChaosTaskResponse:
        """创建混沌测试任务"""
        return await self.create(task_data, str(current_user_id))

    async def get_task_by_id(self, task_id: int) -> ChaosTaskResponse:
        """获取任务详情"""
        task = await self.repository.get_with_environment(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")
        return await self._convert_to_response_with_env_names(task)

    async def search_tasks(self, params: ChaosTaskSearchParams) -> PaginationData[ChaosTaskListResponse]:
        """搜索任务"""
        skip = (params.page - 1) * params.size
        
        tasks, total = await self.repository.search_tasks(
            skip=skip,
            limit=params.size,
            keyword=params.keyword,
            env_ids=params.env_ids,
            fault_type=params.fault_type,
            status=params.status,
            execution_type=params.execution_type,
            created_by=params.created_by,
            order_by=params.order_by,
            desc=params.desc
        )

        # 批量获取所有环境信息
        all_env_ids = set()
        all_user_ids = set()
        for task in tasks:
            if task.env_ids:
                all_env_ids.update(task.env_ids)
            if task.created_by and task.created_by.isdigit():
                all_user_ids.add(int(task.created_by))

        env_map = {}
        if all_env_ids:
            try:
                environments = await self.env_repository.get_environments_by_ids(list(all_env_ids))
                env_map = {env.id: env.name for env in environments}
            except Exception:
                pass

        # 批量获取用户信息
        user_map = {}
        if all_user_ids:
            try:
                for user_id in all_user_ids:
                    user = await self.user_repository.get(user_id)
                    if user:
                        user_map[user_id] = user.nickname or user.username
            except Exception:
                pass

        # 转换为列表响应格式
        task_list = []
        for task in tasks:
            env_names = []
            try:
                if task.env_ids:
                    env_names = [env_map.get(env_id, f"环境{env_id}") for env_id in task.env_ids]
            except Exception:
                env_names = []

            # 获取创建者昵称
            created_by_name = task.created_by
            if task.created_by and task.created_by.isdigit():
                created_by_name = user_map.get(int(task.created_by), task.created_by)

            task_list.append(ChaosTaskListResponse(
                id=task.id,
                name=task.name,
                fault_type=task.fault_type,
                status=task.status,
                env_ids=task.env_ids or [],
                environment_names=env_names,
                env_count=len(task.env_ids) if task.env_ids else 0,
                execution_type=task.execution_type,
                created_at=task.created_at,
                created_by=created_by_name,
                can_execute=task.can_execute,
                can_pause=task.can_pause,
                can_terminate=task.can_terminate
            ))

        return PaginationData(
            records=task_list,
            total=total,
            current=params.page,
            size=params.size
        )

    async def execute_task(self, task_id: int, request: ChaosTaskExecuteRequest, current_user_id: int) -> Dict[str, Any]:
        """执行混沌测试任务"""
        # 获取任务
        task = await self.repository.get_with_environment(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")

        # 检查任务状态
        if not task.can_execute and not request.force:
            raise_validation_error(f"任务状态为 {task.status}，无法执行")

        # 如果是重新执行（非pending状态），先清理之前的执行记录
        if task.status != "pending":
            logger.info(f"重新执行任务 {task_id}，清理之前的执行记录")
            # 可选：清理之前的执行记录或标记为历史记录
            # 这里我们保留历史记录，只是重新开始执行

        # 更新任务状态为运行中
        await self.repository.update_status(task_id, "running")

        try:
            # 获取所有环境的主机信息
            execution_results = []
            all_hosts = []

            # 从环境管理获取主机列表
            from app.services.env.env import EnvironmentService
            env_service = EnvironmentService(self.db)

            for env_id in task.env_ids:
                # 获取环境信息
                environment = await env_service.get_environment(env_id)
                if environment:
                    # 将环境ID作为主机ID使用（简化处理）
                    all_hosts.append({
                        "env_id": env_id,
                        "host_id": env_id,  # 使用环境ID作为主机ID
                        "host_info": {
                            "host": environment.host,
                            "port": environment.port or 22,
                            "username": environment.config.get("username", "root") if environment.config else "root",
                            "password": environment.config.get("password") if environment.config else None,
                            "private_key_path": environment.config.get("private_key_path") if environment.config else None
                        }
                    })

            # 为每个主机创建执行记录并执行
            for host_info in all_hosts:
                # 创建执行记录
                execution = ChaosExecution(
                    task_id=task_id,
                    host_id=host_info["host_id"],
                    host_info=host_info["host_info"],
                    status="pending",
                    fault_config=task.fault_params,
                    monitor_config=request.monitor_config,
                    created_by=str(current_user_id)
                )
                self.execution_repository.db.add(execution)
                await self.execution_repository.db.commit()
                await self.execution_repository.db.refresh(execution)

                # 启动监控（如果配置了）
                if request.monitor_config and request.monitor_config.get('enable_monitoring'):
                    try:
                        from app.services.chaos.chaos_monitor_service import ChaosMonitorService
                        from app.schemas.chaos.chaos_monitor import ChaosMonitorConfig

                        monitor_service = ChaosMonitorService(self.db)
                        monitor_config = ChaosMonitorConfig(**request.monitor_config)

                        await monitor_service.start_monitoring(
                            execution.id,
                            host_info["host_id"],
                            host_info["host_info"],
                            monitor_config
                        )
                        logger.info(f"监控已启动: execution_id={execution.id}, host_id={host_info['host_id']}")
                    except Exception as e:
                        logger.error(f"启动监控失败: {e}")
                        # 监控启动失败不影响任务执行

                # 执行故障注入
                result = await self._execute_fault_on_host(task, host_info["host_info"], execution.id)
                execution_results.append(result)

            # 更新任务执行结果
            task_result = {
                "total_hosts": len(all_hosts),
                "successful_hosts": sum(1 for r in execution_results if r["success"]),
                "failed_hosts": sum(1 for r in execution_results if not r["success"]),
                "execution_time": datetime.now().isoformat()
            }

            # 检查是否有timeout参数，决定任务状态
            has_timeout = task.fault_params and task.fault_params.get("timeout")
            timeout_seconds = None
            if has_timeout:
                try:
                    timeout_seconds = int(task.fault_params.get("timeout", 0))
                except (ValueError, TypeError):
                    timeout_seconds = None

            # 根据执行结果更新任务状态
            if all(r["success"] for r in execution_results):
                if timeout_seconds and timeout_seconds > 0:
                    # 有timeout的任务，执行成功后状态为running，等待自动完成
                    final_status = "running"
                    logger.info(f"任务 {task_id} 执行成功，有timeout参数({timeout_seconds}秒)，状态设为running")

                    # 启动后台任务监控timeout完成
                    await self._schedule_task_completion(task_id, timeout_seconds)
                else:
                    # 没有timeout的任务，执行成功后直接完成
                    final_status = "completed"
                    logger.info(f"任务 {task_id} 执行成功，无timeout参数，状态设为completed")
            elif any(r["success"] for r in execution_results):
                # 部分成功，状态为running
                final_status = "running"
                if timeout_seconds and timeout_seconds > 0:
                    await self._schedule_task_completion(task_id, timeout_seconds)
            else:
                # 全部失败
                final_status = "failed"

            await self.repository.update_status(task_id, final_status, task_result)

            return {
                "success": True,
                "message": "任务执行完成",
                "task_id": task_id,
                "execution_results": execution_results,
                "summary": task_result
            }

        except Exception as e:
            logger.error(f"执行任务失败: {str(e)}")
            await self.repository.update_status(task_id, "failed", {"error": str(e)})
            raise_validation_error(f"任务执行失败: {str(e)}")

    async def pause_task(self, task_id: int, current_user_id: int) -> bool:
        """暂停任务执行"""
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")

        if not task.can_pause:
            raise_validation_error(f"任务状态为 {task.status}，无法暂停")

        # 更新任务状态
        await self.repository.update_status(task_id, "paused")
        logger.info(f"任务 {task_id} 已暂停，操作者: {current_user_id}")
        return True

    async def terminate_task(self, task_id: int, current_user_id: int) -> bool:
        """终止任务执行 - 前端未使用，已注释"""
        task = await self.repository.get_with_executions(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")

        if not task.can_terminate:
            raise_validation_error(f"任务状态为 {task.status}，无法终止")

        try:
            # 销毁所有正在运行的故障注入
            for execution in task.executions:
                if execution.is_running and execution.chaos_uid:
                    await self._destroy_fault_execution(execution)

            # 更新任务状态
            await self.repository.update_status(task_id, "cancelled")
            logger.info(f"任务 {task_id} 已终止，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"终止任务失败: {str(e)}")
            raise_validation_error(f"终止任务失败: {str(e)}")

    async def reset_task(self, task_id: int, current_user_id: int) -> bool:
        """重置任务状态，允许重新执行 - 前端未使用，已注释"""
        task = await self.repository.get(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")

        if task.status == "running":
            raise_validation_error("正在运行的任务无法重置，请先终止任务")

        try:
            # 重置任务状态为pending
            await self.repository.update_status(task_id, "pending", {"reset_time": datetime.now().isoformat()})
            logger.info(f"任务 {task_id} 已重置，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"重置任务失败: {str(e)}")
            raise_validation_error(f"重置任务失败: {str(e)}")

    async def get_task_statistics(self) -> ChaosTaskStatistics:
        """获取任务统计信息 - 前端未使用，已注释"""
        stats = await self.repository.get_statistics()

        # 获取最近任务
        recent_tasks, _ = await self.repository.search_tasks(
            skip=0, limit=10, order_by="created_at", desc=True
        )

        recent_task_list = []
        for task in recent_tasks:
            recent_task_list.append(ChaosTaskListResponse(
                id=task.id,
                name=task.name,
                fault_type=task.fault_type,
                status=task.status,
                env_id=task.env_id,
                environment_name=getattr(task.environment, 'name', None) if hasattr(task, 'environment') else None,
                host_count=len(task.host_ids) if task.host_ids else 0,
                execution_type=task.execution_type,
                created_at=task.created_at,
                created_by=task.created_by,
                can_execute=task.can_execute,
                can_pause=task.can_pause,
                can_terminate=task.can_terminate
            ))

        return ChaosTaskStatistics(
            total_count=stats["total_count"],
            status_stats=stats["status_stats"],
            fault_type_stats=stats["fault_type_stats"],
            recent_tasks=recent_task_list
        )

    # ==================== 私有方法 ====================

    def _validate_fault_params(self, fault_type: str, fault_params: Dict[str, Any]) -> None:
        """验证故障参数"""
        if not fault_params:
            raise_validation_error("故障参数不能为空")

        # 根据故障类型验证必需参数
        if fault_type == "cpu":
            if "cpu-percent" not in fault_params:
                raise_validation_error("CPU故障必须指定cpu-percent参数")
        elif fault_type == "memory":
            if "mem-percent" not in fault_params:
                raise_validation_error("内存故障必须指定mem-percent参数")
        # elif fault_type == "network":
        #     if "action" not in fault_params:
        #         raise_validation_error("网络故障必须指定action参数")

    async def _execute_fault_on_host(self, task: ChaosTask, host_info: Dict[str, Any], execution_id: int) -> Dict[str, Any]:
        """在指定主机上执行故障注入"""
        try:
            # 构建故障配置
            fault_config = {
                "fault_type": task.fault_type,
                **task.fault_params
            }

            # 执行故障注入
            result = await self.chaosblade_service.execute_fault(host_info, fault_config)
            
            # 更新执行记录
            if result["success"]:
                # 更新执行状态和结果
                await self.execution_repository.update_execution_status(
                    execution_id, "success",
                    output=result.get("output"),
                    exit_code=result.get("exit_code", 0)
                )

                # 更新chaos_uid和命令信息
                execution = await self.execution_repository.get(execution_id)
                if execution:
                    if result.get("chaos_uid"):
                        execution.chaos_uid = result["chaos_uid"]
                        logger.info(f"保存故障ID: {result['chaos_uid']} 到执行记录 {execution_id}")
                    if result.get("command"):
                        execution.command = result["command"]
                    # 保存故障配置快照
                    execution.fault_config = fault_config
                    await self.execution_repository.db.commit()
                    await self.execution_repository.db.refresh(execution)
            else:
                await self.execution_repository.update_execution_status(
                    execution_id, "failed",
                    error_message=result.get("error"),
                    exit_code=result.get("exit_code", 1)
                )

            return {
                "host": host_info.get("host"),
                "execution_id": execution_id,
                "success": result["success"],
                "chaos_uid": result.get("chaos_uid"),
                "message": result.get("output") or result.get("error")
            }

        except Exception as e:
            logger.error(f"主机 {host_info.get('host')} 故障注入失败: {str(e)}")
            await self.execution_repository.update_execution_status(
                execution_id, "failed", error_message=str(e)
            )
            return {
                "host": host_info.get("host"),
                "execution_id": execution_id,
                "success": False,
                "error": str(e)
            }

    async def _destroy_fault_execution(self, execution: ChaosExecution) -> None:
        """销毁故障注入执行"""
        try:
            host_info = await self._get_host_info_by_execution(execution)
            result = await self.chaosblade_service.destroy_fault(host_info, execution.chaos_uid)
            
            if result["success"]:
                await self.execution_repository.mark_as_destroyed(
                    execution.id, result.get("output")
                )
            else:
                logger.error(f"销毁故障注入失败: {result.get('error')}")

        except Exception as e:
            logger.error(f"销毁执行记录 {execution.id} 失败: {str(e)}")

    async def _get_host_info_by_execution(self, execution: ChaosExecution) -> Dict[str, Any]:
        """根据执行记录获取主机信息"""
        # 从执行记录的host_info字段获取主机信息
        if execution.host_info:
            return execution.host_info

        # 如果没有保存主机信息，从环境管理获取
        from app.services.env.env import EnvironmentService
        env_service = EnvironmentService(self.db)
        environment = await env_service.get_environment(execution.host_id)  # 使用host_id作为env_id

        if environment:
            return {
                "host": environment.host,
                "port": environment.port or 22,
                "username": environment.config.get("username", "root") if environment.config else "root",
                "password": environment.config.get("password") if environment.config else None,
                "private_key_path": environment.config.get("private_key_path") if environment.config else None
            }

        # 默认返回空字典
        return {}

    async def _schedule_task_completion(self, task_id: int, timeout_seconds: int) -> None:
        """安排任务在timeout后自动完成"""
        import asyncio

        async def complete_task_after_timeout():
            try:
                # 等待timeout时间
                await asyncio.sleep(timeout_seconds)

                # 检查任务是否仍在运行
                task = await self.repository.get(task_id)
                if task and task.status == "running":
                    # 更新任务状态为completed
                    completion_result = {
                        "completed_at": datetime.now().isoformat(),
                        "completion_reason": f"Timeout reached ({timeout_seconds}s)",
                        "auto_completed": True
                    }
                    await self.repository.update_status(task_id, "completed", completion_result)
                    logger.info(f"任务 {task_id} 在timeout({timeout_seconds}秒)后自动完成")

            except Exception as e:
                logger.error(f"任务 {task_id} 自动完成失败: {str(e)}")

        # 启动后台任务
        asyncio.create_task(complete_task_after_timeout())
