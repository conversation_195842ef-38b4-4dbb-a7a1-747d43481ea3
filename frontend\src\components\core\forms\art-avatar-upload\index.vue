<template>
  <div class="art-avatar-upload">
    <ElUpload
      ref="uploadRef"
      :auto-upload="false"
      :show-file-list="false"
      :before-upload="beforeUpload"
      @change="handleChange"
      accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
      class="avatar-uploader"
    >
      <div class="avatar-container" :class="{ 'uploading': uploading }">
        <!-- 头像显示 -->
        <img 
          v-if="avatarUrl && !uploading" 
          :src="getFullImageUrl(avatarUrl)" 
          class="avatar" 
          alt="头像"
        />
        
        <!-- 上传中状态 -->
        <div v-else-if="uploading" class="upload-loading">
          <ElIcon class="loading-icon">
            <Loading />
          </ElIcon>
          <div class="upload-progress">{{ uploadProgress }}%</div>
        </div>
        
        <!-- 默认上传区域 -->
        <div v-else class="upload-placeholder">
          <ElIcon class="upload-icon">
            <Plus />
          </ElIcon>
          <div class="upload-text">点击上传头像</div>
        </div>
        
        <!-- 悬停遮罩 -->
        <div class="upload-overlay" v-show="!uploading">
          <ElIcon class="overlay-icon">
            <Camera />
          </ElIcon>
          <div class="overlay-text">更换头像</div>
        </div>
      </div>
    </ElUpload>
    
    <!-- 上传提示 -->
    <div class="upload-tips">
      <p>支持 JPG、PNG、GIF、WEBP 格式</p>
      <p>文件大小不超过 5MB</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, defineProps } from 'vue'
import { ElUpload, ElIcon, ElMessage, type UploadFile, type UploadInstance } from 'element-plus'
import { Plus, Camera, Loading } from '@element-plus/icons-vue'
import { UploadService } from '@/api/uploadApi'
import { UserService } from '@/api/usersApi'
import { getAvatarUrl } from '@/utils/ui/avatar'

defineOptions({ name: 'ArtAvatarUpload' })

// Props 定义
interface Props {
  /** 当前头像URL */
  modelValue?: string
  /** 上传尺寸限制 */
  size?: number
  /** 是否圆形头像 */
  round?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 120,
  round: true
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'success': [url: string]
  'error': [error: string]
}>()

// 响应式数据
const uploadRef = ref<UploadInstance>()
const uploading = ref(false)
const uploadProgress = ref(0)

// 计算属性
const avatarUrl = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})

// 获取完整图片URL
const getFullImageUrl = (url: string) => {
  return getAvatarUrl(url)
}

// 文件上传前验证
const beforeUpload = (file: File) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片格式的文件!')
    return false
  }

  // 检查文件大小 (5MB)
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }

  return true
}

// 处理文件选择
const handleChange = async (uploadFile: UploadFile) => {
  if (!uploadFile.raw) return

  // 验证文件
  if (!beforeUpload(uploadFile.raw)) {
    return
  }

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 100)

    // 上传文件
    const uploadResult = await UploadService.uploadAvatar(uploadFile.raw)
    
    // HTTP工具返回的是data部分，包含url等信息
    const fileUrl = uploadResult.url
    
    // 更新用户头像
    await UserService.updateAvatar(fileUrl)
    
    // 更新进度到100%
    uploadProgress.value = 100
    
    // 清除进度定时器
    clearInterval(progressInterval)
    
    // 延迟一下再完成，让用户看到100%
    setTimeout(() => {
      avatarUrl.value = fileUrl
      uploading.value = false
      uploadProgress.value = 0
      
      ElMessage.success('头像上传成功!')
      emit('success', fileUrl)
    }, 500)
    
  } catch (error: any) {
    uploading.value = false
    uploadProgress.value = 0
    
    const errorMessage = error.response?.data?.message || error.message || '头像上传失败'
    ElMessage.error(errorMessage)
    emit('error', errorMessage)
  }
}

// 暴露方法
defineExpose({
  /** 清除上传状态 */
  clearFiles: () => uploadRef.value?.clearFiles(),
  /** 手动触发上传 */
  triggerUpload: () => uploadRef.value?.$el.querySelector('input')?.click()
})
</script>

<style lang="scss" scoped>
.art-avatar-upload {
  display: inline-block;

  .avatar-uploader {
    :deep(.el-upload) {
      border: 2px dashed var(--art-border-color);
      border-radius: v-bind('props.round ? "50%" : "8px"');
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      width: v-bind('props.size + "px"');
      height: v-bind('props.size + "px"');

      &:hover {
        border-color: var(--el-color-primary);
        
        .upload-overlay {
          opacity: 1;
        }
      }
    }
  }

  .avatar-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.uploading {
      :deep(.el-upload) {
        border-color: var(--el-color-primary);
      }
    }

    .avatar {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: v-bind('props.round ? "50%" : "6px"');
    }

    .upload-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: var(--el-color-primary);

      .loading-icon {
        font-size: 24px;
        animation: loading-rotate 1s linear infinite;
      }

      .upload-progress {
        font-size: 12px;
        font-weight: 500;
      }
    }

    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: var(--art-text-gray-400);

      .upload-icon {
        font-size: 24px;
      }

      .upload-text {
        font-size: 12px;
      }
    }

    .upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: v-bind('props.round ? "50%" : "6px"');

      .overlay-icon {
        font-size: 20px;
      }

      .overlay-text {
        font-size: 12px;
      }
    }
  }

  .upload-tips {
    margin-top: 8px;
    text-align: center;

    p {
      margin: 2px 0;
      font-size: 12px;
      color: var(--art-text-gray-400);
    }
  }
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 