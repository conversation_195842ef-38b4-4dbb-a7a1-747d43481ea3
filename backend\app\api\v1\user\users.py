"""
用户管理API路由
"""
from fastapi import APIRouter, Depends, Path, Query
from typing import List

from app.api.deps import get_current_user, get_current_superuser
from app.core.dependencies import get_pagination_params
from app.core.dependencies import UserServiceType
from app.core.responses import response_builder
from app.schemas.base import APIResponse, PaginationResponse
from app.schemas.user.user import UserCreate, UserUpdate, UserResponse, UserProfileUpdate, UserStatusUpdate, UserListItem
from app.schemas.common import PaginationParams
from app.models.user.user import User

router = APIRouter()


@router.get("/info", response_model=APIResponse[UserResponse], summary="获取当前用户信息")
async def get_user_info(
    service: UserServiceType,
    current_user: User = Depends(get_current_user)
):
    """获取当前用户信息"""
    user_info = await service.get_current_user_info(current_user)
    return response_builder.success(data=user_info, message="获取用户信息成功")


@router.get("/list", response_model=PaginationResponse[UserListItem], summary="获取用户列表")
async def get_user_list(
    service: UserServiceType,
    name: str = Query(None, description="用户名搜索关键词"),
    status: str = Query(None, description="用户状态"),
    pagination: PaginationParams = Depends(get_pagination_params),
    current_user: User = Depends(get_current_superuser)
):
    """获取用户列表"""
    # 构建搜索参数
    from app.schemas.common import SearchParams
    search_params = SearchParams(
        current=pagination.current,
        size=pagination.size,
        keyword=name
    )
    
    users, total = await service.get_user_list(search_params, status_filter=status)
    
    return response_builder.paginated(
        records=users,
        total=total,
        current=pagination.current,
        size=pagination.size,
        message="获取用户列表成功"
    )


@router.get("/{user_id}", response_model=APIResponse[UserResponse], summary="获取用户详情")
async def get_user_by_id(
    service: UserServiceType,
    user_id: int = Path(..., description="用户ID"),
    current_user: User = Depends(get_current_superuser)
):
    """获取用户详情"""
    user_info = await service.get_user_by_id(user_id)
    return response_builder.success(data=user_info, message="获取用户详情成功")


@router.post("/add", response_model=APIResponse[UserResponse], summary="创建用户")
async def create_user(
    user_data: UserCreate,
    service: UserServiceType,
    current_user: User = Depends(get_current_superuser)
):
    """创建用户"""
    new_user = await service.create_user(user_data, current_user.id)
    return response_builder.success(data=new_user, message="创建用户成功")


@router.put("/profile/{user_id}", response_model=APIResponse[UserResponse], summary="更新用户资料")
async def update_user_profile(
    service: UserServiceType,
    profile_data: UserProfileUpdate,
    user_id: int = Path(..., description="用户ID"),
    current_user: User = Depends(get_current_user)
):
    """更新用户资料"""
    updated_user = await service.update_user_profile(user_id, profile_data, current_user.id)
    return response_builder.success(data=updated_user, message="更新用户资料成功")


@router.put("/{user_id}", response_model=APIResponse[UserResponse], summary="更新用户信息")
async def update_user(
    service: UserServiceType,
    user_data: UserUpdate,
    user_id: int = Path(..., description="用户ID"),
    current_user: User = Depends(get_current_superuser)
):
    """更新用户信息"""
    updated_user = await service.update_user(user_id, user_data, current_user.id)
    return response_builder.success(data=updated_user, message="更新用户信息成功")


@router.patch("/{user_id}/status", response_model=APIResponse[UserResponse], summary="更新用户状态")
async def update_user_status(
    service: UserServiceType,
    status_data: UserStatusUpdate,
    user_id: int = Path(..., description="用户ID"),
    current_user: User = Depends(get_current_superuser)
):
    """更新用户状态"""
    updated_user = await service.update_user_status(user_id, status_data, current_user.id)
    return response_builder.success(data=updated_user, message="更新用户状态成功")


@router.delete("/{user_id}", response_model=APIResponse[bool], summary="删除用户")
async def delete_user(
    service: UserServiceType,
    user_id: int = Path(..., description="用户ID"),
    current_user: User = Depends(get_current_superuser)
):
    """删除用户"""
    await service.delete_user(user_id, current_user.id)
    return response_builder.success(data=True, message="删除用户成功") 