"""添加监控数据表

Revision ID: add_monitor_data_table
Revises: 
Create Date: 2025-01-29 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_monitor_data_table'
down_revision = '3d51e20f883f'  # 基于最新的迁移
branch_labels = None
depends_on = None


def upgrade():
    """创建监控数据表"""
    # 创建监控数据表
    op.create_table(
        'chaos_monitor_data',
        sa.Column('id', sa.BigInteger(), nullable=False, comment='主键ID'),
        sa.<PERSON>umn('execution_id', sa.Integer(), nullable=False, comment='执行记录ID'),
        sa.<PERSON>umn('host_id', sa.Integer(), nullable=False, comment='主机ID'),
        sa.Column('metric_type', sa.String(20), nullable=False, comment='指标类型: cpu, memory, network, disk'),
        sa.Column('metric_value', sa.DECIMAL(8, 2), nullable=False, comment='指标值'),
        sa.<PERSON>umn('collected_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), comment='采集时间'),
        
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_execution_host_time', 'execution_id', 'host_id', 'collected_at'),
        sa.Index('idx_cleanup_time', 'collected_at'),
        
        comment='监控数据表',
        mysql_engine='InnoDB',
        mysql_charset='utf8mb4'
    )
    
    # 为chaos_executions表添加监控配置字段
    op.add_column(
        'chaos_executions',
        sa.Column('monitor_config', sa.JSON(), nullable=True, comment='监控配置')
    )


def downgrade():
    """删除监控数据表"""
    # 删除监控配置字段
    op.drop_column('chaos_executions', 'monitor_config')
    
    # 删除监控数据表
    op.drop_table('chaos_monitor_data')
