---
alwaysApply: true
---
---
type: "always_apply"
---

# DpTestPlatform 前端开发规范 - 综合版

## 项目概述

**项目名称**: DpTestPlatform Frontend  
**技术栈**: Vue 3 + TypeScript + Vite + Element Plus + Pinia  
**开发模式**: 企业级中后台管理系统  
**构建工具**: Vite 6.1.0  

## 1. 技术架构概览

### 1.1 核心技术栈

#### 前端框架
- **Vue 3.5.12**: 采用 Composition API，支持 `<script setup>` 语法
- **TypeScript**: 强类型语言支持，提供更好的开发体验
- **Vite**: 现代化构建工具，快速的热更新和构建

#### UI 组件库
- **Element Plus 2.10.2**: 企业级 UI 组件库
- **@element-plus/icons-vue**: Element Plus 图标库
- **自定义组件系统**: 基于业务需求的组件封装

#### 状态管理
- **Pinia 3.0.2**: Vue 3 官方推荐的状态管理库
- **pinia-plugin-persistedstate**: 状态持久化插件

#### 路由管理
- **Vue Router 4.4.2**: 官方路由管理器
- **动态路由**: 支持基于权限的路由配置
- **路由守卫**: 完整的路由拦截机制

### 1.2 项目目录结构

```
frontend/
├── src/
│   ├── api/                  # API 接口定义
│   │   ├── usersApi.ts       # 用户相关接口
│   │   ├── envApi.ts         # 环境相关接口
│   │   └── modules/          # API 模块化目录
│   ├── assets/               # 静态资源
│   │   ├── styles/          # 样式文件
│   │   │   ├── variables.scss    # CSS 变量定义
│   │   │   ├── el-light.scss     # Element Plus 亮色主题
│   │   │   ├── el-dark.scss      # Element Plus 暗色主题
│   │   │   ├── dark.scss         # 系统暗色主题
│   │   │   └── theme-animation.scss # 主题切换动画
│   │   └── icons/           # 图标资源
│   ├── components/          # 组件库
│   │   ├── core/            # 核心组件
│   │   │   ├── forms/       # 表单组件
│   │   │   │   ├── art-search-bar/    # 搜索栏组件
│   │   │   │   ├── art-table/         # 表格组件
│   │   │   │   └── art-table-header/  # 表格头部组件
│   │   │   └── base/        # 基础组件
│   │   └── business/        # 业务组件
│   ├── composables/         # 组合式函数
│   │   ├── useTable.ts      # 表格状态管理
│   │   ├── useTheme.ts      # 主题管理
│   │   └── useLoading.ts    # 加载状态管理
│   ├── router/              # 路由配置
│   │   ├── index.ts         # 路由主文件
│   │   ├── routes/          # 路由定义
│   │   │   ├── staticRoutes.ts  # 静态路由
│   │   │   └── asyncRoutes.ts   # 动态路由
│   │   └── guards/          # 路由守卫
│   ├── store/               # 状态管理
│   │   ├── core/            # 核心状态
│   │   │   ├── user.ts      # 用户状态
│   │   │   └── setting.ts   # 设置状态
│   │   └── business/        # 业务状态
│   │       ├── environment/ # 环境管理状态
│   │       └── chaos/       # 混沌测试状态
│   ├── types/               # 类型定义
│   │   ├── api/             # API 类型
│   │   ├── component/       # 组件类型
│   │   └── store/           # 状态类型
│   ├── utils/               # 工具函数
│   │   ├── http/            # HTTP 请求工具
│   │   ├── theme/           # 主题工具
│   │   └── ui/              # UI 工具
│   └── views/               # 页面组件
│       ├── system/          # 系统管理
│       │   └── user/        # 用户管理
│       │       ├── index.vue           # 主页面
│       │       └── modules/            # 子组件
│       │           ├── user-search.vue # 搜索组件
│       │           └── user-dialog.vue # 对话框组件
│       └── config/          # 配置管理
│           └── environment/ # 环境管理
│               ├── index.vue           # 主页面
│               └── modules/            # 子组件
│                   ├── environment-search.vue # 搜索组件
│                   └── environment-dialog.vue # 对话框组件
├── public/                  # 公共静态资源
├── index.html              # HTML 模板
├── vite.config.ts          # Vite 配置
└── package.json            # 项目依赖
```

## 2. 页面开发标准模板

### 2.1 基础列表页面模板（基于用户管理）

#### 主页面结构 (`index.vue`)

```vue
<template>
  <div class="page-container art-full-height">
    <!-- 搜索栏 -->
    <PageSearch 
      v-model:filter="defaultFilter" 
      @reset="resetSearch" 
      @search="handleSearch" 
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton @click="showDialog('add')" type="primary">新增</ElButton>
          <!-- 批量操作按钮（可选） -->
          <ElButton
            v-if="selectedRows.length > 0"
            @click="batchOperation"
            :loading="isLoading"
          >
            批量操作
          </ElButton>
        </template>
        <template #right>
          <!-- 额外操作按钮（可选） -->
          <ElButton @click="showStatsDialog" type="success">统计信息</ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        @selection-change="handleSelectionChange"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      />

      <!-- 对话框组件 -->
      <PageDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :data="currentData"
        @submit="handleDialogSubmit"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox, ElTag, ElButton } from 'element-plus'
import { useTable } from '@/composables/useTable'
import { PageService } from '@/api/pageApi'
import PageSearch from './modules/page-search.vue'
import PageDialog from './modules/page-dialog.vue'

defineOptions({ name: 'PageManagement' })

// 类型定义
type PageListItem = Api.Page.PageListItem
const { width } = useWindowSize()

// 弹窗相关
const dialogType = ref<Form.DialogType>('add')
const dialogVisible = ref(false)
const currentData = ref<Partial<PageListItem>>({})

// 选中行
const selectedRows = ref<PageListItem[]>([])

// 表单搜索初始值
const defaultFilter = ref({
  keyword: undefined,
  status: undefined,
  // 其他搜索字段...
})

// API适配函数
const getPageListForTable = async (params: any) => {
  const response = await PageService.getPageList(params)
  return {
    records: response.records,
    total: response.total,
    current: response.current,
    size: response.size
  }
}

// 使用 useTable Hook
const {
  columns,
  columnChecks,
  tableData,
  isLoading,
  paginationState,
  searchData,
  searchState,
  resetSearch,
  onPageSizeChange,
  onCurrentPageChange,
  refreshAll
} = useTable<PageListItem>({
  // 核心配置
  core: {
    apiFn: getPageListForTable,
    apiParams: {
      current: 1,
      size: 20,
      ...defaultFilter.value
    }
  },
  // 列配置
  columns: {
    list: [
      {
        type: 'selection',
        width: 55,
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '名称',
        minWidth: 150,
        sortable: true
      },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        formatter: (row) => {
          const statusConfig = getStatusConfig(row.status)
          return h(ElTag, { type: statusConfig.type }, () => statusConfig.text)
        }
      },
      {
        prop: 'created_at',
        label: '创建时间',
        width: 180,
        sortable: true
      },
      {
        prop: 'operation',
        label: '操作',
        width: 120,
        fixed: 'right',
        formatter: (row) =>
          h('div', [
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => showDialog('edit', row)
            }),
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => deleteItem(row)
            })
          ])
      }
    ]
  }
})

// 搜索处理
const handleSearch = (params: Record<string, any>) => {
  Object.assign(searchState, params)
  searchData()
}

// 显示对话框
const showDialog = (type: Form.DialogType, row?: PageListItem): void => {
  dialogType.value = type
  currentData.value = row || {}
  nextTick(() => {
    dialogVisible.value = true
  })
}

// 删除操作
const deleteItem = async (row: PageListItem): Promise<void> => {
  try {
    await ElMessageBox.confirm(`确定要删除"${row.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await PageService.deletePage(row.id)
    ElMessage.success('删除成功')
    await refreshAll()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 处理对话框提交
const handleDialogSubmit = async (): Promise<void> => {
  try {
    dialogVisible.value = false
    currentData.value = {}
    await refreshAll()
    ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

// 处理表格行选择变化
const handleSelectionChange = (selection: PageListItem[]): void => {
  selectedRows.value = selection
}

// 批量操作（可选）
const batchOperation = async (): Promise<void> => {
  // 批量操作逻辑
}

// 初始化
onMounted(() => {
  refreshAll()
})
</script>

<style scoped lang="scss">
.page-container {
  // art-full-height 自动计算页面剩余高度
  
  :deep(.art-table-card) {
    // art-table-card 符合系统样式并自动撑满剩余高度
  }
}
</style>
```

### 2.2 搜索组件模板 (`modules/page-search.vue`)

```vue
<template>
  <ArtSearchBar
    v-model:filter="searchFormState"
    :items="formItems"
    @reset="handleReset"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
import type { SearchChangeParams, SearchFormItem } from '@/types'

interface Emits {
  (e: 'search', params: Record<string, any>): void
  (e: 'reset'): void
}

const props = defineProps<{
  filter: Record<string, any>
}>()

const emit = defineEmits<Emits>()

const searchFormState = ref({ ...props.filter })

// 监听外部 filter 变化
watch(
  () => props.filter,
  (newFilter) => {
    searchFormState.value = { ...newFilter }
  },
  { deep: true, immediate: true }
)

// 重置表单
const handleReset = () => {
  searchFormState.value = { ...props.filter }
  emit('reset')
}

// 搜索处理
const handleSearch = () => {
  emit('search', searchFormState.value)
}

// 表单变化处理
const handleFormChange = (params: SearchChangeParams): void => {
  // 可以在这里处理表单联动逻辑
}

// 表单配置项
const formItems: SearchFormItem[] = [
  {
    label: '关键词',
    prop: 'keyword',
    type: 'input',
    config: {
      clearable: true,
      placeholder: '请输入关键词'
    },
    onChange: handleFormChange
  },
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '启用', value: 'enabled' },
      { label: '禁用', value: 'disabled' }
    ],
    config: {
      clearable: true,
      placeholder: '请选择状态'
    },
    onChange: handleFormChange
  },
  {
    label: '日期范围',
    prop: 'daterange',
    type: 'daterange',
    config: {
      clearable: true,
      placeholder: ['开始日期', '结束日期']
    },
    onChange: handleFormChange
  }
]
</script>
```

### 2.3 对话框组件模板 (`modules/page-dialog.vue`)

```vue
<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="right"
      v-loading="isLoading"
      element-loading-text="处理中..."
    >
      <!-- 基础表单项 -->
      <ElFormItem label="名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入名称" />
      </ElFormItem>

      <ElFormItem label="描述" prop="description">
        <ElInput
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </ElFormItem>

      <ElFormItem label="状态" prop="status">
        <ElSelect v-model="formData.status" placeholder="请选择状态" style="width: 100%">
          <ElOption label="启用" value="enabled" />
          <ElOption label="禁用" value="disabled" />
        </ElSelect>
      </ElFormItem>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel" :disabled="isLoading">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit"
          :loading="isLoading"
        >
          {{ dialogType === 'add' ? '新增' : '更新' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { PageService } from '@/api/pageApi'

// 组件属性
interface Props {
  visible: boolean
  type: string
  data?: any
}

// 组件事件
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'submit'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const isLoading = ref(false)

// 对话框显示控制
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框类型
const dialogType = computed(() => props.type)

// 对话框标题
const dialogTitle = computed(() => {
  return dialogType.value === 'add' ? '新增' : '编辑'
})

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  status: 'enabled'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 初始化表单数据
const initFormData = () => {
  const isEdit = dialogType.value === 'edit' && props.data
  const data = props.data

  Object.assign(formData, {
    name: isEdit ? data.name || '' : '',
    description: isEdit ? data.description || '' : '',
    status: isEdit ? data.status || 'enabled' : 'enabled'
  })
}

// 监听对话框状态变化
watch(
  () => [props.visible, props.type, props.data],
  async ([visible]) => {
    if (visible) {
      // 初始化表单数据
      initFormData()

      // 清除验证信息
      nextTick(() => {
        formRef.value?.clearValidate()
      })
    }
  },
  { immediate: true }
)

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理对话框关闭
const handleDialogClosed = () => {
  // 重置表单数据
  Object.assign(formData, {
    name: '',
    description: '',
    status: 'enabled'
  })
}

// 处理提交
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    isLoading.value = true

    const submitData = {
      name: formData.name,
      description: formData.description,
      status: formData.status
    }

    if (dialogType.value === 'add') {
      // 新增
      await PageService.createPage(submitData)
      ElMessage.success('新增成功')
    } else {
      // 编辑
      const id = props.data?.id
      if (!id) {
        ElMessage.error('数据ID不存在')
        return
      }
      await PageService.updatePage(id, submitData)
      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    emit('submit')
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 暗黑模式适配 */
html.dark {
  .dialog-footer {
    // 暗黑模式下的样式调整
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto !important;
  }

  :deep(.el-form-item__label) {
    width: 80px !important;
    min-width: 80px !important;
  }
}
</style>
```

## 3. 路由注册完整示例

### 3.1 静态路由配置

```typescript
// router/routes/staticRoutes.ts
import type { AppRouteRecordRaw } from '@/types/router'
import { RoutesAlias } from '@/router/routes/alias'

/**
 * 静态路由配置
 * 不需要权限就能访问的路由
 */
export const staticRoutes: AppRouteRecordRaw[] = [
  {
    path: RoutesAlias.Login,
    name: 'Login',
    component: () => import('@/views/auth/login/index.vue'),
    meta: {
      title: 'menus.login.title',
      isHideTab: true,
      setTheme: true
    }
  },
  {
    path: RoutesAlias.Register,
    name: 'Register',
    component: () => import('@/views/auth/register/index.vue'),
    meta: {
      title: 'menus.register.title',
      isHideTab: true,
      noLogin: true,
      setTheme: true
    }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      isHideTab: true
    }
  }
]
```

### 3.2 动态路由配置

```typescript
// router/routes/asyncRoutes.ts
import type { AppRouteRecord } from '@/types/router'
import { RoutesAlias } from '@/router/routes/alias'

/**
 * 动态路由配置
 * 需要权限验证的路由
 */
export const asyncRoutes: AppRouteRecord[] = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.dashboard.title',
      icon: '&#xe721;',
      roles: ['R_ADMIN', 'R_USER']
    },
    children: [
      {
        path: 'console',
        name: 'Console',
        component: () => import('@/views/dashboard/console/index.vue'),
        meta: {
          title: 'menus.dashboard.console',
          keepAlive: false,
          fixedTab: true,
          roles: ['R_ADMIN', 'R_USER']
        }
      }
    ]
  },
  {
    name: 'System',
    path: '/system',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.system.title',
      icon: '&#xe6e7;',
      roles: ['R_ADMIN']
    },
    children: [
      {
        path: 'user',
        name: 'UserManagement',
        component: () => import('@/views/system/user/index.vue'),
        meta: {
          title: 'menus.system.user',
          icon: '&#xe6e8;',
          roles: ['R_ADMIN']
        }
      },
      {
        path: 'role',
        name: 'RoleManagement',
        component: () => import('@/views/system/role/index.vue'),
        meta: {
          title: 'menus.system.role',
          icon: '&#xe6e9;',
          roles: ['R_ADMIN']
        }
      }
    ]
  },
  {
    name: 'Config',
    path: '/config',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.config.title',
      icon: '&#xe6ea;',
      roles: ['R_ADMIN', 'R_USER']
    },
    children: [
      {
        path: 'environment',
        name: 'EnvironmentManagement',
        component: () => import('@/views/config/environment/index.vue'),
        meta: {
          title: 'menus.config.environment',
          icon: '&#xe6eb;',
          roles: ['R_ADMIN', 'R_USER']
        }
      }
    ]
  }
]
```

### 3.3 路由守卫配置

```typescript
// router/guards/beforeEach.ts
import type { Router } from 'vue-router'
import { useUserStore } from '@/store/core/user'
import { useSettingStore } from '@/store/core/setting'
import { ElMessage } from 'element-plus'

export function setupBeforeEachGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()
    const settingStore = useSettingStore()

    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE}`
    }

    // 检查是否需要登录
    if (to.meta?.noLogin) {
      next()
      return
    }

    // 检查登录状态
    if (!userStore.isLoggedIn) {
      if (to.path !== '/login') {
        ElMessage.warning('请先登录')
        next('/login')
        return
      }
    }

    // 检查权限
    if (to.meta?.roles && to.meta.roles.length > 0) {
      const hasPermission = to.meta.roles.some(role =>
        userStore.roles.includes(role)
      )

      if (!hasPermission) {
        ElMessage.error('没有访问权限')
        next('/403')
        return
      }
    }

    // 设置主题
    if (to.meta?.setTheme) {
      settingStore.setSystemTheme(settingStore.systemThemeType)
    }

    next()
  })
}
```

## 4. API接口调用最佳实践

### 4.1 API服务类设计

```typescript
// api/pageApi.ts
import request from '@/utils/http'
import type {
  PageListParams,
  PageListResponse,
  PageCreateRequest,
  PageUpdateRequest,
  PageResponse
} from '@/types/api/page'

export class PageService {
  /**
   * 获取列表
   */
  static async getPageList(params: PageListParams): Promise<PageListResponse> {
    return await request.get<PageListResponse>({
      url: '/api/page/list',
      params
    })
  }

  /**
   * 获取详情
   */
  static async getPageDetail(id: number): Promise<PageResponse> {
    return await request.get<PageResponse>({
      url: `/api/page/${id}`
    })
  }

  /**
   * 创建
   */
  static async createPage(data: PageCreateRequest): Promise<PageResponse> {
    return await request.post<PageResponse>({
      url: '/api/page',
      data
    })
  }

  /**
   * 更新
   */
  static async updatePage(id: number, data: PageUpdateRequest): Promise<PageResponse> {
    return await request.put<PageResponse>({
      url: `/api/page/${id}`,
      data
    })
  }

  /**
   * 删除
   */
  static async deletePage(id: number): Promise<void> {
    return await request.del<void>({
      url: `/api/page/${id}`
    })
  }

  /**
   * 批量删除
   */
  static async batchDeletePages(ids: number[]): Promise<void> {
    return await request.post<void>({
      url: '/api/page/batch-delete',
      data: { ids }
    })
  }

  /**
   * 文件上传
   */
  static async uploadFile(file: File): Promise<{ url: string }> {
    const formData = new FormData()
    formData.append('file', file)

    return await request.post<{ url: string }>({
      url: '/api/page/upload',
      data: formData
    })
  }
}
```

### 4.2 HTTP请求工具配置

```typescript
// utils/http/index.ts
import axios, { InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { useUserStore } from '@/store/core/user'
import { ElMessage } from 'element-plus'

// 扩展请求配置
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  showErrorMessage?: boolean
}

const axiosInstance = axios.create({
  timeout: 15000,
  baseURL: import.meta.env.VITE_API_URL,
  withCredentials: false,
  transformRequest: [
    (data, headers) => {
      // 智能处理不同数据类型
      if (data instanceof FormData) {
        return data  // FormData 保持原样
      }
      if (typeof data === 'string' || typeof data === 'number' || data == null) {
        return data
      }
      return JSON.stringify(data)  // 其他对象转 JSON
    }
  ]
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const { token } = useUserStore()

    // 设置 Token
    if (token) {
      config.headers.set('Authorization', `Bearer ${token}`)
    }

    // 设置 Content-Type
    if (config.data instanceof FormData) {
      config.headers.delete('Content-Type')  // 让浏览器自动设置
    } else if (config.data && typeof config.data === 'object') {
      config.headers.set('Content-Type', 'application/json')
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse<Api.Common.BaseResponse>) => {
    const { success, code, message, data } = response.data

    // 检查业务状态
    if (success === false) {
      ElMessage.error(message || '业务操作失败')
      throw new Error(message || '业务操作失败')
    }

    // 返回数据部分
    return data
  },
  async (error) => {
    // 处理 401 错误
    if (error.response?.status === 401) {
      const userStore = useUserStore()
      userStore.logout()
      ElMessage.error('登录已过期，请重新登录')
      return Promise.reject(error)
    }

    // 处理其他错误
    const message = error.response?.data?.message || error.message || '请求失败'
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// API 方法集合
const api = {
  get<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return axiosInstance({ ...config, method: 'GET' })
  },
  post<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return axiosInstance({ ...config, method: 'POST' })
  },
  put<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return axiosInstance({ ...config, method: 'PUT' })
  },
  del<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return axiosInstance({ ...config, method: 'DELETE' })
  }
}

export default api
```

### 4.3 类型定义规范

```typescript
// types/api/page.ts
/**
 * 页面相关API类型定义
 */

// 基础类型
export interface PageBase {
  name: string
  description?: string
  status: 'enabled' | 'disabled'
}

// 请求类型
export interface PageListParams {
  current?: number
  size?: number
  keyword?: string
  status?: string
}

export interface PageCreateRequest extends PageBase {
  // 创建时的特定字段
}

export interface PageUpdateRequest extends Partial<PageBase> {
  // 更新时的特定字段
}

// 响应类型
export interface PageResponse extends PageBase {
  id: number
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string
}

export interface PageListResponse {
  records: PageResponse[]
  total: number
  current: number
  size: number
}
```

## 5. 状态管理模块化规范

### 5.1 核心状态管理（用户状态）

```typescript
// store/core/user.ts
import { defineStore } from 'pinia'
import { UserService } from '@/api/usersApi'
import type { LoginParams, UserInfo } from '@/types/api/user'

export const useUserStore = defineStore('user', () => {
  // ==================== 状态定义 ====================

  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])

  // 加载状态
  const loading = ref(false)

  // ==================== 计算属性 ====================

  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const isAdmin = computed(() => roles.value.includes('admin'))
  const isSuperUser = computed(() => roles.value.includes('superuser'))

  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })

  const hasRole = computed(() => (role: string) => {
    return roles.value.includes(role)
  })

  // ==================== 方法 ====================

  /**
   * 登录
   */
  const login = async (params: LoginParams) => {
    try {
      loading.value = true
      const response = await UserService.login(params)

      // 设置token
      token.value = response.access_token

      // 获取用户信息
      await getUserInfo()

      return response
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取用户信息
   */
  const getUserInfo = async () => {
    try {
      const response = await UserService.getUserInfo()
      userInfo.value = response
      roles.value = response.roles || []
      permissions.value = response.permissions || []
      return response
    } catch (error) {
      // 获取用户信息失败，清除登录状态
      logout()
      throw error
    }
  }

  /**
   * 登出
   */
  const logout = () => {
    userInfo.value = null
    token.value = ''
    permissions.value = []
    roles.value = []

    // 清除本地存储
    localStorage.removeItem('token')

    // 跳转到登录页
    router.push('/login')
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = (info: Partial<UserInfo>) => {
    if (userInfo.value) {
      Object.assign(userInfo.value, info)
    }
  }

  return {
    // 状态
    userInfo: readonly(userInfo),
    token: readonly(token),
    permissions: readonly(permissions),
    roles: readonly(roles),
    loading: readonly(loading),

    // 计算属性
    isLoggedIn,
    isAdmin,
    isSuperUser,
    hasPermission,
    hasRole,

    // 方法
    login,
    getUserInfo,
    logout,
    updateUserInfo
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['token', 'userInfo', 'roles', 'permissions']
  }
})
```

### 5.2 业务状态管理（环境管理）

```typescript
// store/business/environment/index.ts
import { defineStore } from 'pinia'
import { EnvironmentService } from '@/api/envApi'
import type {
  EnvironmentListParams,
  EnvironmentResponse,
  EnvironmentCreateRequest,
  EnvironmentUpdateRequest
} from '@/types/api/environment'

export const useEnvironmentStore = defineStore('environment', () => {
  // ==================== 状态定义 ====================

  const environments = ref<EnvironmentResponse[]>([])
  const currentEnvironment = ref<EnvironmentResponse | null>(null)
  const loading = ref(false)

  // 分页状态
  const pagination = ref({
    current: 1,
    size: 20,
    total: 0
  })

  // ==================== 计算属性 ====================

  const enabledEnvironments = computed(() =>
    environments.value.filter(env => env.status === 'connected')
  )

  const environmentOptions = computed(() =>
    environments.value.map(env => ({
      label: env.name,
      value: env.id
    }))
  )

  // ==================== 方法 ====================

  /**
   * 获取环境列表
   */
  const fetchEnvironments = async (params: EnvironmentListParams = {}) => {
    try {
      loading.value = true
      const response = await EnvironmentService.getEnvironmentList({
        current: pagination.value.current,
        size: pagination.value.size,
        ...params
      })

      environments.value = response.records
      pagination.value = {
        current: response.current,
        size: response.size,
        total: response.total
      }

      return response
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取环境详情
   */
  const fetchEnvironmentDetail = async (id: number) => {
    try {
      loading.value = true
      const response = await EnvironmentService.getEnvironmentDetail(id)
      currentEnvironment.value = response
      return response
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建环境
   */
  const createEnvironment = async (data: EnvironmentCreateRequest) => {
    const response = await EnvironmentService.createEnvironment(data)

    // 更新本地状态
    environments.value.unshift(response)
    pagination.value.total += 1

    return response
  }

  /**
   * 更新环境
   */
  const updateEnvironment = async (id: number, data: EnvironmentUpdateRequest) => {
    const response = await EnvironmentService.updateEnvironment(id, data)

    // 更新本地状态
    const index = environments.value.findIndex(env => env.id === id)
    if (index !== -1) {
      environments.value[index] = response
    }

    // 更新当前环境
    if (currentEnvironment.value?.id === id) {
      currentEnvironment.value = response
    }

    return response
  }

  /**
   * 删除环境
   */
  const deleteEnvironment = async (id: number) => {
    await EnvironmentService.deleteEnvironment(id)

    // 更新本地状态
    const index = environments.value.findIndex(env => env.id === id)
    if (index !== -1) {
      environments.value.splice(index, 1)
      pagination.value.total -= 1
    }

    // 清除当前环境
    if (currentEnvironment.value?.id === id) {
      currentEnvironment.value = null
    }
  }

  /**
   * 测试环境连接
   */
  const testConnection = async (id: number) => {
    const response = await EnvironmentService.testConnection(id)

    // 更新环境状态
    const index = environments.value.findIndex(env => env.id === id)
    if (index !== -1) {
      environments.value[index].status = response.status
      environments.value[index].last_test_time = response.test_time
    }

    return response
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    environments.value = []
    currentEnvironment.value = null
    pagination.value = {
      current: 1,
      size: 20,
      total: 0
    }
  }

  return {
    // 状态
    environments: readonly(environments),
    currentEnvironment: readonly(currentEnvironment),
    loading: readonly(loading),
    pagination: readonly(pagination),

    // 计算属性
    enabledEnvironments,
    environmentOptions,

    // 方法
    fetchEnvironments,
    fetchEnvironmentDetail,
    createEnvironment,
    updateEnvironment,
    deleteEnvironment,
    testConnection,
    resetState
  }
})
```

## 6. 主题适配完整指南

### 6.1 CSS变量定义

```scss
// assets/styles/variables.scss
// Light 主题变量
:root {
  // 主题色彩
  --art-primary: 93, 135, 255;
  --art-secondary: 73, 190, 255;
  --art-success: 19, 222, 185;
  --art-warning: 255, 174, 31;
  --art-danger: 255, 77, 79;
  --art-info: 107, 125, 155;

  // 背景色彩
  --art-bg-primary: 236, 242, 255;
  --art-bg-secondary: 232, 247, 255;
  --art-bg-success: 230, 255, 250;
  --art-bg-warning: 254, 245, 229;
  --art-bg-danger: 253, 237, 232;
  --art-bg-info: 240, 242, 247;

  // 文本色彩
  --art-text-primary: 29, 52, 61;
  --art-text-secondary: 107, 125, 155;
  --art-text-placeholder: 165, 175, 185;
  --art-text-disabled: 195, 205, 215;

  // 边框色彩
  --art-border-light: 241, 241, 244;
  --art-border-base: 220, 223, 230;
  --art-border-dark: 200, 206, 212;

  // 背景色彩
  --art-bg-color: #fafbfc;
  --art-main-bg-color: #ffffff;
  --art-card-bg-color: #ffffff;

  // 阴影
  --art-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --art-card-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
}

// Dark 主题变量
html.dark {
  // 主题色彩（保持一致）
  --art-primary: 93, 135, 255;
  --art-secondary: 73, 190, 255;
  --art-success: 19, 222, 185;
  --art-warning: 255, 174, 31;
  --art-danger: 255, 77, 79;
  --art-info: 107, 125, 155;

  // 背景色彩（暗色调整）
  --art-bg-primary: 37, 54, 98;
  --art-bg-secondary: 28, 69, 93;
  --art-bg-success: 27, 60, 72;
  --art-bg-warning: 77, 58, 42;
  --art-bg-danger: 100, 49, 61;
  --art-bg-info: 45, 50, 62;

  // 文本色彩（暗色调整）
  --art-text-primary: 255, 255, 255;
  --art-text-secondary: 187, 187, 189;
  --art-text-placeholder: 136, 136, 136;
  --art-text-disabled: 85, 85, 85;

  // 边框色彩（暗色调整）
  --art-border-light: 30, 32, 39;
  --art-border-base: 63, 66, 85;
  --art-border-dark: 85, 91, 112;

  // 背景色彩（暗色调整）
  --art-bg-color: #070707;
  --art-main-bg-color: #161618;
  --art-card-bg-color: #1e1e20;

  // 阴影（暗色调整）
  --art-box-shadow: none;
  --art-card-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.2);
}
```

### 6.2 主题切换工具

```typescript
// composables/useTheme.ts
import { useSettingStore } from '@/store/core/setting'
import { SystemThemeEnum } from '@/enums/appEnum'

export function useTheme() {
  const settingStore = useSettingStore()

  // 禁用过渡效果
  const disableTransitions = () => {
    const style = document.createElement('style')
    style.setAttribute('id', 'disable-transitions')
    style.textContent = '* { transition: none !important; }'
    document.head.appendChild(style)
  }

  // 启用过渡效果
  const enableTransitions = () => {
    const style = document.getElementById('disable-transitions')
    if (style) {
      style.remove()
    }
  }

  // 设置系统主题
  const setSystemTheme = (theme: SystemThemeEnum) => {
    // 临时禁用过渡效果
    disableTransitions()

    const el = document.getElementsByTagName('html')[0]
    const isDark = theme === SystemThemeEnum.DARK

    // 设置主题类名
    if (isDark) {
      el.classList.add('dark')
    } else {
      el.classList.remove('dark')
    }

    // 设置Element Plus主题色
    const primary = settingStore.systemThemeColor
    for (let i = 1; i <= 9; i++) {
      document.documentElement.style.setProperty(
        `--el-color-primary-light-${i}`,
        isDark ? getDarkColor(primary, i / 10) : getLightColor(primary, i / 10)
      )
    }

    // 更新store中的主题设置
    settingStore.setSystemTheme(theme)

    // 恢复过渡效果
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        enableTransitions()
      })
    })
  }

  // 自动设置系统主题
  const setSystemAutoTheme = () => {
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setSystemTheme(SystemThemeEnum.DARK)
    } else {
      setSystemTheme(SystemThemeEnum.LIGHT)
    }
  }

  // 切换主题
  const toggleTheme = () => {
    const currentTheme = settingStore.systemThemeType
    const newTheme = currentTheme === SystemThemeEnum.DARK
      ? SystemThemeEnum.LIGHT
      : SystemThemeEnum.DARK
    setSystemTheme(newTheme)
  }

  // 主题切换动画
  const themeAnimation = (e: MouseEvent) => {
    const x = e.clientX
    const y = e.clientY
    const endRadius = Math.hypot(
      Math.max(x, innerWidth - x),
      Math.max(y, innerHeight - y)
    )

    // 设置CSS变量
    document.documentElement.style.setProperty('--x', x + 'px')
    document.documentElement.style.setProperty('--y', y + 'px')
    document.documentElement.style.setProperty('--r', endRadius + 'px')

    // 使用View Transition API（如果支持）
    if (document.startViewTransition) {
      document.startViewTransition(() => toggleTheme())
    } else {
      toggleTheme()
    }
  }

  return {
    setSystemTheme,
    setSystemAutoTheme,
    toggleTheme,
    themeAnimation
  }
}

// 颜色工具函数
function getDarkColor(color: string, level: number): string {
  // 实现颜色加深逻辑
  return color
}

function getLightColor(color: string, level: number): string {
  // 实现颜色变浅逻辑
  return color
}
```

### 6.3 组件主题适配示例

```vue
<template>
  <div class="custom-component">
    <div class="header">
      <h3 class="title">标题</h3>
      <div class="actions">
        <ElButton type="primary">主要按钮</ElButton>
        <ElButton>次要按钮</ElButton>
      </div>
    </div>

    <div class="content">
      <ElCard class="data-card" shadow="never">
        <div class="data-item">
          <span class="label">数据标签</span>
          <span class="value">数据值</span>
        </div>
      </ElCard>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: 'CustomComponent' })
</script>

<style scoped lang="scss">
.custom-component {
  // 使用CSS变量确保主题适配
  background-color: rgb(var(--art-main-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 8px;
  padding: 16px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgb(var(--art-border-light));

    .title {
      margin: 0;
      color: rgb(var(--art-text-primary));
      font-size: 16px;
      font-weight: 600;
    }

    .actions {
      display: flex;
      gap: 8px;
    }
  }

  .content {
    .data-card {
      background-color: rgb(var(--art-card-bg-color));
      border: 1px solid rgb(var(--art-border-light));
      box-shadow: var(--art-card-shadow);

      .data-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;

        .label {
          color: rgb(var(--art-text-secondary));
          font-size: 14px;
        }

        .value {
          color: rgb(var(--art-text-primary));
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }
}

/* 暗黑模式特殊处理 */
html.dark {
  .custom-component {
    // 暗黑模式下的特殊样式调整
    .header {
      .title {
        // 暗黑模式下标题可能需要特殊处理
      }
    }

    .content {
      .data-card {
        // 暗黑模式下卡片的特殊样式
        :deep(.el-card__body) {
          background-color: rgb(var(--art-card-bg-color));
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-component {
    padding: 12px;

    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}
```

## 7. 组件使用标准化规范

### 7.1 核心组件使用指南

#### ArtTable 表格组件

```vue
<template>
  <!-- 标准表格使用 -->
  <ArtTable
    :loading="isLoading"
    :data="tableData"
    :columns="columns"
    :pagination="paginationState"
    :stripe="true"
    :border="false"
    row-key="id"
    @selection-change="handleSelectionChange"
    @pagination:size-change="onPageSizeChange"
    @pagination:current-change="onCurrentPageChange"
    @sort-change="handleSortChange"
  />
</template>

<script setup lang="ts">
// 列配置示例
const columns = ref([
  {
    type: 'selection',
    width: 55,
    fixed: 'left'
  },
  {
    type: 'index',
    label: '序号',
    width: 80
  },
  {
    prop: 'name',
    label: '名称',
    minWidth: 150,
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    formatter: (row) => {
      const statusConfig = getStatusConfig(row.status)
      return h(ElTag, { type: statusConfig.type }, () => statusConfig.text)
    }
  },
  {
    prop: 'created_at',
    label: '创建时间',
    width: 180,
    sortable: true,
    formatter: (row) => formatDateTime(row.created_at)
  },
  {
    prop: 'operation',
    label: '操作',
    width: 120,
    fixed: 'right',
    formatter: (row) =>
      h('div', { class: 'flex gap-2' }, [
        h(ArtButtonTable, {
          type: 'edit',
          onClick: () => handleEdit(row)
        }),
        h(ArtButtonTable, {
          type: 'delete',
          onClick: () => handleDelete(row)
        })
      ])
  }
])
</script>
```

#### ArtSearchBar 搜索组件

```vue
<template>
  <!-- 标准搜索栏使用 -->
  <ArtSearchBar
    v-model:filter="searchFormState"
    :items="searchItems"
    :loading="searchLoading"
    @search="handleSearch"
    @reset="handleReset"
    @export="handleExport"
  />
</template>

<script setup lang="ts">
// 搜索项配置示例
const searchItems: SearchFormItem[] = [
  {
    label: '关键词',
    prop: 'keyword',
    type: 'input',
    config: {
      clearable: true,
      placeholder: '请输入关键词搜索'
    }
  },
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '启用', value: 'enabled' },
      { label: '禁用', value: 'disabled' }
    ],
    config: {
      clearable: true,
      placeholder: '请选择状态'
    }
  },
  {
    label: '创建时间',
    prop: 'dateRange',
    type: 'daterange',
    config: {
      clearable: true,
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    label: '分类',
    prop: 'category',
    type: 'cascader',
    options: () => categoryOptions.value,
    config: {
      clearable: true,
      placeholder: '请选择分类',
      props: {
        value: 'id',
        label: 'name',
        children: 'children'
      }
    }
  }
]
</script>
```

#### ArtTableHeader 表格头部组件

```vue
<template>
  <!-- 标准表格头部使用 -->
  <ArtTableHeader
    v-model:columns="columnChecks"
    :loading="isLoading"
    @refresh="handleRefresh"
    @export="handleExport"
  >
    <template #left>
      <!-- 左侧操作按钮 -->
      <ElButton
        type="primary"
        @click="handleAdd"
        v-auth="'add'"
      >
        <template #icon>
          <ElIcon><Plus /></ElIcon>
        </template>
        新增
      </ElButton>

      <ElButton
        v-if="selectedRows.length > 0"
        type="danger"
        @click="handleBatchDelete"
        :loading="batchLoading"
      >
        批量删除
      </ElButton>

      <ElButton
        v-if="selectedRows.length > 0"
        @click="handleBatchExport"
        :loading="exportLoading"
      >
        批量导出
      </ElButton>
    </template>

    <template #right>
      <!-- 右侧操作按钮 -->
      <ElButton
        type="success"
        @click="handleStats"
      >
        统计信息
      </ElButton>

      <ElButton
        @click="handleImport"
      >
        导入数据
      </ElButton>
    </template>
  </ArtTableHeader>
</template>
```

### 7.2 表单组件使用规范

#### 基础表单结构

```vue
<template>
  <ElForm
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="120px"
    label-position="right"
    :disabled="formDisabled"
    v-loading="formLoading"
    element-loading-text="处理中..."
  >
    <!-- 基础信息 -->
    <ElCard class="form-section" shadow="never">
      <template #header>
        <span class="section-title">基础信息</span>
      </template>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="名称" prop="name">
            <ElInput
              v-model="formData.name"
              placeholder="请输入名称"
              clearable
              maxlength="50"
              show-word-limit
            />
          </ElFormItem>
        </ElCol>

        <ElCol :span="12">
          <ElFormItem label="状态" prop="status">
            <ElSelect
              v-model="formData.status"
              placeholder="请选择状态"
              style="width: 100%"
            >
              <ElOption label="启用" value="enabled" />
              <ElOption label="禁用" value="disabled" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElFormItem label="描述" prop="description">
        <ElInput
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
          maxlength="200"
          show-word-limit
        />
      </ElFormItem>
    </ElCard>

    <!-- 高级配置 -->
    <ElCard class="form-section" shadow="never">
      <template #header>
        <span class="section-title">高级配置</span>
      </template>

      <ElFormItem label="配置文件" prop="configFile">
        <ElUpload
          v-model:file-list="fileList"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :limit="1"
          accept=".json,.yaml,.yml"
        >
          <ElButton type="primary">
            <template #icon>
              <ElIcon><Upload /></ElIcon>
            </template>
            选择文件
          </ElButton>
          <template #tip>
            <div class="upload-tip">
              只能上传 JSON/YAML 文件，且不超过 2MB
            </div>
          </template>
        </ElUpload>
      </ElFormItem>
    </ElCard>
  </ElForm>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules, UploadProps } from 'element-plus'

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 文件上传处理
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = ['application/json', 'text/yaml', 'application/x-yaml'].includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isValidType) {
    ElMessage.error('只能上传 JSON 或 YAML 格式的文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('文件大小不能超过 2MB!')
    return false
  }
  return true
}
</script>

<style scoped lang="scss">
.form-section {
  margin-bottom: 20px;

  .section-title {
    font-weight: 600;
    color: rgb(var(--art-text-primary));
  }

  :deep(.el-card__body) {
    padding: 20px;
  }
}

.upload-tip {
  color: rgb(var(--art-text-secondary));
  font-size: 12px;
  margin-top: 4px;
}

/* 暗黑模式适配 */
html.dark {
  .form-section {
    :deep(.el-card) {
      background-color: rgb(var(--art-card-bg-color));
      border-color: rgb(var(--art-border-light));
    }
  }
}
```

### 7.3 对话框组件使用规范

```vue
<template>
  <!-- 标准对话框结构 -->
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="dialogWidth"
    :fullscreen="isFullscreen"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @opened="handleDialogOpened"
    @closed="handleDialogClosed"
  >
    <!-- 对话框头部插槽 -->
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog-header">
        <h4 :id="titleId" :class="titleClass">{{ dialogTitle }}</h4>
        <div class="dialog-header-actions">
          <ElButton
            v-if="!isFullscreen"
            text
            @click="toggleFullscreen"
            title="全屏"
          >
            <ElIcon><FullScreen /></ElIcon>
          </ElButton>
          <ElButton
            v-else
            text
            @click="toggleFullscreen"
            title="退出全屏"
          >
            <ElIcon><Aim /></ElIcon>
          </ElButton>
          <ElButton text @click="close" title="关闭">
            <ElIcon><Close /></ElIcon>
          </ElButton>
        </div>
      </div>
    </template>

    <!-- 对话框内容 -->
    <div class="dialog-content" v-loading="dialogLoading">
      <slot />
    </div>

    <!-- 对话框底部 -->
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel" :disabled="submitLoading">
          取消
        </ElButton>
        <ElButton
          type="primary"
          @click="handleConfirm"
          :loading="submitLoading"
        >
          {{ confirmText }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  title?: string
  width?: string
  confirmText?: string
  loading?: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '对话框',
  width: '600px',
  confirmText: '确定',
  loading: false
})

const emit = defineEmits<Emits>()

// 对话框状态
const isFullscreen = ref(false)
const submitLoading = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => props.title)
const dialogWidth = computed(() => isFullscreen.value ? '100%' : props.width)
const dialogLoading = computed(() => props.loading)

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 处理确认
const handleConfirm = () => {
  emit('confirm')
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 对话框打开
const handleDialogOpened = () => {
  // 对话框打开后的处理
}

// 对话框关闭
const handleDialogClosed = () => {
  // 重置状态
  isFullscreen.value = false
  submitLoading.value = false
}
</script>

<style scoped lang="scss">
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h4 {
    margin: 0;
    color: rgb(var(--art-text-primary));
  }

  .dialog-header-actions {
    display: flex;
    gap: 8px;
  }
}

.dialog-content {
  min-height: 200px;
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgb(var(--art-border-light));
}

/* 全屏模式样式 */
:deep(.el-dialog.is-fullscreen) {
  .dialog-content {
    max-height: calc(100vh - 200px);
  }
}

/* 暗黑模式适配 */
html.dark {
  .dialog-header {
    h4 {
      color: rgb(var(--art-text-primary));
    }
  }

  .dialog-footer {
    border-top-color: rgb(var(--art-border-light));
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .dialog-content {
    max-height: 50vh;
  }
}
```

## 8. 样式开发规范

### 8.1 SCSS 组织结构

```scss
// assets/styles/
├── reset.scss           # 重置样式
├── variables.scss       # SCSS 变量和 CSS 变量
├── mixins.scss         # SCSS 混入
├── app.scss            # 全局样式
├── el-ui.scss          # Element Plus 样式覆盖
├── el-light.scss       # Element Plus 亮色主题
├── el-dark.scss        # Element Plus 暗色主题
├── dark.scss           # 系统暗色主题
├── mobile.scss         # 移动端适配
├── change.scss         # 主题切换过渡
└── theme-animation.scss # 主题切换动画
```

### 8.2 SCSS 变量和混入

```scss
// assets/styles/mixins.scss
// 响应式断点混入
@mixin respond-to($breakpoint) {
  @if $breakpoint == mobile {
    @media (max-width: 767px) { @content; }
  }
  @if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1023px) { @content; }
  }
  @if $breakpoint == desktop {
    @media (min-width: 1024px) { @content; }
  }
  @if $breakpoint == large {
    @media (min-width: 1200px) { @content; }
  }
}

// 文本省略混入
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// Flex 布局混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

// 卡片样式混入
@mixin card-style {
  background-color: rgb(var(--art-card-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 8px;
  box-shadow: var(--art-card-shadow);
}

// 按钮样式混入
@mixin button-style($type: 'default') {
  @if $type == 'primary' {
    background-color: rgb(var(--art-primary));
    border-color: rgb(var(--art-primary));
    color: #ffffff;

    &:hover {
      background-color: rgba(var(--art-primary), 0.8);
      border-color: rgba(var(--art-primary), 0.8);
    }
  }

  @if $type == 'success' {
    background-color: rgb(var(--art-success));
    border-color: rgb(var(--art-success));
    color: #ffffff;

    &:hover {
      background-color: rgba(var(--art-success), 0.8);
      border-color: rgba(var(--art-success), 0.8);
    }
  }
}

// 动画混入
@mixin transition($property: all, $duration: 0.3s, $timing: ease) {
  transition: $property $duration $timing;
}

@mixin fade-in($duration: 0.3s) {
  animation: fadeIn $duration ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
```

### 8.3 组件样式规范

```scss
// 组件样式编写规范
.component-name {
  // 1. 布局相关属性
  position: relative;
  display: flex;
  width: 100%;
  height: auto;

  // 2. 外观相关属性
  background-color: rgb(var(--art-main-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 8px;

  // 3. 文本相关属性
  color: rgb(var(--art-text-primary));
  font-size: 14px;
  line-height: 1.5;

  // 4. 其他属性
  @include transition();

  // 5. 伪类
  &:hover {
    border-color: rgb(var(--art-primary));
  }

  &:focus {
    outline: 2px solid rgba(var(--art-primary), 0.2);
    outline-offset: 2px;
  }

  // 6. 修饰符类
  &.is-active {
    background-color: rgba(var(--art-primary), 0.1);
    border-color: rgb(var(--art-primary));
  }

  &.is-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  // 7. 子元素
  .component-header {
    @include flex-between;
    padding: 16px;
    border-bottom: 1px solid rgb(var(--art-border-light));

    .title {
      margin: 0;
      color: rgb(var(--art-text-primary));
      font-size: 16px;
      font-weight: 600;
    }

    .actions {
      display: flex;
      gap: 8px;
    }
  }

  .component-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  .component-footer {
    @include flex-end;
    padding: 16px;
    border-top: 1px solid rgb(var(--art-border-light));
    gap: 12px;
  }
}

// 8. 响应式设计
@include respond-to(mobile) {
  .component-name {
    .component-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}

// 9. 暗黑模式适配
html.dark {
  .component-name {
    // 暗黑模式下的特殊样式调整
    .component-header {
      .title {
        color: rgb(var(--art-text-primary));
      }
    }
  }
}

// 10. 深度选择器（修改子组件样式）
.component-name {
  :deep(.el-button) {
    // 修改 Element Plus 按钮样式
    border-radius: 6px;
  }

  :deep(.el-input__inner) {
    // 修改 Element Plus 输入框样式
    border-radius: 6px;
  }
}
```

### 8.4 工具类样式

```scss
// assets/styles/utilities.scss
// 间距工具类
@for $i from 0 through 10 {
  .m-#{$i} { margin: #{$i * 4}px !important; }
  .mt-#{$i} { margin-top: #{$i * 4}px !important; }
  .mr-#{$i} { margin-right: #{$i * 4}px !important; }
  .mb-#{$i} { margin-bottom: #{$i * 4}px !important; }
  .ml-#{$i} { margin-left: #{$i * 4}px !important; }
  .mx-#{$i} {
    margin-left: #{$i * 4}px !important;
    margin-right: #{$i * 4}px !important;
  }
  .my-#{$i} {
    margin-top: #{$i * 4}px !important;
    margin-bottom: #{$i * 4}px !important;
  }

  .p-#{$i} { padding: #{$i * 4}px !important; }
  .pt-#{$i} { padding-top: #{$i * 4}px !important; }
  .pr-#{$i} { padding-right: #{$i * 4}px !important; }
  .pb-#{$i} { padding-bottom: #{$i * 4}px !important; }
  .pl-#{$i} { padding-left: #{$i * 4}px !important; }
  .px-#{$i} {
    padding-left: #{$i * 4}px !important;
    padding-right: #{$i * 4}px !important;
  }
  .py-#{$i} {
    padding-top: #{$i * 4}px !important;
    padding-bottom: #{$i * 4}px !important;
  }
}

// Flex 工具类
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.flex-col { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.items-start { align-items: flex-start !important; }
.items-end { align-items: flex-end !important; }
.items-center { align-items: center !important; }
.items-baseline { align-items: baseline !important; }
.items-stretch { align-items: stretch !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

// 文本工具类
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.text-xs { font-size: 12px !important; }
.text-sm { font-size: 14px !important; }
.text-base { font-size: 16px !important; }
.text-lg { font-size: 18px !important; }
.text-xl { font-size: 20px !important; }
.text-2xl { font-size: 24px !important; }

.font-thin { font-weight: 100 !important; }
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

.text-primary { color: rgb(var(--art-primary)) !important; }
.text-success { color: rgb(var(--art-success)) !important; }
.text-warning { color: rgb(var(--art-warning)) !important; }
.text-danger { color: rgb(var(--art-danger)) !important; }
.text-info { color: rgb(var(--art-info)) !important; }

// 显示工具类
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.inline { display: inline !important; }
.hidden { display: none !important; }

// 位置工具类
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

// 溢出工具类
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }

// 圆角工具类
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: 2px !important; }
.rounded { border-radius: 4px !important; }
.rounded-md { border-radius: 6px !important; }
.rounded-lg { border-radius: 8px !important; }
.rounded-xl { border-radius: 12px !important; }
.rounded-full { border-radius: 9999px !important; }

// 阴影工具类
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; }
```

## 9. 响应式设计指南

### 9.1 断点定义

```scss
// 响应式断点
$breakpoints: (
  'mobile': 480px,
  'tablet': 768px,
  'desktop': 1024px,
  'large': 1200px,
  'xlarge': 1440px
);

// 媒体查询混入
@mixin respond-to($breakpoint) {
  $value: map-get($breakpoints, $breakpoint);
  @if $value {
    @media (min-width: $value) {
      @content;
    }
  }
}

// 最大宽度媒体查询
@mixin respond-below($breakpoint) {
  $value: map-get($breakpoints, $breakpoint);
  @if $value {
    @media (max-width: $value - 1px) {
      @content;
    }
  }
}
```

### 9.2 响应式布局示例

```vue
<template>
  <div class="responsive-layout">
    <!-- 头部 -->
    <header class="layout-header">
      <div class="header-content">
        <div class="logo">
          <img src="/logo.png" alt="Logo" />
          <span class="logo-text">DpTestPlatform</span>
        </div>

        <nav class="nav-menu" v-if="!isMobile">
          <ElMenu mode="horizontal" :default-active="activeMenu">
            <ElMenuItem index="dashboard">仪表板</ElMenuItem>
            <ElMenuItem index="system">系统管理</ElMenuItem>
            <ElMenuItem index="config">配置管理</ElMenuItem>
          </ElMenu>
        </nav>

        <div class="header-actions">
          <ElButton
            v-if="isMobile"
            text
            @click="toggleMobileMenu"
          >
            <ElIcon><Menu /></ElIcon>
          </ElButton>

          <ElDropdown>
            <ElAvatar :src="userInfo.avatar" />
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem>个人中心</ElDropdownItem>
                <ElDropdownItem>设置</ElDropdownItem>
                <ElDropdownItem divided @click="logout">退出登录</ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
        </div>
      </div>

      <!-- 移动端菜单 -->
      <div v-if="isMobile && showMobileMenu" class="mobile-menu">
        <ElMenu :default-active="activeMenu">
          <ElMenuItem index="dashboard">仪表板</ElMenuItem>
          <ElMenuItem index="system">系统管理</ElMenuItem>
          <ElMenuItem index="config">配置管理</ElMenuItem>
        </ElMenu>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="layout-main">
      <div class="main-content">
        <!-- 侧边栏（桌面端） -->
        <aside v-if="!isMobile" class="sidebar">
          <ElMenu :default-active="activeSubmenu">
            <ElSubMenu index="system">
              <template #title>系统管理</template>
              <ElMenuItem index="user">用户管理</ElMenuItem>
              <ElMenuItem index="role">角色管理</ElMenuItem>
            </ElSubMenu>
          </ElMenu>
        </aside>

        <!-- 页面内容 -->
        <section class="content-area">
          <div class="content-header">
            <ElBreadcrumb>
              <ElBreadcrumbItem to="/">首页</ElBreadcrumbItem>
              <ElBreadcrumbItem>系统管理</ElBreadcrumbItem>
              <ElBreadcrumbItem>用户管理</ElBreadcrumbItem>
            </ElBreadcrumb>
          </div>

          <div class="content-body">
            <slot />
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'

const { width } = useWindowSize()

// 响应式状态
const isMobile = computed(() => width.value < 768)
const isTablet = computed(() => width.value >= 768 && width.value < 1024)
const isDesktop = computed(() => width.value >= 1024)

// 移动端菜单状态
const showMobileMenu = ref(false)

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 监听屏幕尺寸变化
watch(isMobile, (newVal) => {
  if (!newVal) {
    showMobileMenu.value = false
  }
})
</script>

<style scoped lang="scss">
.responsive-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background-color: rgb(var(--art-main-bg-color));
  border-bottom: 1px solid rgb(var(--art-border-light));
  position: sticky;
  top: 0;
  z-index: 1000;

  .header-content {
    @include flex-between;
    padding: 0 16px;
    height: 60px;
    max-width: 1200px;
    margin: 0 auto;

    .logo {
      @include flex-start;
      gap: 12px;

      img {
        width: 32px;
        height: 32px;
      }

      .logo-text {
        font-size: 18px;
        font-weight: 600;
        color: rgb(var(--art-text-primary));
      }
    }

    .nav-menu {
      flex: 1;
      margin: 0 40px;
    }

    .header-actions {
      @include flex-start;
      gap: 16px;
    }
  }

  .mobile-menu {
    border-top: 1px solid rgb(var(--art-border-light));
    padding: 8px 0;
  }
}

.layout-main {
  flex: 1;

  .main-content {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 60px);

    .sidebar {
      width: 240px;
      background-color: rgb(var(--art-main-bg-color));
      border-right: 1px solid rgb(var(--art-border-light));
      padding: 16px 0;
    }

    .content-area {
      flex: 1;
      display: flex;
      flex-direction: column;

      .content-header {
        padding: 16px 20px;
        border-bottom: 1px solid rgb(var(--art-border-light));
        background-color: rgb(var(--art-main-bg-color));
      }

      .content-body {
        flex: 1;
        padding: 20px;
        background-color: rgb(var(--art-bg-color));
      }
    }
  }
}

// 响应式适配
@include respond-below(tablet) {
  .layout-header {
    .header-content {
      padding: 0 12px;

      .logo {
        .logo-text {
          display: none;
        }
      }
    }
  }

  .layout-main {
    .main-content {
      flex-direction: column;

      .content-area {
        .content-header {
          padding: 12px 16px;
        }

        .content-body {
          padding: 16px;
        }
      }
    }
  }
}

@include respond-below(mobile) {
  .layout-header {
    .header-content {
      padding: 0 8px;
      height: 50px;

      .logo {
        img {
          width: 28px;
          height: 28px;
        }
      }
    }
  }

  .layout-main {
    .main-content {
      .content-area {
        .content-header {
          padding: 8px 12px;
        }

        .content-body {
          padding: 12px;
        }
      }
    }
  }
}

// 暗黑模式适配
html.dark {
  .layout-header {
    background-color: rgb(var(--art-main-bg-color));
    border-bottom-color: rgb(var(--art-border-light));

    .mobile-menu {
      border-top-color: rgb(var(--art-border-light));
    }
  }

  .layout-main {
    .main-content {
      .sidebar {
        background-color: rgb(var(--art-main-bg-color));
        border-right-color: rgb(var(--art-border-light));
      }

      .content-area {
        .content-header {
          background-color: rgb(var(--art-main-bg-color));
          border-bottom-color: rgb(var(--art-border-light));
        }

        .content-body {
          background-color: rgb(var(--art-bg-color));
        }
      }
    }
  }
}
```

### 9.3 响应式表格处理

```vue
<template>
  <div class="responsive-table">
    <!-- 桌面端表格 -->
    <ArtTable
      v-if="isDesktop"
      :loading="isLoading"
      :data="tableData"
      :columns="desktopColumns"
      :pagination="paginationState"
    />

    <!-- 移动端卡片列表 -->
    <div v-else class="mobile-card-list">
      <div
        v-for="item in tableData"
        :key="item.id"
        class="mobile-card"
      >
        <div class="card-header">
          <h4 class="card-title">{{ item.name }}</h4>
          <ElTag :type="getStatusType(item.status)">
            {{ getStatusText(item.status) }}
          </ElTag>
        </div>

        <div class="card-content">
          <div class="card-item">
            <span class="label">类型:</span>
            <span class="value">{{ item.type }}</span>
          </div>
          <div class="card-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDateTime(item.created_at) }}</span>
          </div>
          <div class="card-item">
            <span class="label">描述:</span>
            <span class="value">{{ item.description || '-' }}</span>
          </div>
        </div>

        <div class="card-actions">
          <ElButton size="small" @click="handleEdit(item)">编辑</ElButton>
          <ElButton size="small" type="danger" @click="handleDelete(item)">删除</ElButton>
        </div>
      </div>

      <!-- 移动端分页 -->
      <div class="mobile-pagination">
        <ElPagination
          v-model:current-page="paginationState.current"
          v-model:page-size="paginationState.size"
          :total="paginationState.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          small
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'

const { width } = useWindowSize()

// 响应式状态
const isMobile = computed(() => width.value < 768)
const isTablet = computed(() => width.value >= 768 && width.value < 1024)
const isDesktop = computed(() => width.value >= 1024)

// 桌面端列配置
const desktopColumns = ref([
  {
    type: 'selection',
    width: 55,
    fixed: 'left'
  },
  {
    prop: 'name',
    label: '名称',
    minWidth: 150
  },
  {
    prop: 'type',
    label: '类型',
    width: 120
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    formatter: (row) => {
      return h(ElTag, { type: getStatusType(row.status) }, () => getStatusText(row.status))
    }
  },
  {
    prop: 'created_at',
    label: '创建时间',
    width: 180,
    formatter: (row) => formatDateTime(row.created_at)
  },
  {
    prop: 'operation',
    label: '操作',
    width: 120,
    fixed: 'right',
    formatter: (row) =>
      h('div', { class: 'flex gap-2' }, [
        h(ElButton, { size: 'small', onClick: () => handleEdit(row) }, () => '编辑'),
        h(ElButton, { size: 'small', type: 'danger', onClick: () => handleDelete(row) }, () => '删除')
      ])
  }
])
</script>

<style scoped lang="scss">
.responsive-table {
  .mobile-card-list {
    .mobile-card {
      @include card-style;
      margin-bottom: 16px;
      padding: 16px;

      .card-header {
        @include flex-between;
        margin-bottom: 12px;

        .card-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: rgb(var(--art-text-primary));
        }
      }

      .card-content {
        margin-bottom: 16px;

        .card-item {
          @include flex-between;
          padding: 8px 0;
          border-bottom: 1px solid rgb(var(--art-border-light));

          &:last-child {
            border-bottom: none;
          }

          .label {
            color: rgb(var(--art-text-secondary));
            font-size: 14px;
            min-width: 80px;
          }

          .value {
            color: rgb(var(--art-text-primary));
            font-size: 14px;
            text-align: right;
            flex: 1;
            @include text-ellipsis;
          }
        }
      }

      .card-actions {
        @include flex-end;
        gap: 8px;
      }
    }

    .mobile-pagination {
      margin-top: 20px;
      text-align: center;
    }
  }
}

// 暗黑模式适配
html.dark {
  .responsive-table {
    .mobile-card-list {
      .mobile-card {
        background-color: rgb(var(--art-card-bg-color));
        border-color: rgb(var(--art-border-light));

        .card-content {
          .card-item {
            border-bottom-color: rgb(var(--art-border-light));
          }
        }
      }
    }
  }
}
```

## 10. 开发工作流程和最佳实践

### 10.1 开发环境搭建

```bash
# 1. 克隆项目
git clone <repository-url>
cd DpTestPlatform

# 2. 安装依赖
cd frontend
npm install
# 或者使用 yarn
yarn install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件，配置API地址等

# 4. 启动开发服务器
npm run dev
# 或者
yarn dev

# 5. 构建生产版本
npm run build
# 或者
yarn build
```

### 10.2 代码提交规范

```bash
# Git提交消息格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      Bug修复
docs:     文档更新
style:    代码格式化（不影响代码运行的变动）
refactor: 代码重构（既不是新增功能，也不是修复bug的代码变动）
test:     增加测试
chore:    构建过程或辅助工具的变动
perf:     性能优化
ci:       持续集成相关

# 示例
feat(user): 添加用户头像上传功能
fix(auth): 修复Token过期时间计算错误
docs(api): 更新用户API文档
style(component): 统一组件样式格式
refactor(store): 重构用户状态管理
test(utils): 添加工具函数单元测试
chore(deps): 升级依赖包版本
```

### 10.3 代码质量保证

#### ESLint 配置

```javascript
// eslint.config.mjs
import { defineConfig } from 'eslint-define-config'
import vue from 'eslint-plugin-vue'
import typescript from '@typescript-eslint/eslint-plugin'

export default defineConfig([
  {
    files: ['**/*.{js,ts,vue}'],
    plugins: {
      vue,
      '@typescript-eslint': typescript
    },
    rules: {
      // Vue 相关规则
      'vue/multi-word-component-names': 'off',
      'vue/no-v-html': 'off',
      'vue/require-default-prop': 'off',
      'vue/require-explicit-emits': 'off',
      'vue/html-self-closing': ['error', {
        html: {
          void: 'always',
          normal: 'never',
          component: 'always'
        }
      }],

      // TypeScript 相关规则
      '@typescript-eslint/no-unused-vars': ['error', {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_'
      }],
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',

      // 通用规则
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'prefer-const': 'error',
      'no-var': 'error',
      'object-shorthand': 'error',
      'prefer-template': 'error'
    }
  }
])
```

#### Prettier 配置

```json
// .prettierrc
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "none",
  "printWidth": 100,
  "endOfLine": "lf",
  "vueIndentScriptAndStyle": false,
  "htmlWhitespaceSensitivity": "ignore"
}
```

### 10.4 性能优化指南

#### 组件懒加载

```typescript
// 路由懒加载
const routes = [
  {
    path: '/user',
    name: 'UserManagement',
    component: () => import('@/views/system/user/index.vue')
  }
]

// 组件懒加载
const LazyComponent = defineAsyncComponent(() => import('@/components/heavy-component.vue'))
```

#### 图片优化

```vue
<template>
  <!-- 响应式图片 -->
  <picture>
    <source
      media="(max-width: 768px)"
      srcset="/images/mobile-banner.webp"
      type="image/webp"
    />
    <source
      media="(max-width: 768px)"
      srcset="/images/mobile-banner.jpg"
      type="image/jpeg"
    />
    <source
      srcset="/images/desktop-banner.webp"
      type="image/webp"
    />
    <img
      src="/images/desktop-banner.jpg"
      alt="Banner"
      loading="lazy"
      decoding="async"
    />
  </picture>

  <!-- 懒加载图片 -->
  <img
    v-lazy="imageUrl"
    alt="Lazy loaded image"
    class="lazy-image"
  />
</template>
```

#### 虚拟滚动

```vue
<template>
  <!-- 大数据量列表使用虚拟滚动 -->
  <VirtualList
    :items="largeDataList"
    :item-height="60"
    :container-height="400"
    v-slot="{ item, index }"
  >
    <div class="list-item">
      <span>{{ item.name }}</span>
      <span>{{ item.description }}</span>
    </div>
  </VirtualList>
</template>
```

### 10.5 错误处理和调试

#### 全局错误处理

```typescript
// main.ts
import { createApp } from 'vue'
import App from './App.vue'

const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('组件信息:', vm)
  console.error('错误信息:', info)

  // 发送错误到监控服务
  if (import.meta.env.PROD) {
    // sendErrorToMonitoring(err, vm, info)
  }
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('全局警告:', msg)
  console.warn('组件信息:', vm)
  console.warn('组件追踪:', trace)
}
```

#### 开发调试工具

```typescript
// utils/debug.ts
export const debug = {
  log: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.log('[DEBUG]', ...args)
    }
  },

  warn: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.warn('[WARN]', ...args)
    }
  },

  error: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.error('[ERROR]', ...args)
    }
  },

  table: (data: any) => {
    if (import.meta.env.DEV) {
      console.table(data)
    }
  },

  time: (label: string) => {
    if (import.meta.env.DEV) {
      console.time(label)
    }
  },

  timeEnd: (label: string) => {
    if (import.meta.env.DEV) {
      console.timeEnd(label)
    }
  }
}
```

## 11. 总结

### 11.1 核心原则

1. **一致性优先**: 保持代码风格、命名规范、组件使用的一致性
2. **类型安全**: 充分利用TypeScript的类型系统，减少运行时错误
3. **组件化思维**: 合理拆分组件，提高代码复用性和可维护性
4. **性能意识**: 关注应用性能，合理使用懒加载、虚拟滚动等优化技术
5. **用户体验**: 重视响应式设计、主题适配、加载状态等用户体验细节

### 11.2 开发检查清单

#### 页面开发检查
- [ ] 使用标准页面模板结构
- [ ] 实现搜索、表格、对话框组件
- [ ] 配置正确的路由和权限
- [ ] 添加适当的加载状态和错误处理
- [ ] 实现响应式设计
- [ ] 适配暗黑/白天模式

#### 组件开发检查
- [ ] 定义清晰的Props和Emits接口
- [ ] 使用CSS变量确保主题适配
- [ ] 添加适当的TypeScript类型定义
- [ ] 实现响应式布局
- [ ] 添加必要的无障碍属性

#### 代码质量检查
- [ ] 通过ESLint和Prettier检查
- [ ] 添加适当的注释和文档
- [ ] 遵循命名规范
- [ ] 处理边界情况和错误状态
- [ ] 优化性能和用户体验

### 11.3 持续改进

这份规范将随着项目的发展和技术的更新持续完善。建议定期回顾和更新规范内容，确保其始终符合项目需求和最佳实践。

---

**文档版本**: v2.0.0
**最后更新**: 2025-01-28
**适用项目**: DpTestPlatform Frontend
**主要更新**: 综合用户管理和环境管理页面最佳实践，完善主题适配和响应式设计指南
