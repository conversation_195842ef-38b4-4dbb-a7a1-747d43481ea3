<template>
  <div class="base64-encoder">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6f4;</i>
        <h2>Base64 编码/解码工具</h2>
      </div>
      <p class="tool-description">
        文本和文件的 Base64 编码解码，支持图片预览
      </p>
    </div>

    <div class="tool-content">
      <!-- 功能选择 -->
      <div class="function-tabs">
        <div
          v-for="tab in functionTabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeFunction === tab.key }"
          @click="activeFunction = tab.key"
        >
          <i class="iconfont-sys" v-html="tab.icon"></i>
          <span>{{ tab.name }}</span>
        </div>
      </div>

      <!-- 文本编码/解码 -->
      <div v-if="activeFunction === 'text'" class="text-section">
        <div class="text-panels">
          <!-- 输入面板 -->
          <div class="panel">
            <div class="panel-header">
              <h3 class="panel-title">原始文本</h3>
              <div class="panel-actions">
                <ElButton size="small" @click="clearText">
                  <i class="iconfont-sys">&#xe622;</i>
                  清空
                </ElButton>
                <ElButton size="small" @click="pasteText">
                  <i class="iconfont-sys">&#xe623;</i>
                  粘贴
                </ElButton>
              </div>
            </div>
            
            <ElInput
              v-model="inputText"
              type="textarea"
              :rows="10"
              placeholder="请输入需要编码的文本..."
              class="text-area"
              @input="handleTextEncode"
            />
            
            <div class="text-info">
              <span>字符数：{{ inputText.length }}</span>
              <span>字节数：{{ getByteLength(inputText) }}</span>
            </div>
          </div>

          <!-- 输出面板 -->
          <div class="panel">
            <div class="panel-header">
              <h3 class="panel-title">Base64 编码</h3>
              <div class="panel-actions">
                <ElButton size="small" @click="copyEncoded" :disabled="!encodedText">
                  <i class="iconfont-sys">&#xe627;</i>
                  复制
                </ElButton>
                <ElButton size="small" @click="swapTexts">
                  <i class="iconfont-sys">&#xe632;</i>
                  交换
                </ElButton>
              </div>
            </div>
            
            <ElInput
              v-model="encodedText"
              type="textarea"
              :rows="10"
              readonly
              placeholder="编码结果将在这里显示..."
              class="text-area result-area"
            />
            
            <div class="text-info">
              <span>编码长度：{{ encodedText.length }}</span>
              <span>压缩比：{{ getCompressionRatio() }}%</span>
            </div>
          </div>
        </div>

        <!-- 编码选项 -->
        <div class="encode-options">
          <div class="option-group">
            <ElCheckbox v-model="urlSafe" @change="handleTextEncode">
              URL 安全编码
            </ElCheckbox>
            <ElCheckbox v-model="removePadding" @change="handleTextEncode">
              移除填充字符 (=)
            </ElCheckbox>
          </div>
        </div>
      </div>

      <!-- 文件编码 -->
      <div v-if="activeFunction === 'file'" class="file-section">
        <div class="file-upload">
          <div class="upload-area" @drop="handleFileDrop" @dragover.prevent @dragenter.prevent>
            <ElUpload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileSelect"
              drag
              class="upload-component"
            >
              <div class="upload-content">
                <i class="upload-icon iconfont-sys">&#xe646;</i>
                <div class="upload-text">
                  <p>点击或拖拽文件到此处</p>
                  <p class="upload-hint">支持图片、文档等各种文件类型</p>
                </div>
              </div>
            </ElUpload>
          </div>
        </div>

        <!-- 文件信息 -->
        <div v-if="fileInfo" class="file-info">
          <div class="info-header">
            <h3 class="info-title">文件信息</h3>
            <ElButton size="small" @click="clearFile">
              <i class="iconfont-sys">&#xe622;</i>
              清除文件
            </ElButton>
          </div>
          
          <div class="info-content">
            <div class="info-item">
              <label>文件名：</label>
              <span>{{ fileInfo.name }}</span>
            </div>
            <div class="info-item">
              <label>文件大小：</label>
              <span>{{ formatFileSize(fileInfo.size) }}</span>
            </div>
            <div class="info-item">
              <label>文件类型：</label>
              <span>{{ fileInfo.type || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>最后修改：</label>
              <span>{{ formatDate(fileInfo.lastModified) }}</span>
            </div>
          </div>
        </div>

        <!-- 图片预览 -->
        <div v-if="imagePreview" class="image-preview">
          <div class="preview-header">
            <h3 class="preview-title">图片预览</h3>
          </div>
          
          <div class="preview-content">
            <img :src="imagePreview" alt="预览图片" class="preview-image" />
          </div>
        </div>

        <!-- Base64 结果 -->
        <div v-if="fileBase64" class="file-result">
          <div class="result-header">
            <h3 class="result-title">Base64 编码结果</h3>
            <div class="result-actions">
              <ElButton size="small" @click="copyFileBase64">
                <i class="iconfont-sys">&#xe627;</i>
                复制编码
              </ElButton>
              <ElButton size="small" @click="copyDataUrl">
                <i class="iconfont-sys">&#xe627;</i>
                复制 Data URL
              </ElButton>
              <ElButton size="small" @click="downloadBase64">
                <i class="iconfont-sys">&#xe62b;</i>
                下载
              </ElButton>
            </div>
          </div>
          
          <ElInput
            v-model="fileBase64"
            type="textarea"
            :rows="8"
            readonly
            placeholder="文件 Base64 编码..."
            class="result-textarea"
          />
          
          <div class="result-info">
            <span>编码长度：{{ fileBase64.length }}</span>
            <span>Data URL 长度：{{ getDataUrlLength() }}</span>
          </div>
        </div>
      </div>

      <!-- Base64 解码 -->
      <div v-if="activeFunction === 'decode'" class="decode-section">
        <div class="decode-input">
          <div class="input-header">
            <h3 class="input-title">Base64 输入</h3>
            <div class="input-actions">
              <ElButton size="small" @click="clearDecode">
                <i class="iconfont-sys">&#xe622;</i>
                清空
              </ElButton>
              <ElButton size="small" @click="pasteDecode">
                <i class="iconfont-sys">&#xe623;</i>
                粘贴
              </ElButton>
            </div>
          </div>
          
          <ElInput
            v-model="decodeInput"
            type="textarea"
            :rows="6"
            placeholder="请输入 Base64 编码或 Data URL..."
            class="decode-textarea"
            @input="handleDecode"
          />
        </div>

        <!-- 解码结果 -->
        <div v-if="decodeResult" class="decode-result">
          <div class="result-header">
            <h3 class="result-title">解码结果</h3>
          </div>
          
          <!-- 文本结果 -->
          <div v-if="decodeResult.type === 'text'" class="text-result">
            <div class="result-content">
              <ElInput
                v-model="decodeResult.content"
                type="textarea"
                :rows="8"
                readonly
                class="result-textarea"
              />
            </div>
            
            <div class="result-actions">
              <ElButton size="small" @click="copyDecoded">
                <i class="iconfont-sys">&#xe627;</i>
                复制文本
              </ElButton>
            </div>
          </div>

          <!-- 图片结果 -->
          <div v-if="decodeResult.type === 'image'" class="image-result">
            <div class="decoded-image">
              <img :src="decodeResult.dataUrl" alt="解码图片" class="result-image" />
            </div>
            
            <div class="result-actions">
              <ElButton size="small" @click="downloadDecoded">
                <i class="iconfont-sys">&#xe62b;</i>
                下载图片
              </ElButton>
            </div>
          </div>

          <!-- 文件结果 -->
          <div v-if="decodeResult.type === 'file'" class="file-result">
            <div class="file-info">
              <p>检测到文件数据，大小：{{ formatFileSize(decodeResult.size) }}</p>
            </div>
            
            <div class="result-actions">
              <ElButton size="small" @click="downloadDecoded">
                <i class="iconfont-sys">&#xe62b;</i>
                下载文件
              </ElButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'Base64Encoder' })

// 功能选项
const functionTabs = [
  { key: 'text', name: '文本编码', icon: '&#xe647;' },
  { key: 'file', name: '文件编码', icon: '&#xe648;' },
  { key: 'decode', name: 'Base64解码', icon: '&#xe649;' }
]

// 响应式数据
const activeFunction = ref('text')

// 文本编码
const inputText = ref('')
const encodedText = ref('')
const urlSafe = ref(false)
const removePadding = ref(false)

// 文件编码
const fileInfo = ref<any>(null)
const fileBase64 = ref('')
const imagePreview = ref('')

// 解码
const decodeInput = ref('')
const decodeResult = ref<any>(null)

// 方法
const handleTextEncode = () => {
  if (!inputText.value) {
    encodedText.value = ''
    return
  }

  try {
    let encoded = btoa(unescape(encodeURIComponent(inputText.value)))
    
    if (urlSafe.value) {
      encoded = encoded.replace(/\+/g, '-').replace(/\//g, '_')
    }
    
    if (removePadding.value) {
      encoded = encoded.replace(/=/g, '')
    }
    
    encodedText.value = encoded
  } catch (error) {
    ElMessage.error('编码失败')
    encodedText.value = ''
  }
}

const handleFileSelect = (file: any) => {
  const selectedFile = file.raw
  processFile(selectedFile)
}

const handleFileDrop = (event: DragEvent) => {
  event.preventDefault()
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

const processFile = (file: File) => {
  fileInfo.value = {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified
  }

  const reader = new FileReader()
  
  reader.onload = (e) => {
    const result = e.target?.result as string
    const base64 = result.split(',')[1]
    fileBase64.value = base64

    // 如果是图片，显示预览
    if (file.type.startsWith('image/')) {
      imagePreview.value = result
    } else {
      imagePreview.value = ''
    }
  }

  reader.onerror = () => {
    ElMessage.error('文件读取失败')
  }

  reader.readAsDataURL(file)
}

const handleDecode = () => {
  if (!decodeInput.value.trim()) {
    decodeResult.value = null
    return
  }

  try {
    let base64Data = decodeInput.value.trim()
    let mimeType = ''

    // 检查是否是 Data URL
    if (base64Data.startsWith('data:')) {
      const parts = base64Data.split(',')
      if (parts.length === 2) {
        const header = parts[0]
        base64Data = parts[1]
        
        // 提取 MIME 类型
        const mimeMatch = header.match(/data:([^;]+)/)
        if (mimeMatch) {
          mimeType = mimeMatch[1]
        }
      }
    }

    // 尝试解码
    const decoded = atob(base64Data)
    
    // 判断内容类型
    if (mimeType.startsWith('image/') || isImageData(decoded)) {
      // 图片数据
      const dataUrl = mimeType ? 
        `data:${mimeType};base64,${base64Data}` : 
        `data:image/png;base64,${base64Data}`
      
      decodeResult.value = {
        type: 'image',
        dataUrl,
        content: decoded,
        size: decoded.length
      }
    } else if (isTextData(decoded)) {
      // 文本数据
      const text = decodeURIComponent(escape(decoded))
      decodeResult.value = {
        type: 'text',
        content: text,
        size: decoded.length
      }
    } else {
      // 其他文件数据
      decodeResult.value = {
        type: 'file',
        content: decoded,
        size: decoded.length,
        mimeType
      }
    }
  } catch (error) {
    ElMessage.error('解码失败，请检查 Base64 格式')
    decodeResult.value = null
  }
}

const isImageData = (data: string): boolean => {
  // 检查常见图片文件头
  const imageHeaders = [
    '\xFF\xD8\xFF', // JPEG
    '\x89PNG\r\n\x1A\n', // PNG
    'GIF87a', // GIF87a
    'GIF89a', // GIF89a
    'RIFF' // WebP (需要进一步检查)
  ]
  
  return imageHeaders.some(header => data.startsWith(header))
}

const isTextData = (data: string): boolean => {
  // 检查是否包含可打印字符
  for (let i = 0; i < Math.min(data.length, 100); i++) {
    const code = data.charCodeAt(i)
    if (code < 32 && code !== 9 && code !== 10 && code !== 13) {
      return false
    }
  }
  return true
}

const getByteLength = (str: string): number => {
  return new Blob([str]).size
}

const getCompressionRatio = (): string => {
  if (!inputText.value || !encodedText.value) return '0'
  const ratio = (1 - encodedText.value.length / getByteLength(inputText.value)) * 100
  return ratio.toFixed(1)
}

const getDataUrlLength = (): number => {
  if (!fileBase64.value || !fileInfo.value) return 0
  const mimeType = fileInfo.value.type || 'application/octet-stream'
  return `data:${mimeType};base64,${fileBase64.value}`.length
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const clearText = () => {
  inputText.value = ''
  encodedText.value = ''
}

const pasteText = async () => {
  try {
    const text = await navigator.clipboard.readText()
    inputText.value = text
    handleTextEncode()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const copyEncoded = async () => {
  if (!encodedText.value) return
  
  try {
    await navigator.clipboard.writeText(encodedText.value)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const swapTexts = () => {
  const temp = inputText.value
  inputText.value = encodedText.value
  encodedText.value = temp
  
  // 尝试解码
  if (inputText.value) {
    try {
      const decoded = atob(inputText.value)
      encodedText.value = decodeURIComponent(escape(decoded))
    } catch (error) {
      ElMessage.error('解码失败')
    }
  }
  
  ElMessage.success('文本已交换')
}

const clearFile = () => {
  fileInfo.value = null
  fileBase64.value = ''
  imagePreview.value = ''
}

const copyFileBase64 = async () => {
  if (!fileBase64.value) return
  
  try {
    await navigator.clipboard.writeText(fileBase64.value)
    ElMessage.success('复制编码成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const copyDataUrl = async () => {
  if (!fileBase64.value || !fileInfo.value) return
  
  const mimeType = fileInfo.value.type || 'application/octet-stream'
  const dataUrl = `data:${mimeType};base64,${fileBase64.value}`
  
  try {
    await navigator.clipboard.writeText(dataUrl)
    ElMessage.success('复制 Data URL 成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadBase64 = () => {
  if (!fileBase64.value || !fileInfo.value) return
  
  const blob = new Blob([fileBase64.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${fileInfo.value.name}.base64.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('下载成功')
}

const clearDecode = () => {
  decodeInput.value = ''
  decodeResult.value = null
}

const pasteDecode = async () => {
  try {
    const text = await navigator.clipboard.readText()
    decodeInput.value = text
    handleDecode()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const copyDecoded = async () => {
  if (!decodeResult.value?.content) return
  
  try {
    await navigator.clipboard.writeText(decodeResult.value.content)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadDecoded = () => {
  if (!decodeResult.value) return
  
  let blob: Blob
  let filename: string
  
  if (decodeResult.value.type === 'image') {
    // 从 Data URL 创建 Blob
    const byteCharacters = atob(decodeResult.value.dataUrl.split(',')[1])
    const byteNumbers = new Array(byteCharacters.length)
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    const byteArray = new Uint8Array(byteNumbers)
    blob = new Blob([byteArray], { type: 'image/png' })
    filename = 'decoded_image.png'
  } else if (decodeResult.value.type === 'text') {
    blob = new Blob([decodeResult.value.content], { type: 'text/plain' })
    filename = 'decoded_text.txt'
  } else {
    // 文件数据
    const byteCharacters = decodeResult.value.content
    const byteNumbers = new Array(byteCharacters.length)
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    const byteArray = new Uint8Array(byteNumbers)
    blob = new Blob([byteArray], { type: decodeResult.value.mimeType || 'application/octet-stream' })
    filename = 'decoded_file'
  }
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('下载成功')
}
</script>

<style lang="scss" scoped>
.base64-encoder {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .function-tabs {
      display: flex;
      gap: 8px;
      margin-bottom: 24px;
      padding: 4px;
      background: var(--el-fill-color-light);
      border-radius: 8px;

      .tab-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-regular);

        &:hover {
          background: var(--el-fill-color);
        }

        &.active {
          background: var(--el-color-primary);
          color: white;
        }

        i {
          font-size: 16px;
        }
      }
    }

    .text-section,
    .file-section,
    .decode-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 24px;
    }

    .text-section {
      .text-panels {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;

        .panel {
          .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            .panel-title {
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              margin: 0;
            }

            .panel-actions {
              display: flex;
              gap: 8px;
            }
          }

          .text-area {
            :deep(.el-textarea__inner) {
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 13px;
              line-height: 1.5;
            }

            &.result-area {
              :deep(.el-textarea__inner) {
                background: var(--el-fill-color-lighter);
              }
            }
          }

          .text-info {
            display: flex;
            gap: 16px;
            margin-top: 8px;
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }
      }

      .encode-options {
        .option-group {
          display: flex;
          gap: 20px;
        }
      }
    }

    .file-section {
      .file-upload {
        margin-bottom: 24px;

        .upload-area {
          .upload-component {
            width: 100%;

            :deep(.el-upload-dragger) {
              width: 100%;
              height: 200px;
              border: 2px dashed var(--el-border-color);
              border-radius: 8px;
              background: var(--el-fill-color-lighter);
              transition: all 0.3s ease;

              &:hover {
                border-color: var(--el-color-primary);
                background: var(--el-color-primary-light-9);
              }
            }

            .upload-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100%;

              .upload-icon {
                font-size: 48px;
                color: var(--el-color-primary);
                margin-bottom: 16px;
              }

              .upload-text {
                text-align: center;

                p {
                  margin: 0 0 4px 0;
                  font-size: 16px;
                  color: var(--el-text-color-primary);

                  &.upload-hint {
                    font-size: 14px;
                    color: var(--el-text-color-regular);
                  }
                }
              }
            }
          }
        }
      }

      .file-info,
      .image-preview,
      .file-result {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-header,
        .preview-header,
        .result-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .info-title,
          .preview-title,
          .result-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }

          .result-actions {
            display: flex;
            gap: 8px;
          }
        }

        .info-content {
          .info-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 4px;
            margin-bottom: 8px;

            label {
              width: 80px;
              font-size: 13px;
              font-weight: 500;
              color: var(--el-text-color-regular);
              flex-shrink: 0;
            }

            span {
              flex: 1;
              font-size: 13px;
              color: var(--el-text-color-primary);
              word-break: break-all;
            }
          }
        }

        .preview-content {
          text-align: center;
          padding: 16px;
          border: 1px solid var(--el-border-color);
          border-radius: 6px;
          background: var(--el-fill-color-lighter);

          .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }

        .result-textarea {
          :deep(.el-textarea__inner) {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
          }
        }

        .result-info {
          display: flex;
          gap: 16px;
          margin-top: 8px;
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }

    .decode-section {
      .decode-input {
        margin-bottom: 24px;

        .input-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .input-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }

          .input-actions {
            display: flex;
            gap: 8px;
          }
        }

        .decode-textarea {
          :deep(.el-textarea__inner) {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.5;
          }
        }
      }

      .decode-result {
        .result-header {
          margin-bottom: 16px;

          .result-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .text-result,
        .image-result,
        .file-result {
          .result-content {
            margin-bottom: 12px;

            .result-textarea {
              :deep(.el-textarea__inner) {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 13px;
                line-height: 1.5;
              }
            }
          }

          .decoded-image {
            text-align: center;
            padding: 16px;
            border: 1px solid var(--el-border-color);
            border-radius: 6px;
            background: var(--el-fill-color-lighter);
            margin-bottom: 12px;

            .result-image {
              max-width: 100%;
              max-height: 400px;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
          }

          .file-info {
            padding: 12px 16px;
            background: var(--el-fill-color-light);
            border: 1px solid var(--el-border-color);
            border-radius: 6px;
            margin-bottom: 12px;

            p {
              margin: 0;
              font-size: 14px;
              color: var(--el-text-color-primary);
            }
          }

          .result-actions {
            display: flex;
            gap: 8px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .base64-encoder {
    .tool-content {
      .function-tabs {
        .tab-item {
          flex-direction: column;
          gap: 4px;
          padding: 8px 12px;

          span {
            font-size: 12px;
          }
        }
      }

      .text-section {
        .text-panels {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }

      .file-section {
        .file-upload {
          .upload-area {
            .upload-component {
              :deep(.el-upload-dragger) {
                height: 150px;
              }

              .upload-content {
                .upload-icon {
                  font-size: 36px;
                  margin-bottom: 12px;
                }

                .upload-text {
                  p {
                    font-size: 14px;

                    &.upload-hint {
                      font-size: 12px;
                    }
                  }
                }
              }
            }
          }
        }

        .file-info {
          .info-content {
            .info-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              label {
                width: auto;
              }
            }
          }
        }

        .file-result {
          .result-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .result-actions {
              width: 100%;
              justify-content: flex-start;
            }
          }
        }
      }

      .decode-section {
        .decode-result {
          .text-result,
          .image-result,
          .file-result {
            .result-actions {
              flex-direction: column;
              align-items: stretch;
            }
          }
        }
      }
    }
  }
}
</style>
