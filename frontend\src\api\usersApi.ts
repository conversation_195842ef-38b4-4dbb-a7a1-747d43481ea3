import request from '@/utils/http'

export class UserService {
  // 登录
  static async login(params: Api.Auth.LoginParams): Promise<Api.Auth.LoginResponse> {
    const response = await request.post<Api.Auth.LoginResponse>({
      url: '/api/auth/login',
      params
    })
    return response
  }

  // 获取用户信息
  static async getUserInfo(): Promise<Api.User.UserInfo> {
    const response = await request.get<Api.User.UserInfo>({
      url: '/api/user/info'
    })
    return response
  }

  // 获取用户列表
  static async getUserList(params: Api.Common.PaginatingSearchParams): Promise<Api.User.UserListData> {
    const response = await request.get<Api.User.UserListData>({
      url: '/api/user/list',
      params
    })
    return response
  }

  // 刷新令牌
  static async refreshToken(refreshToken: string): Promise<Api.Auth.LoginResponse> {
    const response = await request.post<Api.Auth.LoginResponse>({
      url: '/api/auth/refresh',
      params: {
        refresh_token: refreshToken
      }
    })
    return response
  }

  // 用户登出
  static async logout(): Promise<void> {
    await request.post<void>({
      url: '/api/auth/logout'
    })
  }

  // 更新用户资料
  static async updateProfile(data: Partial<Api.User.UserInfo>): Promise<void> {
    await request.put<void>({
      url: '/api/user/profile',
      params: data
    })
  }

  // 更新用户头像
  static async updateAvatar(avatarUrl: string): Promise<Api.Common.BaseResponse> {
    const response = await request.put<Api.Common.BaseResponse>({
      url: '/api/user/avatar',
      params: { avatar_url: avatarUrl },
      data: {} // 明确指定data为空对象，避免params被移动到data
    })
    return response
  }

  // 新增用户
  static async addUser(data: Api.User.UserCreateParams): Promise<Api.Common.BaseResponse> {
    const response = await request.post<Api.Common.BaseResponse>({
      url: '/api/user/add',
      params: data
    })
    return response
  }

  // 更新用户信息
  static async updateUser(userId: number, data: Api.User.UserUpdateParams): Promise<Api.Common.BaseResponse> {
    const response = await request.put<Api.Common.BaseResponse>({
      url: `/api/user/${userId}`,
      params: data
    })
    return response
  }

  // 获取角色列表
  static async getRoleList(): Promise<Api.Common.BaseResponse> {
    const response = await request.get<Api.Common.BaseResponse>({
      url: '/api/role/list'
    })
    return response
  }

  // 删除用户
  static async deleteUser(userId: number): Promise<Api.Common.BaseResponse> {
    const response = await request.del<Api.Common.BaseResponse>({
      url: `/api/user/${userId}`
    })
    return response
  }

  // 更新用户状态
  static async updateUserStatus(userId: number, status: string): Promise<Api.Common.BaseResponse> {
    const response = await request.put<Api.Common.BaseResponse>({
      url: `/api/user/${userId}/status`,
      params: { status }
    })
    return response
  }
}
