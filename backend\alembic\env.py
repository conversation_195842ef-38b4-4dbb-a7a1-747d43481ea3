from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.models.base import Base
from app.models.user.user import User
from app.models.user.role import Role
from app.models.env.env import Environment
from app.models.model.model_config import ModelConfig
from app.core.config import settings

target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    # 使用应用配置中的数据库URL，如果没有则使用alembic.ini中的配置
    url = settings.DATABASE_URL or config.get_main_option("sqlalchemy.url")
    # 将异步驱动URL转换为同步驱动URL（用于Alembic迁移）
    if url and "aiomysql" in url:
        url = url.replace("mysql+aiomysql://", "mysql+pymysql://")

    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # 使用应用配置中的数据库URL
    configuration = config.get_section(config.config_ini_section, {})
    if settings.DATABASE_URL:
        # 将异步驱动URL转换为同步驱动URL（用于Alembic迁移）
        sync_url = settings.DATABASE_URL
        if "aiomysql" in sync_url:
            sync_url = sync_url.replace("mysql+aiomysql://", "mysql+pymysql://")
        configuration["sqlalchemy.url"] = sync_url

    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
