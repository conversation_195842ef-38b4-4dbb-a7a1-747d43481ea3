<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Tags显示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tag {
            display: inline-block;
            background: #409eff;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 4px;
            margin-bottom: 4px;
        }
        .env-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .env-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .env-tags {
            margin-top: 8px;
        }
        .loading {
            text-align: center;
            color: #666;
        }
        .error {
            color: #f56c6c;
            background: #fef0f0;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #fbc4c4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏷️ 环境Tags显示测试</h1>
        <div id="content">
            <div class="loading">正在加载环境列表...</div>
        </div>
    </div>

    <script>
        async function loadEnvironments() {
            try {
                const response = await fetch('http://localhost:8001/api/env/list?current=1&size=20');
                const result = await response.json();
                
                console.log('API响应:', result);
                
                if (result.success && result.data && result.data.records) {
                    displayEnvironments(result.data.records);
                } else {
                    showError('API响应格式错误: ' + JSON.stringify(result));
                }
            } catch (error) {
                console.error('加载失败:', error);
                showError('加载环境列表失败: ' + error.message);
            }
        }

        function displayEnvironments(environments) {
            const content = document.getElementById('content');
            
            if (environments.length === 0) {
                content.innerHTML = '<div class="loading">没有找到环境数据</div>';
                return;
            }

            let html = '<h2>环境列表 (' + environments.length + ' 个)</h2>';
            
            environments.forEach(env => {
                html += `
                    <div class="env-item">
                        <div class="env-name">🌐 ${env.name} (ID: ${env.id})</div>
                        <div><strong>类型:</strong> ${env.type}</div>
                        <div><strong>描述:</strong> ${env.description || '-'}</div>
                        <div><strong>Tags字符串:</strong> "${env.tags || ''}"</div>
                        <div><strong>Tag_list数组:</strong> ${JSON.stringify(env.tag_list || [])}</div>
                        <div class="env-tags">
                            <strong>Tags显示:</strong> 
                            ${renderTags(env.tag_list)}
                        </div>
                    </div>
                `;
            });
            
            content.innerHTML = html;
        }

        function renderTags(tagList) {
            if (!tagList || !Array.isArray(tagList) || tagList.length === 0) {
                return '<span style="color: #999;">-</span>';
            }
            
            let html = '';
            const displayTags = tagList.slice(0, 3); // 显示前3个
            
            displayTags.forEach(tag => {
                html += `<span class="tag">${tag}</span>`;
            });
            
            if (tagList.length > 3) {
                html += `<span style="color: #999; font-size: 12px;">+${tagList.length - 3}</span>`;
            }
            
            return html;
        }

        function showError(message) {
            const content = document.getElementById('content');
            content.innerHTML = `<div class="error">${message}</div>`;
        }

        // 页面加载时执行
        window.addEventListener('load', loadEnvironments);
    </script>
</body>
</html>
