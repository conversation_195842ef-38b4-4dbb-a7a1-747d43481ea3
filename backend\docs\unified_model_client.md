# 统一模型客户端使用指南

## 概述

DpTestPlatform 现在使用统一的模型客户端架构，基于 OpenAI 官方 Python 库提供统一的模型调用接口。这个架构支持多种 AI 平台，包括国内外主流的大模型服务。

## 核心特性

### 🚀 统一接口
- 使用 OpenAI 库作为统一的客户端基础
- 支持所有 OpenAI 兼容的 API 接口
- 统一的错误处理和重试机制
- 类型安全和完整的文档支持

### 🌐 多平台支持
支持以下平台：

**国外平台：**
- OpenAI (GPT-3.5, GPT-4, GPT-4o 等)
- <PERSON> (Anthropic)
- Azure OpenAI
- Google AI
- HuggingFace
- 本地模型
- 自定义平台

**国内平台：**
- 豆包 (字节跳动)
- DeepSeek
- 通义千问 (阿里巴巴)
- 文心一言 (百度)
- 智谱AI (ChatGLM)
- 月之暗面 (Kimi)
- 百川智能
- 商汤科技

### 🔧 适配器模式
每个平台都有专门的适配器，负责：
- API URL 配置
- 认证头设置
- 模型名称映射
- 消息格式转换

## 架构设计

```
UnifiedModelClient
├── PlatformAdapter (抽象基类)
│   ├── OpenAIAdapter
│   ├── ClaudeAdapter
│   ├── AzureAdapter
│   ├── LocalAdapter
│   └── CustomAdapter
└── AsyncOpenAI (OpenAI 官方库)
```

## 使用方法

### 1. 基本使用

```python
from app.models.model.model_config import ModelConfig
from app.utils.unified_model_client import UnifiedModelClient

# 创建模型配置
config = ModelConfig(
    name="my-openai-model",
    platform="openai",
    api_url="https://api.openai.com/v1",
    api_key_encrypted="your-api-key",
    config={"model": "gpt-3.5-turbo"}
)

# 创建客户端
client = UnifiedModelClient(config)

# 健康检查
health_result = await client.health_check()
print(f"健康状态: {health_result['is_healthy']}")

# 调用模型
response = await client.call_model(
    prompt="Hello, how are you?",
    parameters={
        "max_tokens": 100,
        "temperature": 0.7
    }
)
print(f"模型响应: {response['response']}")

# 关闭客户端
await client.close()
```

### 2. 不同平台配置示例

#### OpenAI
```python
config = ModelConfig(
    platform="openai",
    api_url="https://api.openai.com/v1",
    api_key_encrypted="sk-...",
    config={"model": "gpt-4"}
)
```

#### Claude
```python
config = ModelConfig(
    platform="claude",
    api_url="https://api.anthropic.com/v1",
    api_key_encrypted="sk-ant-...",
    config={"model": "claude-3-sonnet-20240229"}
)
```

#### Azure OpenAI
```python
config = ModelConfig(
    platform="azure",
    api_url="https://your-resource.openai.azure.com",
    api_key_encrypted="your-azure-key",
    config={
        "deployment_name": "gpt-35-turbo",
        "api_version": "2023-07-01-preview"
    }
)
```

#### DeepSeek
```python
config = ModelConfig(
    platform="deepseek",
    api_url="https://api.deepseek.com/v1",
    api_key_encrypted="sk-...",
    config={"model": "deepseek-chat"}
)
```

#### 通义千问
```python
config = ModelConfig(
    platform="qwen",
    api_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    api_key_encrypted="sk-...",
    config={"model": "qwen-turbo"}
)
```

### 3. 在服务层中使用

```python
from app.utils.model_client import ModelClientFactory

# 创建客户端工厂
factory = ModelClientFactory()

# 创建客户端（自动使用统一客户端）
client = factory.create_client(model_config)

# 使用客户端
health_result = await client.health_check()
call_result = await client.call_model(prompt, parameters)
```

## 配置参数

### 模型配置字段

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `name` | string | ✅ | 模型唯一标识 |
| `platform` | string | ✅ | 平台类型 |
| `api_url` | string | ✅ | API 基础地址 |
| `api_key_encrypted` | string | ✅ | 加密的 API 密钥 |
| `timeout_seconds` | int | ❌ | 超时时间（默认30秒） |
| `config` | dict | ❌ | 平台特定配置 |

### 平台特定配置

#### OpenAI/兼容平台
```json
{
  "model": "gpt-3.5-turbo",
  "use_unified_client": true
}
```

#### Azure OpenAI
```json
{
  "deployment_name": "gpt-35-turbo",
  "api_version": "2023-07-01-preview"
}
```

#### 自定义平台
```json
{
  "model": "custom-model",
  "headers": {
    "Custom-Header": "value"
  }
}
```

## 错误处理

统一客户端提供完整的错误处理：

```python
try:
    result = await client.call_model(prompt)
    if result['success']:
        print(f"成功: {result['response']}")
    else:
        print(f"失败: {result['error_message']}")
except Exception as e:
    print(f"异常: {str(e)}")
```

### 常见错误类型

- `AuthenticationError`: 认证失败（API 密钥错误）
- `RateLimitError`: 请求频率限制
- `APIConnectionError`: 网络连接错误
- `APITimeoutError`: 请求超时
- `APIStatusError`: HTTP 状态错误

## 性能优化

### 1. 连接复用
客户端会自动复用 HTTP 连接，提高性能。

### 2. 异步处理
所有操作都是异步的，支持高并发。

### 3. 超时控制
可以为每个请求设置独立的超时时间。

### 4. 资源管理
使用 `async with` 或手动调用 `close()` 确保资源正确释放。

## 迁移指南

### 从旧客户端迁移

1. **无需修改业务代码**：统一客户端通过适配器保持向后兼容
2. **配置更新**：可选择启用统一客户端（默认启用）
3. **性能提升**：自动获得更好的错误处理和性能

### 禁用统一客户端

如需使用旧客户端：

```python
config = ModelConfig(
    # ... 其他配置
    config={
        "use_unified_client": False,
        # ... 其他配置
    }
)
```

## 最佳实践

1. **使用异步上下文管理器**：
   ```python
   async with UnifiedModelClient(config) as client:
       result = await client.call_model(prompt)
   ```

2. **合理设置超时**：
   ```python
   config.timeout_seconds = 30  # 根据模型响应时间调整
   ```

3. **错误重试**：
   ```python
   for attempt in range(3):
       try:
           result = await client.call_model(prompt)
           break
       except Exception as e:
           if attempt == 2:
               raise
           await asyncio.sleep(1)
   ```

4. **监控和日志**：
   ```python
   result = await client.health_check()
   logger.info(f"健康检查: {result['is_healthy']}, 响应时间: {result['response_time_ms']}ms")
   ```

## 故障排除

### 常见问题

1. **认证失败**：检查 API 密钥是否正确
2. **连接超时**：检查网络连接和 API URL
3. **模型不存在**：检查模型名称配置
4. **请求格式错误**：检查平台适配器配置

### 调试技巧

1. 启用详细日志
2. 使用健康检查测试连接
3. 检查适配器配置
4. 验证 API 密钥权限

## 更新日志

### v1.0.0 (2025-07-28)
- 🎉 首次发布统一模型客户端
- ✅ 支持 OpenAI 库集成
- 🌐 支持多平台适配器
- 🔧 完整的错误处理和重试机制
- 📚 完整的文档和示例
