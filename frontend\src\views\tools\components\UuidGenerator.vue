<template>
  <div class="uuid-generator">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6f7;</i>
        <h2>UUID/GUID 生成器</h2>
      </div>
      <p class="tool-description">
        生成各种版本的 UUID/GUID，支持批量生成和格式转换
      </p>
    </div>

    <div class="tool-content">
      <!-- 生成配置 -->
      <div class="config-section">
        <div class="config-row">
          <div class="config-item">
            <label class="config-label">UUID 版本</label>
            <ElSelect v-model="uuidVersion" @change="handleVersionChange">
              <ElOption
                v-for="version in uuidVersions"
                :key="version.value"
                :label="version.label"
                :value="version.value"
              >
                <div class="version-option">
                  <span class="version-name">{{ version.label }}</span>
                  <span class="version-desc">{{ version.description }}</span>
                </div>
              </ElOption>
            </ElSelect>
          </div>
          
          <div class="config-item">
            <label class="config-label">生成数量</label>
            <ElInputNumber
              v-model="generateCount"
              :min="1"
              :max="1000"
              :step="1"
              controls-position="right"
            />
          </div>
          
          <div class="config-item">
            <label class="config-label">输出格式</label>
            <ElSelect v-model="outputFormat" @change="handleFormatChange">
              <ElOption
                v-for="format in outputFormats"
                :key="format.value"
                :label="format.label"
                :value="format.value"
              />
            </ElSelect>
          </div>
        </div>

        <!-- 格式选项 -->
        <div class="format-options">
          <ElCheckbox v-model="uppercase" @change="handleFormatChange">
            大写字母
          </ElCheckbox>
          <ElCheckbox v-model="removeDashes" @change="handleFormatChange">
            移除连字符
          </ElCheckbox>
          <ElCheckbox v-model="addBraces" @change="handleFormatChange">
            添加花括号
          </ElCheckbox>
        </div>

        <!-- 生成按钮 -->
        <div class="generate-actions">
          <ElButton type="primary" @click="generateUuids" :loading="generating">
            <i class="iconfont-sys">&#xe64f;</i>
            生成 UUID
          </ElButton>
          <ElButton @click="clearResults">
            <i class="iconfont-sys">&#xe622;</i>
            清空结果
          </ElButton>
        </div>
      </div>

      <!-- 生成结果 -->
      <div v-if="generatedUuids.length > 0" class="results-section">
        <div class="results-header">
          <h3 class="results-title">生成结果 ({{ generatedUuids.length }} 个)</h3>
          <div class="results-actions">
            <ElButton size="small" @click="copyAllUuids">
              <i class="iconfont-sys">&#xe627;</i>
              复制全部
            </ElButton>
            <ElButton size="small" @click="downloadUuids">
              <i class="iconfont-sys">&#xe62b;</i>
              下载
            </ElButton>
            <ElButton size="small" @click="regenerateAll">
              <i class="iconfont-sys">&#xe650;</i>
              重新生成
            </ElButton>
          </div>
        </div>
        
        <div class="results-content">
          <div class="results-list">
            <div
              v-for="(uuid, index) in generatedUuids"
              :key="index"
              class="uuid-item"
            >
              <div class="uuid-index">{{ index + 1 }}</div>
              <div class="uuid-value">
                <code>{{ uuid }}</code>
              </div>
              <div class="uuid-actions">
                <ElButton size="small" text @click="copyUuid(uuid)">
                  <i class="iconfont-sys">&#xe627;</i>
                </ElButton>
                <ElButton size="small" text @click="regenerateUuid(index)">
                  <i class="iconfont-sys">&#xe650;</i>
                </ElButton>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <div class="stats-item">
            <label>总数量：</label>
            <span>{{ generatedUuids.length }}</span>
          </div>
          <div class="stats-item">
            <label>版本：</label>
            <span>{{ getCurrentVersionLabel() }}</span>
          </div>
          <div class="stats-item">
            <label>格式：</label>
            <span>{{ getCurrentFormatLabel() }}</span>
          </div>
          <div class="stats-item">
            <label>生成时间：</label>
            <span>{{ generateTime }}</span>
          </div>
        </div>
      </div>

      <!-- UUID 验证 -->
      <div class="validation-section">
        <div class="validation-header">
          <h3 class="validation-title">UUID 验证</h3>
        </div>
        
        <div class="validation-input">
          <ElInput
            v-model="validateInput"
            placeholder="请输入需要验证的 UUID..."
            @input="handleValidation"
            class="validate-input"
          />
        </div>

        <div v-if="validationResult" class="validation-result">
          <div class="validation-status" :class="{ valid: validationResult.isValid, invalid: !validationResult.isValid }">
            <i class="status-icon iconfont-sys" v-html="validationResult.isValid ? '&#xe64a;' : '&#xe64b;'"></i>
            <span class="status-text">
              {{ validationResult.isValid ? 'UUID 格式有效' : 'UUID 格式无效' }}
            </span>
          </div>
          
          <div v-if="validationResult.isValid" class="validation-details">
            <div class="detail-item">
              <label>版本：</label>
              <span>{{ validationResult.version }}</span>
            </div>
            <div class="detail-item">
              <label>变体：</label>
              <span>{{ validationResult.variant }}</span>
            </div>
            <div class="detail-item">
              <label>格式：</label>
              <span>{{ validationResult.format }}</span>
            </div>
            <div v-if="validationResult.timestamp" class="detail-item">
              <label>时间戳：</label>
              <span>{{ validationResult.timestamp }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- UUID 格式转换 -->
      <div class="conversion-section">
        <div class="conversion-header">
          <h3 class="conversion-title">格式转换</h3>
        </div>
        
        <div class="conversion-input">
          <ElInput
            v-model="conversionInput"
            placeholder="请输入需要转换格式的 UUID..."
            @input="handleConversion"
            class="convert-input"
          />
        </div>

        <div v-if="conversionResults.length > 0" class="conversion-results">
          <div
            v-for="result in conversionResults"
            :key="result.format"
            class="conversion-item"
          >
            <div class="conversion-label">{{ result.label }}：</div>
            <div class="conversion-value">
              <code>{{ result.value }}</code>
              <ElButton 
                size="small" 
                text 
                @click="copyUuid(result.value)"
                class="copy-btn"
              >
                <i class="iconfont-sys">&#xe627;</i>
              </ElButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'UuidGenerator' })

// UUID 版本
const uuidVersions = [
  { value: 'v4', label: 'UUID v4', description: '随机生成 (推荐)' },
  { value: 'v1', label: 'UUID v1', description: '基于时间戳和MAC地址' },
  { value: 'v3', label: 'UUID v3', description: '基于MD5哈希' },
  { value: 'v5', label: 'UUID v5', description: '基于SHA-1哈希' },
  { value: 'nil', label: 'Nil UUID', description: '全零UUID' }
]

// 输出格式
const outputFormats = [
  { value: 'standard', label: '标准格式' },
  { value: 'simple', label: '简单格式' },
  { value: 'braces', label: '花括号格式' },
  { value: 'urn', label: 'URN格式' }
]

// 响应式数据
const uuidVersion = ref('v4')
const generateCount = ref(5)
const outputFormat = ref('standard')
const uppercase = ref(false)
const removeDashes = ref(false)
const addBraces = ref(false)
const generating = ref(false)
const generatedUuids = ref<string[]>([])
const generateTime = ref('')

// 验证相关
const validateInput = ref('')
const validationResult = ref<any>(null)

// 转换相关
const conversionInput = ref('')
const conversionResults = ref<any[]>([])

// 方法
const generateUuid = (version: string = 'v4'): string => {
  switch (version) {
    case 'v1':
      return generateUuidV1()
    case 'v3':
      return generateUuidV3()
    case 'v4':
      return generateUuidV4()
    case 'v5':
      return generateUuidV5()
    case 'nil':
      return '00000000-0000-0000-0000-000000000000'
    default:
      return generateUuidV4()
  }
}

const generateUuidV4 = (): string => {
  if (crypto.randomUUID) {
    return crypto.randomUUID()
  }
  
  // 兼容性实现
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

const generateUuidV1 = (): string => {
  // 简化的 UUID v1 实现
  const timestamp = Date.now() * 10000 + 122192928000000000 // UUID epoch offset
  const timeHex = timestamp.toString(16).padStart(16, '0')
  const clockSeq = Math.floor(Math.random() * 0x3fff)
  const node = Array.from({ length: 6 }, () => Math.floor(Math.random() * 256).toString(16).padStart(2, '0')).join('')

  return [
    timeHex.slice(-8),
    timeHex.slice(-12, -8),
    '1' + timeHex.slice(-15, -12),
    (0x8000 | clockSeq).toString(16),
    node
  ].join('-')
}

const generateUuidV3 = (): string => {
  // 简化实现，实际应该基于 MD5
  return generateUuidV4().replace(/4/, '3').replace(/[89ab]/, '8')
}

const generateUuidV5 = (): string => {
  // 简化实现，实际应该基于 SHA-1
  return generateUuidV4().replace(/4/, '5').replace(/[89ab]/, '8')
}

const formatUuid = (uuid: string): string => {
  let formatted = uuid

  // 移除连字符
  if (removeDashes.value) {
    formatted = formatted.replace(/-/g, '')
  }

  // 大写转换
  if (uppercase.value) {
    formatted = formatted.toUpperCase()
  } else {
    formatted = formatted.toLowerCase()
  }

  // 添加花括号
  if (addBraces.value && !removeDashes.value) {
    formatted = `{${formatted}}`
  }

  // 根据输出格式调整
  switch (outputFormat.value) {
    case 'simple':
      formatted = formatted.replace(/-/g, '')
      break
    case 'braces':
      if (!formatted.startsWith('{')) {
        formatted = `{${formatted}}`
      }
      break
    case 'urn':
      formatted = `urn:uuid:${formatted.replace(/[{}]/g, '')}`
      break
  }

  return formatted
}

const generateUuids = async () => {
  generating.value = true
  
  try {
    const uuids: string[] = []
    
    for (let i = 0; i < generateCount.value; i++) {
      const uuid = generateUuid(uuidVersion.value)
      const formatted = formatUuid(uuid)
      uuids.push(formatted)
    }
    
    generatedUuids.value = uuids
    generateTime.value = new Date().toLocaleString('zh-CN')
    
    ElMessage.success(`成功生成 ${generateCount.value} 个 UUID`)
  } catch (error) {
    ElMessage.error('生成 UUID 失败')
  } finally {
    generating.value = false
  }
}

const regenerateUuid = (index: number) => {
  const uuid = generateUuid(uuidVersion.value)
  const formatted = formatUuid(uuid)
  generatedUuids.value[index] = formatted
  ElMessage.success('重新生成成功')
}

const regenerateAll = () => {
  generateUuids()
}

const clearResults = () => {
  generatedUuids.value = []
  generateTime.value = ''
}

const copyUuid = async (uuid: string) => {
  try {
    await navigator.clipboard.writeText(uuid)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const copyAllUuids = async () => {
  if (generatedUuids.value.length === 0) return
  
  const allUuids = generatedUuids.value.join('\n')
  
  try {
    await navigator.clipboard.writeText(allUuids)
    ElMessage.success('复制全部成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadUuids = () => {
  if (generatedUuids.value.length === 0) return
  
  const content = [
    `UUID 生成结果`,
    `生成时间: ${generateTime.value}`,
    `版本: ${getCurrentVersionLabel()}`,
    `数量: ${generatedUuids.value.length}`,
    '',
    ...generatedUuids.value
  ].join('\n')
  
  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `uuids_${Date.now()}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('下载成功')
}

const handleVersionChange = () => {
  if (generatedUuids.value.length > 0) {
    generateUuids()
  }
}

const handleFormatChange = () => {
  if (generatedUuids.value.length > 0) {
    // 重新格式化现有的 UUID
    generatedUuids.value = generatedUuids.value.map(uuid => {
      // 先还原为标准格式
      const clean = uuid.replace(/[{}urn:uuid]/g, '').replace(/^urn:uuid:/, '')
      const standard = clean.length === 32 ? 
        `${clean.slice(0, 8)}-${clean.slice(8, 12)}-${clean.slice(12, 16)}-${clean.slice(16, 20)}-${clean.slice(20)}` :
        clean
      return formatUuid(standard)
    })
  }
}

const handleValidation = () => {
  if (!validateInput.value.trim()) {
    validationResult.value = null
    return
  }

  const uuid = validateInput.value.trim()
  const result = validateUuid(uuid)
  validationResult.value = result
}

const validateUuid = (uuid: string): any => {
  // 移除可能的前缀和包装
  let clean = uuid.replace(/^urn:uuid:/, '').replace(/[{}]/g, '')
  
  // 检查是否是32位无连字符格式
  if (clean.length === 32 && /^[0-9a-fA-F]{32}$/.test(clean)) {
    clean = `${clean.slice(0, 8)}-${clean.slice(8, 12)}-${clean.slice(12, 16)}-${clean.slice(16, 20)}-${clean.slice(20)}`
  }
  
  // 标准 UUID 格式检查
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
  
  if (!uuidRegex.test(clean)) {
    return { isValid: false }
  }

  // 提取版本和变体信息
  const version = parseInt(clean.charAt(14), 16)
  const variant = parseInt(clean.charAt(19), 16)
  
  let versionName = 'Unknown'
  switch (version) {
    case 1: versionName = 'UUID v1 (时间戳)'; break
    case 2: versionName = 'UUID v2 (DCE安全)'; break
    case 3: versionName = 'UUID v3 (MD5哈希)'; break
    case 4: versionName = 'UUID v4 (随机)'; break
    case 5: versionName = 'UUID v5 (SHA-1哈希)'; break
  }
  
  let variantName = 'Unknown'
  if ((variant & 0x8) === 0) variantName = 'NCS'
  else if ((variant & 0xC) === 0x8) variantName = 'RFC 4122'
  else if ((variant & 0xE) === 0xC) variantName = 'Microsoft'
  else variantName = 'Reserved'

  // 检测格式
  let format = '标准格式'
  if (uuid.startsWith('urn:uuid:')) format = 'URN格式'
  else if (uuid.startsWith('{') && uuid.endsWith('}')) format = '花括号格式'
  else if (uuid.length === 32) format = '简单格式'

  const result: any = {
    isValid: true,
    version: versionName,
    variant: variantName,
    format
  }

  // 如果是 v1，尝试提取时间戳
  if (version === 1) {
    try {
      const timeHex = clean.slice(0, 8) + clean.slice(9, 13) + clean.slice(14, 18).slice(1)
      const timestamp = parseInt(timeHex, 16)
      result.timestamp = new Date(timestamp / 10000 - 12219292800000).toLocaleString('zh-CN')
    } catch (error) {
      // 忽略时间戳解析错误
    }
  }

  return result
}

const handleConversion = () => {
  if (!conversionInput.value.trim()) {
    conversionResults.value = []
    return
  }

  const uuid = conversionInput.value.trim()
  const validation = validateUuid(uuid)
  
  if (!validation.isValid) {
    conversionResults.value = []
    return
  }

  // 获取标准格式
  let clean = uuid.replace(/^urn:uuid:/, '').replace(/[{}]/g, '')
  if (clean.length === 32) {
    clean = `${clean.slice(0, 8)}-${clean.slice(8, 12)}-${clean.slice(12, 16)}-${clean.slice(16, 20)}-${clean.slice(20)}`
  }

  const results = [
    { format: 'standard', label: '标准格式', value: clean.toLowerCase() },
    { format: 'upper', label: '大写格式', value: clean.toUpperCase() },
    { format: 'simple', label: '简单格式', value: clean.replace(/-/g, '').toLowerCase() },
    { format: 'simpleUpper', label: '简单大写', value: clean.replace(/-/g, '').toUpperCase() },
    { format: 'braces', label: '花括号格式', value: `{${clean.toLowerCase()}}` },
    { format: 'bracesUpper', label: '花括号大写', value: `{${clean.toUpperCase()}}` },
    { format: 'urn', label: 'URN格式', value: `urn:uuid:${clean.toLowerCase()}` }
  ]

  conversionResults.value = results
}

const getCurrentVersionLabel = (): string => {
  const version = uuidVersions.find(v => v.value === uuidVersion.value)
  return version?.label || 'Unknown'
}

const getCurrentFormatLabel = (): string => {
  const format = outputFormats.find(f => f.value === outputFormat.value)
  let label = format?.label || 'Unknown'
  
  if (uppercase.value) label += ' (大写)'
  if (removeDashes.value) label += ' (无连字符)'
  if (addBraces.value) label += ' (花括号)'
  
  return label
}
</script>

<style lang="scss" scoped>
.uuid-generator {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .config-section,
    .results-section,
    .validation-section,
    .conversion-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }

    .config-section {
      .config-row {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-bottom: 20px;

        .config-item {
          .config-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 8px;
          }

          .version-option {
            display: flex;
            flex-direction: column;

            .version-name {
              font-weight: 600;
            }

            .version-desc {
              font-size: 12px;
              color: var(--el-text-color-regular);
            }
          }
        }
      }

      .format-options {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        flex-wrap: wrap;
      }

      .generate-actions {
        display: flex;
        gap: 12px;
      }
    }

    .results-section {
      .results-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .results-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }

        .results-actions {
          display: flex;
          gap: 8px;
        }
      }

      .results-content {
        margin-bottom: 20px;

        .results-list {
          max-height: 400px;
          overflow-y: auto;
          border: 1px solid var(--el-border-color);
          border-radius: 6px;

          .uuid-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid var(--el-border-color-lighter);

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background: var(--el-fill-color-light);
            }

            .uuid-index {
              width: 40px;
              font-size: 13px;
              font-weight: 600;
              color: var(--el-text-color-regular);
              flex-shrink: 0;
            }

            .uuid-value {
              flex: 1;
              margin: 0 12px;

              code {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                color: var(--el-color-primary);
                background: var(--el-fill-color-light);
                padding: 4px 8px;
                border-radius: 4px;
                word-break: break-all;
              }
            }

            .uuid-actions {
              display: flex;
              gap: 4px;
              flex-shrink: 0;
            }
          }
        }
      }

      .stats-section {
        display: flex;
        gap: 24px;
        flex-wrap: wrap;
        padding: 16px;
        background: var(--el-fill-color-light);
        border-radius: 6px;

        .stats-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 13px;

          label {
            color: var(--el-text-color-regular);
          }

          span {
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
        }
      }
    }

    .validation-section,
    .conversion-section {
      .validation-header,
      .conversion-header {
        margin-bottom: 16px;

        .validation-title,
        .conversion-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }
      }

      .validation-input,
      .conversion-input {
        margin-bottom: 16px;

        .validate-input,
        .convert-input {
          :deep(.el-input__inner) {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
          }
        }
      }

      .validation-result {
        .validation-status {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          border-radius: 6px;
          margin-bottom: 16px;

          &.valid {
            background: var(--el-color-success-light-9);
            border: 1px solid var(--el-color-success-light-5);

            .status-icon {
              color: var(--el-color-success);
            }

            .status-text {
              color: var(--el-color-success);
              font-weight: 600;
            }
          }

          &.invalid {
            background: var(--el-color-error-light-9);
            border: 1px solid var(--el-color-error-light-5);

            .status-icon {
              color: var(--el-color-error);
            }

            .status-text {
              color: var(--el-color-error);
              font-weight: 600;
            }
          }

          .status-icon {
            font-size: 18px;
          }

          .status-text {
            font-size: 14px;
          }
        }

        .validation-details {
          .detail-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 4px;
            margin-bottom: 8px;

            label {
              width: 80px;
              font-size: 13px;
              font-weight: 500;
              color: var(--el-text-color-regular);
              flex-shrink: 0;
            }

            span {
              flex: 1;
              font-size: 13px;
              color: var(--el-text-color-primary);
            }
          }
        }
      }

      .conversion-results {
        .conversion-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 4px;
          margin-bottom: 8px;

          .conversion-label {
            width: 100px;
            font-size: 13px;
            font-weight: 500;
            color: var(--el-text-color-regular);
            flex-shrink: 0;
          }

          .conversion-value {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;

            code {
              flex: 1;
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 13px;
              color: var(--el-color-primary);
              background: var(--el-fill-color-light);
              padding: 4px 8px;
              border-radius: 4px;
              word-break: break-all;
            }

            .copy-btn {
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .uuid-generator {
    .tool-content {
      .config-section {
        .config-row {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        .format-options {
          flex-direction: column;
          gap: 8px;
        }

        .generate-actions {
          flex-direction: column;
          align-items: stretch;
        }
      }

      .results-section {
        .results-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .results-actions {
            width: 100%;
            justify-content: flex-start;
          }
        }

        .results-content {
          .results-list {
            .uuid-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;

              .uuid-index {
                width: auto;
              }

              .uuid-value {
                width: 100%;
                margin: 0;
              }

              .uuid-actions {
                width: 100%;
                justify-content: flex-end;
              }
            }
          }
        }

        .stats-section {
          flex-direction: column;
          gap: 8px;
        }
      }

      .validation-section,
      .conversion-section {
        .validation-result {
          .validation-details {
            .detail-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              label {
                width: auto;
              }
            }
          }
        }

        .conversion-results {
          .conversion-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .conversion-label {
              width: auto;
            }

            .conversion-value {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
</style>
