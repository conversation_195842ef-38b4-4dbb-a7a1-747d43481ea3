<template>
  <ElDialog
    v-model="dialogVisible"
    title="环境统计信息"
    width="800px"
    align-center
    @opened="handleDialogOpened"
  >
    <div v-loading="isLoading" element-loading-text="加载中...">
      <!-- 总体统计 -->
      <ElRow :gutter="20" class="stats-overview">
        <ElCol :span="6">
          <ElCard shadow="never" class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ totalEnvironments }}</div>
              <div class="stats-label">总环境数</div>
            </div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard shadow="never" class="stats-card">
            <div class="stats-item">
              <div class="stats-value connected">{{ connectedEnvironments }}</div>
              <div class="stats-label">已连接</div>
            </div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard shadow="never" class="stats-card">
            <div class="stats-item">
              <div class="stats-value failed">{{ failedEnvironments }}</div>
              <div class="stats-label">连接失败</div>
            </div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard shadow="never" class="stats-card">
            <div class="stats-item">
              <div class="stats-value ratio">
                {{ connectionRatio }}%
              </div>
              <div class="stats-label">连接率</div>
            </div>
          </ElCard>
        </ElCol>
      </ElRow>

      <!-- 类型统计 -->
      <ElDivider content-position="left">类型分布</ElDivider>
      
      <ElTable :data="typeStatsData" style="width: 100%">
        <ElTableColumn prop="type" label="环境类型" width="120">
          <template #default="{ row }">
            <ElTag :type="getTypeTagType(row.type)">
              {{ getTypeDisplayName(row.type) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="total" label="总数" width="80" align="center" />
        <ElTableColumn prop="connected" label="已连接" width="80" align="center">
          <template #default="{ row }">
            <span class="connected-text">{{ row.connected }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="failed" label="失败" width="80" align="center">
          <template #default="{ row }">
            <span class="failed-text">{{ row.failed }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="连接率" width="100" align="center">
          <template #default="{ row }">
            <span>{{ calculateConnectionRatio(row.connected, row.total) }}%</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="状态分布" min-width="200">
          <template #default="{ row }">
            <div class="status-bar">
              <div 
                class="status-segment connected" 
                :style="{ width: `${(row.connected / row.total) * 100}%` }"
              ></div>
              <div 
                class="status-segment failed" 
                :style="{ width: `${(row.failed / row.total) * 100}%` }"
              ></div>
              <div 
                class="status-segment unknown" 
                :style="{ width: `${((row.total - row.connected - row.failed) / row.total) * 100}%` }"
              ></div>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 图例 -->
      <div class="legend">
        <div class="legend-item">
          <div class="legend-color connected"></div>
          <span>已连接</span>
        </div>
        <div class="legend-item">
          <div class="legend-color failed"></div>
          <span>连接失败</span>
        </div>
        <div class="legend-item">
          <div class="legend-color unknown"></div>
          <span>未知</span>
        </div>
      </div>

      <!-- 最近环境 -->
      <ElDivider content-position="left">最近环境</ElDivider>
      
      <ElTable :data="recentEnvironments" style="width: 100%">
        <ElTableColumn prop="name" label="环境名称" min-width="150" />
        <ElTableColumn prop="type" label="类型" width="100">
          <template #default="{ row }">
            <ElTag :type="getTypeTagType(row.type)" size="small">
              {{ getTypeDisplayName(row.type) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="status" label="状态" width="100">
          <template #default="{ row }">
            <ElTag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusDisplayName(row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="refreshStats" :loading="isLoading">刷新</ElButton>
        <ElButton type="primary" @click="dialogVisible = false">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useEnvironmentStore } from '@/store/business/environment/index'
import { ENVIRONMENT_TYPE_CONFIG, ENVIRONMENT_STATUS_CONFIG } from '@/types/api/environment'
import type { EnvironmentResponse } from '@/types/api/environment'

// 组件属性
interface Props {
  visible: boolean
}

// 组件事件
interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const environmentStore = useEnvironmentStore()

// 响应式数据
const isLoading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

// 统计数据
const environmentStats = computed(() => environmentStore.environmentStats)

// 总体统计计算
const totalEnvironments = computed(() => {
  if (!environmentStats.value) return 0
  return Object.values(environmentStats.value.status_stats).reduce((sum, count) => sum + count, 0)
})

const connectedEnvironments = computed(() => {
  return environmentStats.value?.status_stats?.connected || 0
})

const failedEnvironments = computed(() => {
  return environmentStats.value?.status_stats?.failed || 0
})

const connectionRatio = computed(() => {
  if (totalEnvironments.value === 0) return 0
  return Math.round((connectedEnvironments.value / totalEnvironments.value) * 100)
})

// 类型统计数据
const typeStatsData = computed(() => {
  // 使用基础统计数据
  if (!environmentStats.value?.type_stats) return []

  return Object.entries(environmentStats.value.type_stats).map(([type, total]) => {
    return {
      type,
      total,
      connected: Math.floor(total * 0.7), // 临时计算
      failed: Math.floor(total * 0.2),
      unknown: total - Math.floor(total * 0.7) - Math.floor(total * 0.2)
    }
  })
})

// 最近环境
const recentEnvironments = computed(() => {
  return [...(environmentStats.value?.recent_environments || [])]
})

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  return ENVIRONMENT_TYPE_CONFIG[type as keyof typeof ENVIRONMENT_TYPE_CONFIG]?.type || 'default'
}

// 获取类型显示名称
const getTypeDisplayName = (type: string) => {
  return ENVIRONMENT_TYPE_CONFIG[type as keyof typeof ENVIRONMENT_TYPE_CONFIG]?.text || type
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  return ENVIRONMENT_STATUS_CONFIG[status as keyof typeof ENVIRONMENT_STATUS_CONFIG]?.type || 'default'
}

// 获取状态显示名称
const getStatusDisplayName = (status: string) => {
  return ENVIRONMENT_STATUS_CONFIG[status as keyof typeof ENVIRONMENT_STATUS_CONFIG]?.text || status
}

// 计算连接率
const calculateConnectionRatio = (connected: number, total: number) => {
  if (total === 0) return 0
  return Math.round((connected / total) * 100)
}

// 格式化时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 刷新统计数据
const refreshStats = async () => {
  try {
    isLoading.value = true
    await environmentStore.fetchEnvironmentStats()
  } catch (error) {
    console.error('刷新统计数据失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 对话框打开处理
const handleDialogOpened = () => {
  refreshStats()
}
</script>

<style scoped>
.stats-overview {
  margin-bottom: 20px;
}

.stats-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.stats-item {
  text-align: center;
  padding: 10px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stats-value.connected {
  color: #67c23a;
}

.stats-value.failed {
  color: #f56c6c;
}

.stats-value.ratio {
  color: #409eff;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.connected-text {
  color: #67c23a;
  font-weight: bold;
}

.failed-text {
  color: #f56c6c;
  font-weight: bold;
}

.status-bar {
  display: flex;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  background-color: #f5f7fa;
}

.status-segment {
  height: 100%;
}

.status-segment.connected {
  background-color: #67c23a;
}

.status-segment.failed {
  background-color: #f56c6c;
}

.status-segment.unknown {
  background-color: #909399;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 15px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.connected {
  background-color: #67c23a;
}

.legend-color.failed {
  background-color: #f56c6c;
}

.legend-color.unknown {
  background-color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
