<template>
  <div class="json-tools">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6f0;</i>
        <h2>JSON 工具</h2>
      </div>
      <p class="tool-description">
        JSON 格式化、校验、压缩、路径提取等功能
      </p>
    </div>

    <div class="tool-content">
      <!-- 功能选择 -->
      <div class="function-tabs">
        <div
          v-for="tab in functionTabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeFunction === tab.key }"
          @click="activeFunction = tab.key"
        >
          <i class="iconfont-sys" v-html="tab.icon"></i>
          <span>{{ tab.name }}</span>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <div class="section-header">
          <h3 class="section-title">输入 JSON</h3>
          <div class="section-actions">
            <ElButton size="small" @click="clearInput">
              <i class="iconfont-sys">&#xe622;</i>
              清空
            </ElButton>
            <ElButton size="small" @click="pasteInput">
              <i class="iconfont-sys">&#xe623;</i>
              粘贴
            </ElButton>
            <ElButton size="small" @click="loadSample">
              <i class="iconfont-sys">&#xe629;</i>
              示例
            </ElButton>
          </div>
        </div>
        
        <ElInput
          v-model="inputJson"
          type="textarea"
          :rows="8"
          placeholder="请输入 JSON 数据..."
          class="json-textarea"
          @input="handleInputChange"
        />
        
        <!-- 错误提示 -->
        <div v-if="validationError" class="error-message">
          <i class="iconfont-sys">&#xe62a;</i>
          {{ validationError }}
        </div>
      </div>

      <!-- JSON 路径提取（仅在路径提取模式下显示） -->
      <div v-if="activeFunction === 'extract'" class="path-section">
        <div class="section-header">
          <h3 class="section-title">JSON 路径</h3>
        </div>
        
        <ElInput
          v-model="jsonPath"
          placeholder="请输入 JSON 路径，如：$.data.list[0].id"
          @input="handlePathExtract"
        >
          <template #prepend>路径</template>
        </ElInput>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <ElButton
          type="primary"
          size="large"
          @click="handleProcess"
          :loading="processing"
          class="process-button"
        >
          <i class="iconfont-sys">&#xe626;</i>
          {{ getActionButtonText() }}
        </ElButton>
      </div>

      <!-- 结果展示区域 -->
      <div v-if="result" class="result-section">
        <div class="section-header">
          <h3 class="section-title">处理结果</h3>
          <div class="section-actions">
            <ElButton size="small" @click="copyResult">
              <i class="iconfont-sys">&#xe627;</i>
              复制结果
            </ElButton>
            <ElButton v-if="activeFunction !== 'validate'" size="small" @click="downloadResult">
              <i class="iconfont-sys">&#xe62b;</i>
              下载
            </ElButton>
          </div>
        </div>
        
        <div class="result-content">
          <!-- 验证结果 -->
          <div v-if="activeFunction === 'validate'" class="validation-result">
            <div class="validation-status" :class="{ success: result.valid, error: !result.valid }">
              <i class="iconfont-sys" v-html="result.valid ? '&#xe62c;' : '&#xe62d;'"></i>
              <span>{{ result.valid ? 'JSON 格式正确' : 'JSON 格式错误' }}</span>
            </div>
            <div v-if="!result.valid && result.error" class="validation-error">
              {{ result.error }}
            </div>
            <div v-if="result.valid && result.stats" class="validation-stats">
              <div class="stat-item">
                <label>对象数量：</label>
                <span>{{ result.stats.objects }}</span>
              </div>
              <div class="stat-item">
                <label>数组数量：</label>
                <span>{{ result.stats.arrays }}</span>
              </div>
              <div class="stat-item">
                <label>字符串数量：</label>
                <span>{{ result.stats.strings }}</span>
              </div>
              <div class="stat-item">
                <label>数字数量：</label>
                <span>{{ result.stats.numbers }}</span>
              </div>
            </div>
          </div>

          <!-- 其他结果 -->
          <ElInput
            v-else
            v-model="result.output"
            type="textarea"
            :rows="12"
            readonly
            class="result-textarea"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'JsonTools' })

// 功能选项
const functionTabs = [
  { key: 'format', name: '格式化', icon: '&#xe62e;' },
  { key: 'compress', name: '压缩', icon: '&#xe62f;' },
  { key: 'validate', name: '校验', icon: '&#xe630;' },
  { key: 'extract', name: '路径提取', icon: '&#xe631;' }
]

// 响应式数据
const activeFunction = ref('format')
const inputJson = ref('')
const jsonPath = ref('$.data')
const processing = ref(false)
const validationError = ref('')
const result = ref<any>(null)

// 示例 JSON 数据
const sampleJson: { [key: string]: any } = {
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "张三",
        "age": 25,
        "email": "<EMAIL>"
      },
      {
        "id": 2,
        "name": "李四",
        "age": 30,
        "email": "<EMAIL>"
      }
    ],
    "total": 2,
    "page": 1,
    "size": 10
  },
  "timestamp": 1699123456789
}

// 方法
const handleInputChange = () => {
  validationError.value = ''
  result.value = null
  
  // 实时验证 JSON 格式
  if (inputJson.value.trim()) {
    try {
      JSON.parse(inputJson.value)
      validationError.value = ''
    } catch (error) {
      validationError.value = error instanceof Error ? error.message : 'JSON 格式错误'
    }
  }
}

const handlePathExtract = () => {
  if (activeFunction.value === 'extract' && inputJson.value.trim() && jsonPath.value.trim()) {
    handleProcess()
  }
}

const handleProcess = () => {
  if (!inputJson.value.trim()) {
    ElMessage.warning('请输入 JSON 数据')
    return
  }

  processing.value = true
  
  try {
    const jsonData = JSON.parse(inputJson.value)
    
    switch (activeFunction.value) {
      case 'format':
        result.value = {
          output: JSON.stringify(jsonData, null, 2)
        }
        break
        
      case 'compress':
        result.value = {
          output: JSON.stringify(jsonData)
        }
        break
        
      case 'validate':
        result.value = {
          valid: true,
          stats: analyzeJson(jsonData)
        }
        break
        
      case 'extract':
        if (!jsonPath.value.trim()) {
          ElMessage.warning('请输入 JSON 路径')
          return
        }
        
        const extractedValue = extractJsonPath(jsonData, jsonPath.value)
        result.value = {
          output: typeof extractedValue === 'object' 
            ? JSON.stringify(extractedValue, null, 2)
            : String(extractedValue)
        }
        break
    }
    
    ElMessage.success('处理完成')
  } catch (error) {
    if (activeFunction.value === 'validate') {
      result.value = {
        valid: false,
        error: error instanceof Error ? error.message : 'JSON 格式错误'
      }
    } else {
      ElMessage.error(error instanceof Error ? error.message : '处理失败')
      result.value = null
    }
  } finally {
    processing.value = false
  }
}

const analyzeJson = (data: any): any => {
  const stats: { [key: string]: number } = {
    objects: 0,
    arrays: 0,
    strings: 0,
    numbers: 0
  }
  
  const analyze = (obj: any) => {
    if (Array.isArray(obj)) {
      stats.arrays++
      obj.forEach(analyze)
    } else if (typeof obj === 'object' && obj !== null) {
      stats.objects++
      Object.values(obj).forEach(analyze)
    } else if (typeof obj === 'string') {
      stats.strings++
    } else if (typeof obj === 'number') {
      stats.numbers++
    }
  }
  
  analyze(data)
  return stats
}

const extractJsonPath = (data: any, path: string): any => {
  // 简单的 JSON 路径提取实现
  const parts = path.replace(/^\$\.?/, '').split(/[\.\[\]]/).filter(Boolean)
  let current = data
  
  for (const part of parts) {
    if (current === null || current === undefined) {
      throw new Error(`路径 "${path}" 不存在`)
    }
    
    if (Array.isArray(current)) {
      const index = parseInt(part)
      if (isNaN(index) || index < 0 || index >= current.length) {
        throw new Error(`数组索引 "${part}" 无效`)
      }
      current = current[index]
    } else if (typeof current === 'object') {
      if (!(part in current)) {
        throw new Error(`属性 "${part}" 不存在`)
      }
      current = current[part]
    } else {
      throw new Error(`无法在 ${typeof current} 类型上访问属性 "${part}"`)
    }
  }
  
  return current
}

const getActionButtonText = () => {
  const texts: { [key: string]: string } = {
    format: '格式化',
    compress: '压缩',
    validate: '校验',
    extract: '提取'
  }
  return texts[activeFunction.value] || '处理'
}

const clearInput = () => {
  inputJson.value = ''
  result.value = null
  validationError.value = ''
}

const pasteInput = async () => {
  try {
    const text = await navigator.clipboard.readText()
    inputJson.value = text
    handleInputChange()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const loadSample = () => {
  inputJson.value = JSON.stringify(sampleJson, null, 2)
  handleInputChange()
  ElMessage.success('已加载示例数据')
}

const copyResult = async () => {
  if (!result.value) return
  
  const text = activeFunction.value === 'validate' 
    ? `JSON 校验结果：${result.value.valid ? '格式正确' : '格式错误'}`
    : result.value.output
    
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadResult = () => {
  if (!result.value || !result.value.output) return
  
  const blob = new Blob([result.value.output], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `json_${activeFunction.value}_${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('下载成功')
}
</script>

<style lang="scss" scoped>
.json-tools {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .function-tabs {
      display: flex;
      gap: 8px;
      margin-bottom: 24px;
      padding: 4px;
      background: var(--el-fill-color-light);
      border-radius: 8px;

      .tab-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-regular);

        &:hover {
          background: var(--el-fill-color);
        }

        &.active {
          background: var(--el-color-primary);
          color: white;
        }

        i {
          font-size: 16px;
        }
      }
    }

    .input-section,
    .path-section,
    .action-section,
    .result-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }

        .section-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .json-textarea,
    .result-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }

    .error-message {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      padding: 8px 12px;
      background: var(--el-color-error-light-9);
      color: var(--el-color-error);
      border-radius: 4px;
      font-size: 13px;

      i {
        font-size: 14px;
      }
    }

    .action-section {
      text-align: center;

      .process-button {
        min-width: 120px;
        height: 40px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .validation-result {
      .validation-status {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;

        &.success {
          background: var(--el-color-success-light-9);
          color: var(--el-color-success);
        }

        &.error {
          background: var(--el-color-error-light-9);
          color: var(--el-color-error);
        }

        i {
          font-size: 18px;
        }
      }

      .validation-error {
        padding: 12px 16px;
        background: var(--el-color-error-light-9);
        color: var(--el-color-error);
        border-radius: 6px;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 13px;
        margin-bottom: 16px;
      }

      .validation-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;

        .stat-item {
          padding: 12px;
          background: var(--el-fill-color-light);
          border-radius: 6px;
          text-align: center;

          label {
            display: block;
            font-size: 12px;
            color: var(--el-text-color-regular);
            margin-bottom: 4px;
          }

          span {
            font-size: 18px;
            font-weight: 600;
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .json-tools {
    .tool-content {
      .function-tabs {
        .tab-item {
          flex-direction: column;
          gap: 4px;
          padding: 8px 12px;

          span {
            font-size: 12px;
          }
        }
      }

      .validation-result {
        .validation-stats {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    }
  }
}
</style>
