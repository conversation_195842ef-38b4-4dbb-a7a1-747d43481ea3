/**
 * 混沌测试场景状态管理
 * 专门管理场景相关的状态和操作
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usePagination } from '@/store/shared/pagination'
import { useLoading } from '@/store/shared/loading'
import { globalCache } from '@/store/shared/cache'
import ChaosService from '@/api/chaosApi'
import type {
  ChaosScenario,
  ChaosScenarioCreate,
  ChaosScenarioUpdate,
  ChaosScenarioSearchParams,
  ChaosScenarioValidateRequest,
  ChaosScenarioImportRequest
} from '@/types/api/chaos'

export const useChaosScenariosStore = defineStore('chaos-scenarios', () => {
  // ==================== 状态定义 ====================
  
  const scenarios = ref<ChaosScenario[]>([])
  const currentScenario = ref<ChaosScenario | null>(null)
  const builtinScenarios = ref<ChaosScenario[]>([])
  const popularScenarios = ref<ChaosScenario[]>([])
  
  // 分页和加载状态
  const pagination = usePagination({ defaultSize: 20 })
  const loading = useLoading()
  const builtinLoading = useLoading()
  const popularLoading = useLoading()

  // ==================== 计算属性 ====================
  
  const totalScenarios = computed(() => pagination.pagination.total)
  
  const activeScenarios = computed(() =>
    scenarios.value.filter(scenario => scenario.is_active === true)
  )

  const customScenarios = computed(() =>
    scenarios.value.filter(scenario => scenario.is_builtin === false)
  )

  const systemScenarios = computed(() =>
    scenarios.value.filter(scenario => scenario.is_builtin === true)
  )

  // ==================== 场景管理方法 ====================
  
  /**
   * 获取场景列表
   */
  const fetchScenarios = async (params: ChaosScenarioSearchParams = {}) => {
    const queryParams = {
      ...params,
      ...pagination.params.value
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getScenarioList(queryParams)
      
      scenarios.value = response.records
      pagination.updateFromResponse(response)
      
      return response
    })
  }

  /**
   * 创建场景
   */
  const createScenario = async (data: ChaosScenarioCreate) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.createScenario(data)

      // 添加到列表开头
      scenarios.value.unshift(response)
      pagination.setTotal(pagination.pagination.total + 1)

      // 清除相关缓存
      globalCache.remove('scenario_statistics')

      return response
    })
  }

  /**
   * 获取场景详情
   */
  const fetchScenario = async (id: number) => {
    // 先尝试从缓存获取
    const cacheKey = `scenario_detail_${id}`
    const cached = globalCache.get(cacheKey)
    if (cached) {
      currentScenario.value = cached
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getScenarioDetail(id)
      
      currentScenario.value = response
      
      // 缓存场景详情
      globalCache.set(cacheKey, response, 5 * 60 * 1000) // 5分钟缓存
      
      return response
    })
  }
  
  /**
   * 更新场景
   */
  const updateScenario = async (id: number, data: ChaosScenarioUpdate) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.updateScenario(id, data)
      
      // 更新本地状态
      const index = scenarios.value.findIndex(scenario => scenario.id === id)
      if (index !== -1) {
        scenarios.value[index] = response
      }
      
      if (currentScenario.value?.id === id) {
        currentScenario.value = response
      }
      
      // 清除相关缓存
      globalCache.remove(`scenario_detail_${id}`)
      
      return response
    })
  }

  /**
   * 删除场景
   */
  const deleteScenario = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.deleteScenario(id)
      
      // 从本地状态中移除
      const index = scenarios.value.findIndex(scenario => scenario.id === id)
      if (index !== -1) {
        scenarios.value.splice(index, 1)
        pagination.setTotal(pagination.pagination.total - 1)
      }
      
      if (currentScenario.value?.id === id) {
        currentScenario.value = null
      }
      
      // 清除相关缓存
      globalCache.remove(`scenario_detail_${id}`)
      globalCache.remove('scenario_statistics')
    })
  }
  
  /**
   * 验证场景参数
   */
  const validateScenarioParams = async (data: ChaosScenarioValidateRequest) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.validateScenarioParams(data)
      return response
    })
  }
  
  /**
   * 使用场景
   */
  const useScenario = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.useScenario(id)
      
      // 更新使用次数
      const scenario = scenarios.value.find(s => s.id === id)
      if (scenario) {
        scenario.usage_count = (scenario.usage_count || 0) + 1
      }
      
      if (currentScenario.value?.id === id) {
        currentScenario.value.usage_count = (currentScenario.value.usage_count || 0) + 1
      }
      
      // 清除相关缓存
      globalCache.remove(`scenario_detail_${id}`)
      globalCache.remove('popular_scenarios_10') // 清除热门场景缓存
    })
  }

  /**
   * 导入场景
   */
  const importScenarios = async (data: ChaosScenarioImportRequest) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.importScenarios(data)
      
      // 重新加载场景列表
      await fetchScenarios({})
      
      return response
    })
  }

  /**
   * 导出场景
   */
  const exportScenarios = async (scenarioIds?: number[]) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.exportScenarios(scenarioIds)
      return response
    })
  }



  /**
   * 获取场景统计
   */
  const fetchScenarioStatistics = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('scenario_statistics')
    if (cached) {
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getScenarioStatistics()
      
      // 缓存统计信息
      globalCache.set('scenario_statistics', response, 2 * 60 * 1000) // 2分钟缓存
      
      return response
    })
  }

  // ==================== 辅助方法 ====================
  
  /**
   * 重置状态
   */
  const resetState = () => {
    scenarios.value = []
    currentScenario.value = null
    builtinScenarios.value = []
    popularScenarios.value = []
    pagination.reset()
    loading.reset()
    builtinLoading.reset()
    popularLoading.reset()
  }

  return {
    // 状态
    scenarios: readonly(scenarios),
    currentScenario: readonly(currentScenario),
    builtinScenarios: readonly(builtinScenarios),
    popularScenarios: readonly(popularScenarios),
    pagination,
    loading,
    builtinLoading,
    popularLoading,
    
    // 计算属性
    totalScenarios,
    activeScenarios,
    customScenarios,
    systemScenarios,
    
    // 方法
    fetchScenarios,
    createScenario,
    fetchScenario,
    updateScenario,
    deleteScenario,
    validateScenarioParams,
    useScenario,
    importScenarios,
    exportScenarios,
    fetchScenarioStatistics,
    resetState
  }
})
