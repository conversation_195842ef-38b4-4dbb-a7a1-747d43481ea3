"""
混沌测试故障场景数据传输对象
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.schemas.base import BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema


class ChaosScenarioCreate(BaseCreateSchema):
    """
    创建故障场景请求模式
    """
    name: str = Field(..., min_length=1, max_length=100, description="场景名称")
    fault_type: str = Field(..., description="故障类型")
    description: Optional[str] = Field(None, max_length=1000, description="场景描述")
    default_params: Dict[str, Any] = Field(..., description="默认参数配置")
    param_schema: Optional[Dict[str, Any]] = Field(default_factory=dict, description="参数结构定义和验证规则")
    category: Optional[str] = Field(None, max_length=50, description="场景分类")
    tags: Optional[str] = Field(None, max_length=500, description="场景标签，逗号分隔")

    @validator('fault_type')
    def validate_fault_type(cls, v):
        """验证故障类型"""
        allowed_types = ['cpu', 'memory', 'network', 'disk', 'process', 'k8s','docker']
        if v not in allowed_types:
            raise ValueError(f'故障类型必须为: {", ".join(allowed_types)}')
        return v

    @validator('param_schema')
    def validate_param_schema(cls, v):
        """验证参数结构定义"""
        if not isinstance(v, dict):
            raise ValueError('参数结构定义必须为字典格式')
        
        # 验证每个参数的结构
        for param_name, param_def in v.items():
            if not isinstance(param_def, dict):
                raise ValueError(f'参数 {param_name} 的定义必须为字典格式')
            
            # 必须包含type字段
            if 'type' not in param_def:
                raise ValueError(f'参数 {param_name} 必须包含type字段')
            
            # 验证type值
            allowed_types = ['string', 'integer', 'float', 'boolean', 'array', 'object']
            if param_def['type'] not in allowed_types:
                raise ValueError(f'参数 {param_name} 的type必须为: {", ".join(allowed_types)}')
        
        return v


class ChaosScenarioUpdate(BaseUpdateSchema):
    """
    更新故障场景请求模式
    """
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="场景名称")
    description: Optional[str] = Field(None, max_length=1000, description="场景描述")
    default_params: Optional[Dict[str, Any]] = Field(None, description="默认参数配置")
    param_schema: Optional[Dict[str, Any]] = Field(None, description="参数结构定义和验证规则")
    is_active: Optional[bool] = Field(None, description="是否启用")
    category: Optional[str] = Field(None, max_length=50, description="场景分类")
    tags: Optional[str] = Field(None, max_length=500, description="场景标签，逗号分隔")


class ChaosScenarioResponse(BaseResponseSchema):
    """
    故障场景响应模式
    """
    name: str = Field(description="场景名称")
    fault_type: str = Field(description="故障类型")
    description: Optional[str] = Field(description="场景描述")
    default_params: Dict[str, Any] = Field(description="默认参数配置")
    param_schema: Dict[str, Any] = Field(description="参数结构定义和验证规则")
    is_builtin: bool = Field(description="是否内置模板")
    is_active: bool = Field(description="是否启用")
    category: Optional[str] = Field(description="场景分类")
    usage_count: int = Field(description="使用次数")
    tags: Optional[str] = Field(description="场景标签，逗号分隔")
    
    # 扩展信息
    tag_list: List[str] = Field(description="标签列表")


class ChaosScenarioListResponse(BaseModel):
    """
    场景列表项响应模式（简化版）
    """
    id: int = Field(description="场景ID")
    name: str = Field(description="场景名称")
    fault_type: str = Field(description="故障类型")
    description: Optional[str] = Field(description="场景描述")
    category: Optional[str] = Field(description="场景分类")
    is_builtin: bool = Field(description="是否内置模板")
    is_active: bool = Field(description="是否启用")
    usage_count: int = Field(description="使用次数")
    tag_list: List[str] = Field(description="标签列表")
    created_at: Optional[datetime] = Field(description="创建时间")
    created_by: Optional[str] = Field(description="创建者")


class ChaosScenarioSearchParams(BaseModel):
    """
    场景搜索参数
    """
    keyword: Optional[str] = Field(None, description="搜索关键词")
    fault_type: Optional[str] = Field(None, description="故障类型")
    category: Optional[str] = Field(None, description="场景分类")
    is_builtin: Optional[bool] = Field(None, description="是否内置")
    is_active: Optional[bool] = Field(None, description="是否启用")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")
    
    # 排序参数
    order_by: str = Field(default="usage_count", description="排序字段")
    desc: bool = Field(default=True, description="是否降序")


class ChaosScenarioValidateRequest(BaseModel):
    """
    场景参数验证请求
    """
    scenario_id: int = Field(..., gt=0, description="场景ID")
    params: Dict[str, Any] = Field(..., description="参数配置")


class ChaosScenarioValidateResponse(BaseModel):
    """
    场景参数验证响应
    """
    valid: bool = Field(description="是否有效")
    errors: Dict[str, str] = Field(description="错误信息")
    merged_params: Dict[str, Any] = Field(description="合并后的参数")


class ChaosScenarioStatistics(BaseModel):
    """
    场景统计信息
    """
    total_count: int = Field(description="总场景数")
    builtin_count: int = Field(description="内置场景数")
    custom_count: int = Field(description="自定义场景数")
    fault_type_stats: Dict[str, int] = Field(description="故障类型统计")
    category_stats: Dict[str, int] = Field(description="分类统计")
    popular_scenarios: List[ChaosScenarioListResponse] = Field(description="热门场景")


class ChaosScenarioTemplate(BaseModel):
    """
    场景模板定义
    """
    name: str = Field(description="模板名称")
    fault_type: str = Field(description="故障类型")
    description: str = Field(description="模板描述")
    category: str = Field(description="模板分类")
    default_params: Dict[str, Any] = Field(description="默认参数")
    param_schema: Dict[str, Any] = Field(description="参数结构")
    tags: List[str] = Field(description="标签列表")


class ChaosScenarioImportRequest(BaseModel):
    """
    场景导入请求
    """
    scenarios: List[ChaosScenarioTemplate] = Field(..., min_items=1, description="场景模板列表")
    overwrite_existing: bool = Field(default=False, description="是否覆盖已存在的场景")


class ChaosScenarioImportResponse(BaseModel):
    """
    场景导入响应
    """
    success_count: int = Field(description="成功导入数量")
    failed_count: int = Field(description="失败数量")
    skipped_count: int = Field(description="跳过数量")
    errors: List[str] = Field(description="错误信息列表")
    imported_scenarios: List[ChaosScenarioListResponse] = Field(description="成功导入的场景")


class ChaosScenarioExportResponse(BaseModel):
    """
    场景导出响应
    """
    scenarios: List[ChaosScenarioTemplate] = Field(description="场景模板列表")
    export_time: datetime = Field(description="导出时间")
    total_count: int = Field(description="导出数量")
