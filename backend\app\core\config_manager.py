"""
配置管理中心
提供统一的配置管理和客户端工厂功能
"""
from typing import Dict, Any, Type, Optional, List
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum

from app.utils.clients import (
    DatabaseClient, RedisClient, SSHClient, KafkaClient,
    MongoDBClient, ElasticsearchClient, K8sClient, APIClient
)


class EnvironmentType(str, Enum):
    """环境类型枚举"""
    DATABASE = "database"
    REDIS = "redis"
    SSH = "ssh"
    K8S = "k8s"
    API = "api"
    KAFKA = "kafka"
    MONGODB = "mongodb"
    ELASTICSEARCH = "elasticsearch"
    RABBITMQ = "rabbitmq"
    MINIO = "minio"
    OPENSEARCH = "opensearch"


@dataclass
class EnvironmentTypeConfig:
    """环境类型配置"""
    name: str
    display_name: str
    description: str
    client_class: Type
    default_port: Optional[int] = None
    required_fields: List[str] = field(default_factory=list)
    optional_fields: List[str] = field(default_factory=list)
    connection_test_timeout: int = 10
    icon: Optional[str] = None
    category: str = "general"


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self._environment_configs: Dict[str, EnvironmentTypeConfig] = {}
        self._client_factory = ClientFactory()
        self._initialize_default_configs()
    
    def _initialize_default_configs(self):
        """初始化默认配置"""
        configs = [
            EnvironmentTypeConfig(
                name=EnvironmentType.DATABASE,
                display_name="数据库",
                description="关系型数据库连接（MySQL、PostgreSQL等）",
                client_class=DatabaseClient,
                default_port=3306,
                required_fields=["host", "port", "database", "username", "password"],
                optional_fields=["charset", "ssl_mode"],
                icon="database",
                category="database"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.REDIS,
                display_name="Redis",
                description="Redis缓存数据库",
                client_class=RedisClient,
                default_port=6379,
                required_fields=["host", "port"],
                optional_fields=["password", "db", "ssl"],
                icon="redis",
                category="cache"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.SSH,
                display_name="SSH服务器",
                description="SSH远程服务器连接",
                client_class=SSHClient,
                default_port=22,
                required_fields=["host", "port", "username"],
                optional_fields=["password", "private_key", "passphrase"],
                icon="server",
                category="server"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.K8S,
                display_name="Kubernetes",
                description="Kubernetes集群",
                client_class=K8sClient,
                required_fields=["api_server", "token"],
                optional_fields=["namespace", "ca_cert"],
                icon="kubernetes",
                category="container"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.API,
                display_name="API服务",
                description="HTTP/HTTPS API服务",
                client_class=APIClient,
                default_port=80,
                required_fields=["base_url"],
                optional_fields=["api_key", "headers", "timeout"],
                icon="api",
                category="api"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.KAFKA,
                display_name="Apache Kafka",
                description="Kafka消息队列",
                client_class=KafkaClient,
                default_port=9092,
                required_fields=["bootstrap_servers"],
                optional_fields=["security_protocol", "sasl_mechanism", "username", "password"],
                icon="kafka",
                category="message_queue"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.MONGODB,
                display_name="MongoDB",
                description="MongoDB文档数据库",
                client_class=MongoDBClient,
                default_port=27017,
                required_fields=["host", "port", "database"],
                optional_fields=["username", "password", "auth_source"],
                icon="mongodb",
                category="database"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.ELASTICSEARCH,
                display_name="Elasticsearch",
                description="Elasticsearch搜索引擎",
                client_class=ElasticsearchClient,
                default_port=9200,
                required_fields=["host", "port"],
                optional_fields=["username", "password", "ssl", "ca_cert"],
                icon="elasticsearch",
                category="search"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.RABBITMQ,
                display_name="RabbitMQ",
                description="RabbitMQ消息队列",
                client_class=KafkaClient,  # 暂时使用Kafka客户端
                default_port=5672,
                required_fields=["host", "port"],
                optional_fields=["username", "password", "virtual_host"],
                icon="rabbitmq",
                category="message_queue"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.MINIO,
                display_name="MinIO",
                description="MinIO对象存储",
                client_class=APIClient,  # 使用API客户端
                default_port=9000,
                required_fields=["endpoint", "access_key", "secret_key"],
                optional_fields=["secure", "region"],
                icon="minio",
                category="storage"
            ),
            EnvironmentTypeConfig(
                name=EnvironmentType.OPENSEARCH,
                display_name="OpenSearch",
                description="OpenSearch搜索引擎",
                client_class=ElasticsearchClient,  # 使用Elasticsearch客户端
                default_port=9200,
                required_fields=["host", "port"],
                optional_fields=["username", "password", "ssl", "ca_cert"],
                icon="opensearch",
                category="search"
            )
        ]
        
        for config in configs:
            self._environment_configs[config.name] = config
    
    def get_environment_config(self, env_type: str) -> Optional[EnvironmentTypeConfig]:
        """获取环境类型配置"""
        return self._environment_configs.get(env_type)
    
    def get_all_environment_configs(self) -> Dict[str, EnvironmentTypeConfig]:
        """获取所有环境类型配置"""
        return self._environment_configs.copy()
    
    def get_supported_types(self) -> List[str]:
        """获取支持的环境类型列表"""
        return list(self._environment_configs.keys())
    
    def get_configs_by_category(self, category: str) -> List[EnvironmentTypeConfig]:
        """根据分类获取配置"""
        return [
            config for config in self._environment_configs.values()
            if config.category == category
        ]
    
    def register_environment_type(self, config: EnvironmentTypeConfig):
        """注册新的环境类型"""
        self._environment_configs[config.name] = config
    
    def is_supported_type(self, env_type: str) -> bool:
        """检查是否支持指定的环境类型"""
        return env_type in self._environment_configs
    
    def get_client_factory(self) -> 'ClientFactory':
        """获取客户端工厂"""
        return self._client_factory
    
    def validate_config(self, env_type: str, config: Dict[str, Any]) -> List[str]:
        """验证环境配置"""
        env_config = self.get_environment_config(env_type)
        if not env_config:
            return [f"不支持的环境类型: {env_type}"]
        
        errors = []
        
        # 检查必需字段
        for field in env_config.required_fields:
            if field not in config or not config[field]:
                errors.append(f"缺少必需字段: {field}")
        
        return errors


class ClientFactory:
    """客户端工厂"""
    
    def __init__(self):
        self._config_manager = None
    
    def set_config_manager(self, config_manager: ConfigManager):
        """设置配置管理器"""
        self._config_manager = config_manager
    
    def create_client(self, env_type: str, config: Dict[str, Any]):
        """创建客户端实例"""
        if not self._config_manager:
            # 如果没有配置管理器，使用默认映射
            client_mapping = {
                EnvironmentType.DATABASE: DatabaseClient,
                EnvironmentType.REDIS: RedisClient,
                EnvironmentType.SSH: SSHClient,
                EnvironmentType.K8S: K8sClient,
                EnvironmentType.API: APIClient,
                EnvironmentType.KAFKA: KafkaClient,
                EnvironmentType.MONGODB: MongoDBClient,
                EnvironmentType.ELASTICSEARCH: ElasticsearchClient,
                EnvironmentType.RABBITMQ: KafkaClient,
                EnvironmentType.MINIO: APIClient,
                EnvironmentType.OPENSEARCH: ElasticsearchClient
            }
            client_class = client_mapping.get(env_type)
        else:
            env_config = self._config_manager.get_environment_config(env_type)
            client_class = env_config.client_class if env_config else None
        
        if not client_class:
            raise ValueError(f"不支持的环境类型: {env_type}")
        
        return client_class(config)
    
    def get_available_clients(self) -> Dict[str, Type]:
        """获取可用的客户端类型"""
        return {
            EnvironmentType.DATABASE: DatabaseClient,
            EnvironmentType.REDIS: RedisClient,
            EnvironmentType.SSH: SSHClient,
            EnvironmentType.K8S: K8sClient,
            EnvironmentType.API: APIClient,
            EnvironmentType.KAFKA: KafkaClient,
            EnvironmentType.MONGODB: MongoDBClient,
            EnvironmentType.ELASTICSEARCH: ElasticsearchClient,
            EnvironmentType.RABBITMQ: KafkaClient,
            EnvironmentType.MINIO: APIClient,
            EnvironmentType.OPENSEARCH: ElasticsearchClient
        }


# 全局配置管理器实例
config_manager = ConfigManager()
config_manager.get_client_factory().set_config_manager(config_manager)
