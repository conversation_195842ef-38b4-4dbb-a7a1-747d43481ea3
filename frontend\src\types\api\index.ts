// API相关类型统一导出
export * from './request'

// 文件上传相关类型
declare namespace Api {
  namespace Upload {
    // 上传响应数据类型
    interface UploadResponse {
      url: string
      filename: string
      content_type: string
      size: number
    }

    // 文件信息类型
    interface FileInfo {
      filename: string
      size: number
      created_at: number
      modified_at: number
    }

    // 头像上传参数
    interface AvatarUploadParams {
      file: File
    }

    // 头像更新参数
    interface AvatarUpdateParams {
      avatar_url: string
    }
  }
}
