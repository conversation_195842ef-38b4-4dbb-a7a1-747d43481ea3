"""
混沌测试任务API路由
"""
from typing import List
from fastapi import APIRouter, Depends, Path, Query, Body

from app.api.deps import get_current_user, get_current_superuser
from app.core.dependencies import get_chaos_task_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_task_service import ChaosTaskService
from app.schemas.base import APIResponse, PaginationResponse
from app.schemas.chaos.chaos_task import (
    ChaosTaskCreate, ChaosTaskUpdate, ChaosTaskResponse, ChaosTaskListResponse,
    ChaosTaskSearchParams, ChaosTaskStatistics, ChaosTaskExecuteRequest,
    ChaosTaskBatchRequest
)

router = APIRouter()


@router.get("/", response_model=PaginationResponse[ChaosTaskListResponse], summary="获取任务列表")
async def get_tasks(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    keyword: str = Query(None, description="搜索关键词"),
    env_ids: List[int] = Query(None, description="环境ID列表"),
    fault_type: str = Query(None, description="故障类型"),
    status: str = Query(None, description="任务状态"),
    execution_type: str = Query(None, description="执行类型"),
    created_by: str = Query(None, description="创建者"),
    order_by: str = Query("created_at", description="排序字段"),
    desc: bool = Query(True, description="是否降序"),
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """获取混沌测试任务列表"""
    search_params = ChaosTaskSearchParams(
        page=page,
        size=size,
        keyword=keyword,
        env_ids=env_ids,
        fault_type=fault_type,
        status=status,
        execution_type=execution_type,
        created_by=created_by,
        order_by=order_by,
        desc=desc
    )
    
    result = await service.search_tasks(search_params)
    return response_builder.paginated(
        records=result.records,
        total=result.total,
        current=result.current,
        size=result.size,
        message="获取任务列表成功"
    )


@router.post("/", response_model=APIResponse[ChaosTaskResponse], summary="创建任务")
async def create_task(
    task_data: ChaosTaskCreate,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """创建混沌测试任务"""
    task = await service.create_task(task_data, current_user.id)
    return response_builder.success(data=task, message="创建任务成功")


@router.get("/{task_id}", response_model=APIResponse[ChaosTaskResponse], summary="获取任务详情")
async def get_task(
    task_id: int = Path(..., description="任务ID"),
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """获取任务详情"""
    task = await service.get_task_by_id(task_id)
    return response_builder.success(data=task, message="获取任务详情成功")


@router.put("/{task_id}", response_model=APIResponse[ChaosTaskResponse], summary="更新任务")
async def update_task(
    task_id: int = Path(..., description="任务ID"),
    task_data: ChaosTaskUpdate = Body(...),
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """更新混沌测试任务"""
    task = await service.update(task_id, task_data, str(current_user.id))
    return response_builder.success(data=task, message="更新任务成功")


@router.delete("/{task_id}", response_model=APIResponse[None], summary="删除任务")
async def delete_task(
    task_id: int = Path(..., description="任务ID"),
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """删除混沌测试任务"""
    await service.delete(task_id)
    return response_builder.success(message="删除任务成功")


@router.post("/{task_id}/execute", response_model=APIResponse[dict], summary="执行任务")
async def execute_task(
    task_id: int = Path(..., description="任务ID"),
    request: ChaosTaskExecuteRequest = Body(...),
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """执行混沌测试任务"""
    result = await service.execute_task(task_id, request, current_user.id)
    return response_builder.success(data=result, message="任务执行成功")


@router.post("/{task_id}/pause", response_model=APIResponse[None], summary="暂停任务")
async def pause_task(
    task_id: int = Path(..., description="任务ID"),
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """暂停任务执行 """
    await service.pause_task(task_id, current_user.id)
    return response_builder.success(message="任务已暂停")


@router.post("/{task_id}/terminate", response_model=APIResponse[None], summary="终止任务")
async def terminate_task(
    task_id: int = Path(..., description="任务ID"),
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """终止任务执行"""
    await service.terminate_task(task_id, current_user.id)
    return response_builder.success(message="任务已终止")


@router.post("/{task_id}/reset", response_model=APIResponse[bool], summary="重置任务")
async def reset_task(
    task_id: int = Path(..., description="任务ID"),
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    
    result = await service.reset_task(task_id, current_user.id)
    return response_builder.success(data=result, message="任务重置成功")


@router.post("/batch", response_model=APIResponse[dict], summary="批量操作任务")
async def batch_operation(
    request: ChaosTaskBatchRequest,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """批量操作任务"""
    results = {
        "success_count": 0,
        "failed_count": 0,
        "errors": []
    }
    
    for task_id in request.task_ids:
        try:
            if request.action == "execute":
                execute_request = ChaosTaskExecuteRequest()
                await service.execute_task(task_id, execute_request, current_user.id)
            elif request.action == "pause":
                await service.pause_task(task_id, current_user.id)
            elif request.action == "terminate":
                await service.terminate_task(task_id, current_user.id)
            elif request.action == "delete":
                await service.delete(task_id)
            
            results["success_count"] += 1
        except Exception as e:
            results["failed_count"] += 1
            results["errors"].append(f"任务 {task_id}: {str(e)}")
    
    return response_builder.success(data=results, message="批量操作完成")

