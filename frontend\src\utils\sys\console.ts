// ANSI 转义码生成网站  https://patorjk.com/software/taag/#p=display&f=Big&t=ABB%0A
const asciiArt = `
  _____  _____ _______ ______  _____ _______ _____  _            _______ ______ ____  _____  __  __ 
 |  __ \|  __ \__   __|  ____|/ ____|__   __|  __ \| |        /\|__   __|  ____/ __ \|  __ \|  \/  |
 | |  | | |__) | | |  | |__  | (___    | |  | |__) | |       /  \  | |  | |__ | |  | | |__) | \  / |
 | |  | |  ___/  | |  |  __|  \___ \   | |  |  ___/| |      / /\ \ | |  |  __|| |  | |  _  /| |\/| |
 | |__| | |      | |  | |____ ____) |  | |  | |    | |____ / ____ \| |  | |   | |__| | | \ \| |  | |
 |_____/|_|      |_|  |______|_____/   |_|  |_|    |______/_/    \_\_|  |_|    \____/|_|  \_\_|  |_|
                                                                                                    
                                                                                                    
`

console.log(asciiArt)
