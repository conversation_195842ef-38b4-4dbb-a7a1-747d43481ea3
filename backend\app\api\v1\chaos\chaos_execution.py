"""
混沌测试执行记录API路由
"""
from fastapi import APIRouter, Depends, Path, Query, Body

from app.api.deps import get_current_user
from app.core.dependencies import get_chaos_execution_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_execution_service import ChaosExecutionService
from app.schemas.base import APIResponse, PaginationResponse
from app.schemas.chaos.chaos_execution import (
    ChaosExecutionResponse, ChaosExecutionListResponse, ChaosExecutionDetailResponse,
    ChaosExecutionSearchParams, ChaosExecutionLogRequest,
    ChaosExecutionLogResponse, ChaosExecutionRetryRequest, ChaosExecutionBatchRequest
)

router = APIRouter()


@router.get("/", response_model=PaginationResponse[ChaosExecutionListResponse], summary="获取执行记录列表")
async def get_executions(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    task_name: str = Query(None, description="任务名称"),
    status: str = Query(None, description="执行状态"),
    order_by: str = Query("created_at", description="排序字段"),
    desc: bool = Query(True, description="是否降序"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """获取执行记录列表"""
    search_params = ChaosExecutionSearchParams(
        page=page,
        size=size,
        task_name=task_name,
        status=status,
        order_by=order_by,
        desc=desc
    )
    
    result = await service.search_executions(search_params)
    return response_builder.paginated(
        records=result.records,
        total=result.total,
        current=result.current,
        size=result.size,
        message="获取执行记录列表成功"
    )


@router.get("/{execution_id}", response_model=APIResponse[ChaosExecutionDetailResponse], summary="获取执行记录详情")
async def get_execution(
    execution_id: int = Path(..., description="执行记录ID"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """获取执行记录详情"""
    execution = await service.get_execution_by_id(execution_id)
    return response_builder.success(data=execution, message="获取执行记录详情成功")


@router.delete("/{execution_id}", response_model=APIResponse[None], summary="删除执行记录")
async def delete_execution(
    execution_id: int = Path(..., description="执行记录ID"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """删除执行记录"""
    await service.delete(execution_id)
    return response_builder.success(message="删除执行记录成功")


@router.get("/task/{task_id}", response_model=PaginationResponse[ChaosExecutionListResponse], summary="获取任务的执行记录")
async def get_task_executions(
    task_id: int = Path(..., description="任务ID"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """获取任务的执行记录"""
    result = await service.get_task_executions(task_id, page, size)
    return response_builder.paginated(
        records=result.records,
        total=result.total,
        current=result.current,
        size=result.size,
        message="获取任务执行记录成功"
    )


@router.post("/{execution_id}/log", response_model=APIResponse[ChaosExecutionLogResponse], summary="获取执行日志")
async def get_execution_log(
    execution_id: int = Path(..., description="执行记录ID"),
    log_request: ChaosExecutionLogRequest = Body(...),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """获取执行日志"""
    log_request.execution_id = execution_id
    log_response = await service.get_execution_log(log_request)
    return response_builder.success(data=log_response, message="获取执行日志成功")


@router.post("/{execution_id}/retry", response_model=APIResponse[ChaosExecutionResponse], summary="重试执行")
async def retry_execution(
    execution_id: int = Path(..., description="执行记录ID"),
    retry_request: ChaosExecutionRetryRequest = Body(default=ChaosExecutionRetryRequest()),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """重试执行"""
    retry_request.execution_id = execution_id
    execution = await service.retry_execution(retry_request, current_user.id)
    return response_builder.success(data=execution, message="重试执行成功")


@router.post("/{execution_id}/cancel", response_model=APIResponse[None], summary="取消执行")
async def cancel_execution(
    execution_id: int = Path(..., description="执行记录ID"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """取消执行"""
    await service.cancel_execution(execution_id, current_user.id)
    return response_builder.success(message="取消执行成功")


@router.post("/batch", response_model=APIResponse[dict], summary="批量操作执行记录")
async def batch_operation(
    request: ChaosExecutionBatchRequest,
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """批量操作执行记录"""
    result = await service.batch_operation(request, current_user.id)
    return response_builder.success(data=result, message="批量操作完成")



