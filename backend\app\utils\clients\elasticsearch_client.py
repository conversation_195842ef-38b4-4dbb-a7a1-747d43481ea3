"""
Elasticsearch连接客户端
支持Elasticsearch和OpenSearch的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional, List

from .base_client import BaseClient, ConnectionResult

try:
    from elasticsearch import AsyncElasticsearch
    from elasticsearch.exceptions import ElasticsearchException, ConnectionError as ESConnectionError
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False


class ElasticsearchClient(BaseClient):
    """
    Elasticsearch连接客户端
    支持Elasticsearch和OpenSearch的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Elasticsearch客户端
        
        Args:
            config: Elasticsearch连接配置
                - host: Elasticsearch主机地址 (默认: localhost)
                - port: Elasticsearch端口 (默认: 9200)
                - scheme: 协议 (默认: http)
                - username: 用户名 (可选)
                - password: 密码 (可选)
                - api_key: API密钥 (可选)
                - ca_certs: CA证书文件路径 (可选)
                - verify_certs: 是否验证证书 (默认: True)
                - ssl_show_warn: 是否显示SSL警告 (默认: True)
        """
        super().__init__(config)
        
        if not ELASTICSEARCH_AVAILABLE:
            raise ImportError("Elasticsearch库未安装，请运行: pip install elasticsearch")
        
        # 设置默认值
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 9200)
        self.scheme = config.get('scheme', 'http')
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.api_key = config.get('api_key', '')
        self.ca_certs = config.get('ca_certs', '')
        self.verify_certs = config.get('verify_certs', True)
        self.ssl_show_warn = config.get('ssl_show_warn', True)
        
        self.client = None

    def _build_connection_config(self) -> Dict[str, Any]:
        """
        构建连接配置
        
        Returns:
            Dict: 连接配置字典
        """
        # 构建主机列表
        hosts = [f"{self.scheme}://{self.host}:{self.port}"]
        
        connection_config = {
            'hosts': hosts,
            'timeout': 30,
            'max_retries': 3,
            'retry_on_timeout': True
        }
        
        # 添加认证配置
        if self.api_key:
            connection_config['api_key'] = self.api_key
        elif self.username and self.password:
            connection_config['basic_auth'] = (self.username, self.password)
        
        # 添加SSL配置
        if self.scheme == 'https':
            ssl_config = {
                'verify_certs': self.verify_certs,
                'ssl_show_warn': self.ssl_show_warn
            }
            if self.ca_certs:
                ssl_config['ca_certs'] = self.ca_certs
            connection_config.update(ssl_config)
        
        return connection_config

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立Elasticsearch连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 获取连接配置
            connection_config = self._build_connection_config()
            connection_config['timeout'] = timeout
            
            # 创建客户端
            self.client = AsyncElasticsearch(**connection_config)
            
            # 测试连接
            info = await self.client.info()
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到Elasticsearch {self.host}:{self.port}",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "scheme": self.scheme,
                    "cluster_name": info.get('cluster_name', 'unknown'),
                    "version": info.get('version', {}).get('number', 'unknown'),
                    "lucene_version": info.get('version', {}).get('lucene_version', 'unknown')
                }
            )
            
        except ESConnectionError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Elasticsearch连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "ConnectionError"}
            )
        except ElasticsearchException as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Elasticsearch异常: {str(e)}",
                duration=duration,
                details={"error_type": "ElasticsearchException"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Elasticsearch连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开Elasticsearch连接"""
        try:
            if self.client:
                await self.client.close()
                self.client = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试Elasticsearch连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        start_time = time.time()
        
        try:
            # 如果没有建立连接，先建立连接
            if not self.client:
                connect_result = await self.connect(timeout)
                if not connect_result.success:
                    return connect_result
            
            # 执行健康检查
            health = await self.client.cluster.health()
            
            # 获取集群统计信息
            stats = await self.client.cluster.stats()
            
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"Elasticsearch连接测试成功",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "cluster_status": health.get('status', 'unknown'),
                    "number_of_nodes": health.get('number_of_nodes', 0),
                    "number_of_data_nodes": health.get('number_of_data_nodes', 0),
                    "active_primary_shards": health.get('active_primary_shards', 0),
                    "active_shards": health.get('active_shards', 0),
                    "indices_count": stats.get('indices', {}).get('count', 0)
                }
            )
            
        except ElasticsearchException as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Elasticsearch连接测试失败: {str(e)}",
                duration=duration,
                details={"error_type": "ElasticsearchException"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Elasticsearch连接测试异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def get_indices(self) -> List[str]:
        """
        获取索引列表
        
        Returns:
            List[str]: 索引名称列表
        """
        if not self.client:
            raise RuntimeError("Elasticsearch未连接")
        
        try:
            indices = await self.client.cat.indices(format='json')
            return [index['index'] for index in indices]
        except Exception as e:
            raise RuntimeError(f"获取索引列表失败: {str(e)}")

    async def get_cluster_health(self) -> Dict[str, Any]:
        """
        获取集群健康状态
        
        Returns:
            Dict: 集群健康信息
        """
        if not self.client:
            raise RuntimeError("Elasticsearch未连接")
        
        try:
            health = await self.client.cluster.health()
            return health
        except Exception as e:
            raise RuntimeError(f"获取集群健康状态失败: {str(e)}")

    async def search(self, index: str, query: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行搜索查询
        
        Args:
            index: 索引名称
            query: 查询DSL
            
        Returns:
            Dict: 搜索结果
        """
        if not self.client:
            raise RuntimeError("Elasticsearch未连接")
        
        try:
            result = await self.client.search(index=index, body=query)
            return {
                "success": True,
                "result": result,
                "total_hits": result.get('hits', {}).get('total', {}).get('value', 0)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }

    async def create_index(self, index: str, mapping: Optional[Dict[str, Any]] = None) -> bool:
        """
        创建索引
        
        Args:
            index: 索引名称
            mapping: 索引映射 (可选)
            
        Returns:
            bool: 是否创建成功
        """
        if not self.client:
            raise RuntimeError("Elasticsearch未连接")
        
        try:
            body = {}
            if mapping:
                body['mappings'] = mapping
            
            await self.client.indices.create(index=index, body=body)
            return True
        except Exception as e:
            raise RuntimeError(f"创建索引失败: {str(e)}")

    async def delete_index(self, index: str) -> bool:
        """
        删除索引
        
        Args:
            index: 索引名称
            
        Returns:
            bool: 是否删除成功
        """
        if not self.client:
            raise RuntimeError("Elasticsearch未连接")
        
        try:
            await self.client.indices.delete(index=index)
            return True
        except Exception as e:
            raise RuntimeError(f"删除索引失败: {str(e)}")
