"""
统一响应处理系统
提供标准化的API响应格式
"""
from typing import Any, List
from app.schemas.base import APIResponse, PaginationResponse, PaginationData


class ResponseBuilder:
    """
    响应构建器
    提供统一的响应数据构建方法，返回Pydantic模型实例
    """
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "操作成功",
        code: int = 200
    ) -> APIResponse[Any]:
        """
        构建成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            code: 状态码
            
        Returns:
            APIResponse 实例
        """
        return APIResponse(
            success=True,
            code=code,
            message=message,
            data=data
        )
    
    @staticmethod
    def error(
        message: str = "操作失败",
        code: int = 400,
        data: Any = None
    ) -> APIResponse[Any]:
        """
        构建错误响应
        
        Args:
            message: 错误消息
            code: 错误码
            data: 错误数据
            
        Returns:
            APIResponse 实例
        """
        return APIResponse(
            success=False,
            code=code,
            message=message,
            data=data
        )
    
    @staticmethod
    def paginated(
        records: List[Any],
        total: int,
        current: int,
        size: int,
        message: str = "查询成功"
    ) -> PaginationResponse[Any]:
        """
        构建分页响应
        
        Args:
            records: 数据列表
            total: 总数
            current: 当前页
            size: 页大小
            message: 响应消息
            
        Returns:
            PaginationResponse 实例
        """
        return PaginationResponse(
            success=True,
            code=200,
            message=message,
            data=PaginationData(
                records=records,
                total=total,
                current=current,
                size=size
            )
        )


# 全局响应构建器实例
response_builder = ResponseBuilder() 