<template>
  <div class="string-processor">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6f8;</i>
        <h2>字符串处理工具</h2>
      </div>
      <p class="tool-description">
        大小写转换、去空格、字符统计、编码转换等字符串处理功能
      </p>
    </div>

    <div class="tool-content">
      <!-- 输入区域 -->
      <div class="input-section">
        <div class="input-header">
          <h3 class="input-title">输入文本</h3>
          <div class="input-actions">
            <ElButton size="small" @click="clearInput">
              <i class="iconfont-sys">&#xe622;</i>
              清空
            </ElButton>
            <ElButton size="small" @click="pasteInput">
              <i class="iconfont-sys">&#xe623;</i>
              粘贴
            </ElButton>
            <ElButton size="small" @click="loadSample">
              <i class="iconfont-sys">&#xe629;</i>
              示例
            </ElButton>
          </div>
        </div>
        
        <ElInput
          v-model="inputText"
          type="textarea"
          :rows="8"
          placeholder="请输入需要处理的文本..."
          class="text-input"
          @input="handleTextChange"
        />
      </div>

      <!-- 处理功能 -->
      <div class="functions-section">
        <div class="function-tabs">
          <div
            v-for="tab in functionTabs"
            :key="tab.key"
            class="tab-item"
            :class="{ active: activeFunction === tab.key }"
            @click="activeFunction = tab.key"
          >
            <i class="iconfont-sys" v-html="tab.icon"></i>
            <span>{{ tab.name }}</span>
          </div>
        </div>

        <!-- 大小写转换 -->
        <div v-if="activeFunction === 'case'" class="case-section">
          <div class="function-grid">
            <div
              v-for="caseFunc in caseFunctions"
              :key="caseFunc.key"
              class="function-card"
              @click="applyCaseFunction(caseFunc.key)"
            >
              <div class="function-icon">
                <i class="iconfont-sys" v-html="caseFunc.icon"></i>
              </div>
              <div class="function-content">
                <div class="function-name">{{ caseFunc.name }}</div>
                <div class="function-desc">{{ caseFunc.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空格处理 -->
        <div v-if="activeFunction === 'space'" class="space-section">
          <div class="function-grid">
            <div
              v-for="spaceFunc in spaceFunctions"
              :key="spaceFunc.key"
              class="function-card"
              @click="applySpaceFunction(spaceFunc.key)"
            >
              <div class="function-icon">
                <i class="iconfont-sys" v-html="spaceFunc.icon"></i>
              </div>
              <div class="function-content">
                <div class="function-name">{{ spaceFunc.name }}</div>
                <div class="function-desc">{{ spaceFunc.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 字符串操作 -->
        <div v-if="activeFunction === 'string'" class="string-section">
          <div class="function-grid">
            <div
              v-for="stringFunc in stringFunctions"
              :key="stringFunc.key"
              class="function-card"
              @click="applyStringFunction(stringFunc.key)"
            >
              <div class="function-icon">
                <i class="iconfont-sys" v-html="stringFunc.icon"></i>
              </div>
              <div class="function-content">
                <div class="function-name">{{ stringFunc.name }}</div>
                <div class="function-desc">{{ stringFunc.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 编码转换 -->
        <div v-if="activeFunction === 'encode'" class="encode-section">
          <div class="function-grid">
            <div
              v-for="encodeFunc in encodeFunctions"
              :key="encodeFunc.key"
              class="function-card"
              @click="applyEncodeFunction(encodeFunc.key)"
            >
              <div class="function-icon">
                <i class="iconfont-sys" v-html="encodeFunc.icon"></i>
              </div>
              <div class="function-content">
                <div class="function-name">{{ encodeFunc.name }}</div>
                <div class="function-desc">{{ encodeFunc.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输出区域 -->
      <div class="output-section">
        <div class="output-header">
          <h3 class="output-title">处理结果</h3>
          <div class="output-actions">
            <ElButton size="small" @click="copyOutput" :disabled="!outputText">
              <i class="iconfont-sys">&#xe627;</i>
              复制
            </ElButton>
            <ElButton size="small" @click="replaceInput" :disabled="!outputText">
              <i class="iconfont-sys">&#xe632;</i>
              替换输入
            </ElButton>
          </div>
        </div>
        
        <ElInput
          v-model="outputText"
          type="textarea"
          :rows="8"
          readonly
          placeholder="处理结果将在这里显示..."
          class="text-output"
        />
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-header">
          <h3 class="stats-title">文本统计</h3>
        </div>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">字符数</div>
            <div class="stat-value">{{ textStats.characters }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">字节数</div>
            <div class="stat-value">{{ textStats.bytes }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">行数</div>
            <div class="stat-value">{{ textStats.lines }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">单词数</div>
            <div class="stat-value">{{ textStats.words }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">段落数</div>
            <div class="stat-value">{{ textStats.paragraphs }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">空格数</div>
            <div class="stat-value">{{ textStats.spaces }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'StringProcessor' })

// 功能选项
const functionTabs = [
  { key: 'case', name: '大小写', icon: '&#xe651;' },
  { key: 'space', name: '空格处理', icon: '&#xe652;' },
  { key: 'string', name: '字符串操作', icon: '&#xe653;' },
  { key: 'encode', name: '编码转换', icon: '&#xe654;' }
]

// 大小写转换功能
const caseFunctions = [
  { key: 'upper', name: '全部大写', description: '转换为大写字母', icon: '&#xe655;' },
  { key: 'lower', name: '全部小写', description: '转换为小写字母', icon: '&#xe656;' },
  { key: 'title', name: '标题格式', description: '每个单词首字母大写', icon: '&#xe657;' },
  { key: 'sentence', name: '句子格式', description: '首字母大写', icon: '&#xe658;' },
  { key: 'camel', name: '驼峰命名', description: 'camelCase格式', icon: '&#xe659;' },
  { key: 'pascal', name: '帕斯卡命名', description: 'PascalCase格式', icon: '&#xe65a;' },
  { key: 'snake', name: '蛇形命名', description: 'snake_case格式', icon: '&#xe65b;' },
  { key: 'kebab', name: '短横线命名', description: 'kebab-case格式', icon: '&#xe65c;' }
]

// 空格处理功能
const spaceFunctions = [
  { key: 'trim', name: '去除首尾空格', description: '删除开头和结尾的空格', icon: '&#xe65d;' },
  { key: 'trimLeft', name: '去除左侧空格', description: '删除开头的空格', icon: '&#xe65e;' },
  { key: 'trimRight', name: '去除右侧空格', description: '删除结尾的空格', icon: '&#xe65f;' },
  { key: 'removeAll', name: '删除所有空格', description: '删除全部空格字符', icon: '&#xe660;' },
  { key: 'normalizeSpaces', name: '规范化空格', description: '多个空格合并为一个', icon: '&#xe661;' },
  { key: 'removeEmptyLines', name: '删除空行', description: '删除空白行', icon: '&#xe662;' },
  { key: 'addSpaces', name: '添加空格', description: '在每个字符间添加空格', icon: '&#xe663;' },
  { key: 'tabToSpaces', name: 'Tab转空格', description: '制表符转换为空格', icon: '&#xe664;' }
]

// 字符串操作功能
const stringFunctions = [
  { key: 'reverse', name: '反转字符串', description: '颠倒字符顺序', icon: '&#xe665;' },
  { key: 'sort', name: '排序行', description: '按字母顺序排序', icon: '&#xe666;' },
  { key: 'unique', name: '去重行', description: '删除重复的行', icon: '&#xe667;' },
  { key: 'shuffle', name: '随机排序', description: '随机打乱行顺序', icon: '&#xe668;' },
  { key: 'addNumbers', name: '添加行号', description: '在每行前添加序号', icon: '&#xe669;' },
  { key: 'removeNumbers', name: '删除行号', description: '删除行首的数字', icon: '&#xe66a;' },
  { key: 'extractEmails', name: '提取邮箱', description: '提取所有邮箱地址', icon: '&#xe66b;' },
  { key: 'extractUrls', name: '提取URL', description: '提取所有网址链接', icon: '&#xe66c;' }
]

// 编码转换功能
const encodeFunctions = [
  { key: 'htmlEncode', name: 'HTML编码', description: '转换HTML特殊字符', icon: '&#xe66d;' },
  { key: 'htmlDecode', name: 'HTML解码', description: '还原HTML实体', icon: '&#xe66e;' },
  { key: 'unicodeEncode', name: 'Unicode编码', description: '转换为Unicode编码', icon: '&#xe66f;' },
  { key: 'unicodeDecode', name: 'Unicode解码', description: '还原Unicode字符', icon: '&#xe670;' },
  { key: 'jsonEscape', name: 'JSON转义', description: '转义JSON特殊字符', icon: '&#xe671;' },
  { key: 'jsonUnescape', name: 'JSON反转义', description: '还原JSON转义字符', icon: '&#xe672;' },
  { key: 'csvEscape', name: 'CSV转义', description: '转义CSV特殊字符', icon: '&#xe673;' },
  { key: 'sqlEscape', name: 'SQL转义', description: '转义SQL特殊字符', icon: '&#xe674;' }
]

// 响应式数据
const activeFunction = ref('case')
const inputText = ref('')
const outputText = ref('')

// 示例文本
const sampleText = `Hello World! 这是一个测试文本。
包含多行内容，用于演示字符串处理功能。
Email: <EMAIL>
URL: https://www.example.com
特殊字符: <>&"'`

// 计算属性
const textStats = computed(() => {
  const text = inputText.value
  
  return {
    characters: text.length,
    bytes: new Blob([text]).size,
    lines: text ? text.split('\n').length : 0,
    words: text ? text.trim().split(/\s+/).filter(word => word.length > 0).length : 0,
    paragraphs: text ? text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length : 0,
    spaces: (text.match(/\s/g) || []).length
  }
})

// 方法
const handleTextChange = () => {
  // 文本变化时清空输出
  outputText.value = ''
}

const applyCaseFunction = (funcKey: string) => {
  if (!inputText.value.trim()) {
    ElMessage.warning('请先输入文本')
    return
  }

  let result = ''
  const text = inputText.value

  switch (funcKey) {
    case 'upper':
      result = text.toUpperCase()
      break
    case 'lower':
      result = text.toLowerCase()
      break
    case 'title':
      result = text.replace(/\w\S*/g, (txt) => 
        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
      )
      break
    case 'sentence':
      result = text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
      break
    case 'camel':
      result = text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
        index === 0 ? word.toLowerCase() : word.toUpperCase()
      ).replace(/\s+/g, '')
      break
    case 'pascal':
      result = text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => 
        word.toUpperCase()
      ).replace(/\s+/g, '')
      break
    case 'snake':
      result = text.toLowerCase().replace(/\s+/g, '_').replace(/[^\w_]/g, '')
      break
    case 'kebab':
      result = text.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '')
      break
  }

  outputText.value = result
  ElMessage.success('转换完成')
}

const applySpaceFunction = (funcKey: string) => {
  if (!inputText.value) {
    ElMessage.warning('请先输入文本')
    return
  }

  let result = ''
  const text = inputText.value

  switch (funcKey) {
    case 'trim':
      result = text.trim()
      break
    case 'trimLeft':
      result = text.trimStart()
      break
    case 'trimRight':
      result = text.trimEnd()
      break
    case 'removeAll':
      result = text.replace(/\s/g, '')
      break
    case 'normalizeSpaces':
      result = text.replace(/\s+/g, ' ').trim()
      break
    case 'removeEmptyLines':
      result = text.split('\n').filter(line => line.trim().length > 0).join('\n')
      break
    case 'addSpaces':
      result = text.split('').join(' ')
      break
    case 'tabToSpaces':
      result = text.replace(/\t/g, '    ')
      break
  }

  outputText.value = result
  ElMessage.success('处理完成')
}

const applyStringFunction = (funcKey: string) => {
  if (!inputText.value.trim()) {
    ElMessage.warning('请先输入文本')
    return
  }

  let result = ''
  const text = inputText.value

  switch (funcKey) {
    case 'reverse':
      result = text.split('').reverse().join('')
      break
    case 'sort':
      result = text.split('\n').sort().join('\n')
      break
    case 'unique':
      result = [...new Set(text.split('\n'))].join('\n')
      break
    case 'shuffle':
      const lines = text.split('\n')
      for (let i = lines.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        ;[lines[i], lines[j]] = [lines[j], lines[i]]
      }
      result = lines.join('\n')
      break
    case 'addNumbers':
      result = text.split('\n').map((line, index) => `${index + 1}. ${line}`).join('\n')
      break
    case 'removeNumbers':
      result = text.split('\n').map(line => line.replace(/^\d+\.\s*/, '')).join('\n')
      break
    case 'extractEmails':
      const emails = text.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g) || []
      result = emails.join('\n')
      break
    case 'extractUrls':
      const urls = text.match(/https?:\/\/[^\s]+/g) || []
      result = urls.join('\n')
      break
  }

  outputText.value = result
  ElMessage.success('处理完成')
}

const applyEncodeFunction = (funcKey: string) => {
  if (!inputText.value.trim()) {
    ElMessage.warning('请先输入文本')
    return
  }

  let result = ''
  const text = inputText.value

  try {
    switch (funcKey) {
      case 'htmlEncode':
        result = text
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;')
        break
      case 'htmlDecode':
        result = text
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")
        break
      case 'unicodeEncode':
        result = text.split('').map(char => {
          const code = char.charCodeAt(0)
          return code > 127 ? `\\u${code.toString(16).padStart(4, '0')}` : char
        }).join('')
        break
      case 'unicodeDecode':
        result = text.replace(/\\u([0-9a-fA-F]{4})/g, (match, code) => 
          String.fromCharCode(parseInt(code, 16))
        )
        break
      case 'jsonEscape':
        result = JSON.stringify(text).slice(1, -1)
        break
      case 'jsonUnescape':
        result = JSON.parse(`"${text}"`)
        break
      case 'csvEscape':
        result = text.includes(',') || text.includes('"') || text.includes('\n') 
          ? `"${text.replace(/"/g, '""')}"` 
          : text
        break
      case 'sqlEscape':
        result = text.replace(/'/g, "''")
        break
    }

    outputText.value = result
    ElMessage.success('编码转换完成')
  } catch (error) {
    ElMessage.error('编码转换失败')
  }
}

const clearInput = () => {
  inputText.value = ''
  outputText.value = ''
}

const pasteInput = async () => {
  try {
    const text = await navigator.clipboard.readText()
    inputText.value = text
    outputText.value = ''
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const loadSample = () => {
  inputText.value = sampleText
  outputText.value = ''
  ElMessage.success('已加载示例文本')
}

const copyOutput = async () => {
  if (!outputText.value) return
  
  try {
    await navigator.clipboard.writeText(outputText.value)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const replaceInput = () => {
  if (!outputText.value) return
  
  inputText.value = outputText.value
  outputText.value = ''
  ElMessage.success('已替换输入文本')
}
</script>

<style lang="scss" scoped>
.string-processor {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .input-section,
    .functions-section,
    .output-section,
    .stats-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }

    .input-section,
    .output-section {
      .input-header,
      .output-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .input-title,
        .output-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }

        .input-actions,
        .output-actions {
          display: flex;
          gap: 8px;
        }
      }

      .text-input,
      .text-output {
        :deep(.el-textarea__inner) {
          font-family: 'Consolas', 'Monaco', monospace;
          font-size: 14px;
          line-height: 1.5;
        }
      }

      .text-output {
        :deep(.el-textarea__inner) {
          background: var(--el-fill-color-lighter);
        }
      }
    }

    .functions-section {
      .function-tabs {
        display: flex;
        gap: 8px;
        margin-bottom: 24px;
        padding: 4px;
        background: var(--el-fill-color-light);
        border-radius: 8px;

        .tab-item {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          padding: 12px 16px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-regular);

          &:hover {
            background: var(--el-fill-color);
          }

          &.active {
            background: var(--el-color-primary);
            color: white;
          }

          i {
            font-size: 16px;
          }
        }
      }

      .case-section,
      .space-section,
      .string-section,
      .encode-section {
        .function-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;

          .function-card {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            border: 1px solid var(--el-border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: var(--el-color-primary);
              background: var(--el-color-primary-light-9);
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .function-icon {
              width: 40px;
              height: 40px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: var(--el-color-primary-light-9);
              border-radius: 8px;
              color: var(--el-color-primary);
              font-size: 18px;
              flex-shrink: 0;
            }

            .function-content {
              flex: 1;
              min-width: 0;

              .function-name {
                font-size: 14px;
                font-weight: 600;
                color: var(--el-text-color-primary);
                margin-bottom: 4px;
              }

              .function-desc {
                font-size: 12px;
                color: var(--el-text-color-regular);
                line-height: 1.4;
              }
            }
          }
        }
      }
    }

    .stats-section {
      .stats-header {
        margin-bottom: 16px;

        .stats-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 16px;

        .stat-item {
          text-align: center;
          padding: 16px;
          background: var(--el-fill-color-light);
          border-radius: 8px;

          .stat-label {
            font-size: 12px;
            color: var(--el-text-color-regular);
            margin-bottom: 8px;
          }

          .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--el-color-primary);
            font-family: 'Consolas', 'Monaco', monospace;
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .string-processor {
    .tool-content {
      .functions-section {
        .function-tabs {
          .tab-item {
            flex-direction: column;
            gap: 4px;
            padding: 8px 12px;

            span {
              font-size: 12px;
            }
          }
        }

        .case-section,
        .space-section,
        .string-section,
        .encode-section {
          .function-grid {
            grid-template-columns: 1fr;

            .function-card {
              .function-icon {
                width: 32px;
                height: 32px;
                font-size: 16px;
              }

              .function-content {
                .function-name {
                  font-size: 13px;
                }

                .function-desc {
                  font-size: 11px;
                }
              }
            }
          }
        }
      }

      .stats-section {
        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          .stat-item {
            padding: 12px;

            .stat-value {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
