"""
混沌测试执行记录数据访问层
"""
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

from sqlalchemy import and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.repositories.base import BaseRepository
from app.models.chaos.chaos_execution import ChaosExecution
from app.schemas.chaos.chaos_execution import ChaosExecutionCreate, ChaosExecutionUpdate


class ChaosExecutionRepository(BaseRepository[ChaosExecution, ChaosExecutionCreate, ChaosExecutionUpdate]):
    """
    混沌测试执行记录数据访问层
    专注于执行记录相关的数据库操作
    """

    def __init__(self, db: AsyncSession):
        super().__init__(ChaosExecution, db)

    async def get_with_task(self, execution_id: int) -> Optional[ChaosExecution]:
        """
        获取执行记录及其关联的任务信息
        
        Args:
            execution_id: 执行记录ID
            
        Returns:
            执行记录实例或None
        """
        query = (
            select(ChaosExecution)
            .options(selectinload(ChaosExecution.task))
            .where(ChaosExecution.id == execution_id)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def get_by_task_id(self, task_id: int) -> List[ChaosExecution]:
        """
        根据任务ID获取执行记录列表
        
        Args:
            task_id: 任务ID
            
        Returns:
            执行记录列表
        """
        return await self.get_by_field("task_id", task_id, unique=False)

    async def get_by_host_id(self, host_id: int) -> List[ChaosExecution]:
        """
        根据主机ID获取执行记录列表
        
        Args:
            host_id: 主机ID
            
        Returns:
            执行记录列表
        """
        return await self.get_by_field("host_id", host_id, unique=False)

    async def get_by_status(self, status: str) -> List[ChaosExecution]:
        """
        根据状态获取执行记录列表
        
        Args:
            status: 执行状态
            
        Returns:
            执行记录列表
        """
        return await self.get_by_field("status", status, unique=False)

    async def get_running_executions(self) -> List[ChaosExecution]:
        """
        获取正在运行的执行记录
        
        Returns:
            运行中的执行记录列表
        """
        return await self.get_by_status("running")

    async def get_by_chaos_uid(self, chaos_uid: str) -> Optional[ChaosExecution]:
        """
        根据ChaosBlade UID获取执行记录
        
        Args:
            chaos_uid: ChaosBlade执行UID
            
        Returns:
            执行记录实例或None
        """
        return await self.get_by_field("chaos_uid", chaos_uid, unique=True)

    async def get_task_executions(
        self,
        task_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: str = "created_at",
        desc: bool = True
    ) -> Tuple[List[ChaosExecution], int]:
        """
        获取任务的执行记录（分页）
        
        Args:
            task_id: 任务ID
            skip: 跳过记录数
            limit: 限制记录数
            order_by: 排序字段
            desc: 是否降序
            
        Returns:
            (执行记录列表, 总数)
        """
        builder = self.query()
        builder = builder.filter_by(task_id=task_id)
        builder = builder.order_by(order_by, desc)

        return await builder.offset(skip).limit(limit).paginate(
            page=(skip // limit) + 1, per_page=limit
        )

    async def search_executions(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        task_name: Optional[str] = None,
        status: Optional[str] = None,
        order_by: str = "created_at",
        desc: bool = True
    ) -> Tuple[List[ChaosExecution], int]:
        """
        搜索执行记录

        Args:
            skip: 跳过记录数
            limit: 限制记录数
            task_name: 任务名称
            status: 执行状态
            order_by: 排序字段
            desc: 是否降序

        Returns:
            (执行记录列表, 总数)
        """
        builder = self.query()

        # 过滤条件
        filters = {}
        if status:
            filters["status"] = status

        if filters:
            builder = builder.filter_by(**filters)

        # 任务名称模糊搜索（通过关联的task表）
        if task_name:
            from app.models.chaos.chaos_task import ChaosTask
            builder = builder.join(ChaosTask).filter(ChaosTask.name.ilike(f"%{task_name}%"))

        # 排序
        builder = builder.order_by(order_by, desc)

        # 预加载任务信息
        builder = builder.preload(ChaosExecution.task)

        # 分页查询
        return await builder.offset(skip).limit(limit).paginate(
            page=(skip // limit) + 1, per_page=limit
        )

    async def get_executions_need_destroy(self) -> List[ChaosExecution]:
        """
        获取需要销毁的执行记录
        
        Returns:
            需要销毁的执行记录列表
        """
        query = (
            select(ChaosExecution)
            .where(
                and_(
                    ChaosExecution.status == "success",
                    ChaosExecution.is_auto_destroyed == False,
                    ChaosExecution.chaos_uid.isnot(None)
                )
            )
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_long_running_executions(self, max_duration_hours: int = 24) -> List[ChaosExecution]:
        """
        获取长时间运行的执行记录
        
        Args:
            max_duration_hours: 最大运行时长（小时）
            
        Returns:
            长时间运行的执行记录列表
        """
        from datetime import timedelta
        
        threshold_time = datetime.now() - timedelta(hours=max_duration_hours)
        
        query = (
            select(ChaosExecution)
            .where(
                and_(
                    ChaosExecution.status == "running",
                    ChaosExecution.start_time <= threshold_time
                )
            )
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def update_execution_status(
        self,
        execution_id: int,
        status: str,
        output: Optional[str] = None,
        error_message: Optional[str] = None,
        exit_code: Optional[int] = None
    ) -> bool:
        """
        更新执行状态
        
        Args:
            execution_id: 执行记录ID
            status: 新状态
            output: 执行输出
            error_message: 错误信息
            exit_code: 退出码
            
        Returns:
            是否更新成功
        """
        execution = await self.get(execution_id)
        if not execution:
            return False

        execution.status = status
        if output:
            execution.output = output
        if error_message:
            execution.error_message = error_message
        if exit_code is not None:
            execution.exit_code = exit_code

        # 如果是完成状态，设置结束时间
        if status in ["success", "failed", "cancelled"]:
            execution.end_time = datetime.now()
            if execution.start_time:
                delta = execution.end_time - execution.start_time
                execution.duration_seconds = int(delta.total_seconds())

        await self.db.commit()
        return True

    async def mark_as_destroyed(self, execution_id: int, destroy_output: Optional[str] = None) -> bool:
        """
        标记执行记录为已销毁
        
        Args:
            execution_id: 执行记录ID
            destroy_output: 销毁命令输出
            
        Returns:
            是否更新成功
        """
        execution = await self.get(execution_id)
        if not execution:
            return False

        execution.is_auto_destroyed = True
        execution.destroy_time = datetime.now()
        if destroy_output:
            execution.destroy_output = destroy_output

        await self.db.commit()
        return True

    async def get_statistics(self, task_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取执行统计信息
        
        Args:
            task_id: 可选的任务ID，用于获取特定任务的统计
            
        Returns:
            统计信息字典
        """
        base_query = select(func.count(ChaosExecution.id))
        
        if task_id:
            base_query = base_query.where(ChaosExecution.task_id == task_id)

        # 总执行数
        total_result = await self.db.execute(base_query)
        total_count = total_result.scalar()

        # 按状态统计
        status_query = (
            select(ChaosExecution.status, func.count(ChaosExecution.id))
            .group_by(ChaosExecution.status)
        )
        if task_id:
            status_query = status_query.where(ChaosExecution.task_id == task_id)

        status_result = await self.db.execute(status_query)
        status_stats = {row[0]: row[1] for row in status_result.fetchall()}

        # 平均执行时长
        avg_duration_query = select(func.avg(ChaosExecution.duration_seconds)).where(
            ChaosExecution.duration_seconds.isnot(None)
        )
        if task_id:
            avg_duration_query = avg_duration_query.where(ChaosExecution.task_id == task_id)

        avg_duration_result = await self.db.execute(avg_duration_query)
        avg_duration = avg_duration_result.scalar() or 0

        return {
            "total_count": total_count,
            "status_stats": status_stats,
            "avg_duration_seconds": round(avg_duration, 2)
        }
