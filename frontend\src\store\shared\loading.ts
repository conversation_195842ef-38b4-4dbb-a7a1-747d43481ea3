/**
 * 通用加载状态管理
 * 提供可复用的加载状态逻辑
 */
import { ref, computed } from 'vue'

/**
 * 加载状态类型
 */
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

/**
 * 加载状态管理选项
 */
export interface LoadingOptions {
  initialState?: LoadingState
  autoReset?: boolean
  resetDelay?: number
}

/**
 * 创建加载状态管理
 */
export function useLoading(options: LoadingOptions = {}) {
  const {
    initialState = 'idle',
    autoReset = false,
    resetDelay = 2000
  } = options

  // 状态
  const state = ref<LoadingState>(initialState)
  const error = ref<string | null>(null)
  const startTime = ref<number | null>(null)

  // 计算属性
  const isIdle = computed(() => state.value === 'idle')
  const isLoading = computed(() => state.value === 'loading')
  const isSuccess = computed(() => state.value === 'success')
  const isError = computed(() => state.value === 'error')
  const duration = computed(() => {
    if (startTime.value && state.value !== 'loading') {
      return Date.now() - startTime.value
    }
    return 0
  })

  // 方法
  const setLoading = () => {
    state.value = 'loading'
    error.value = null
    startTime.value = Date.now()
  }

  const setSuccess = () => {
    state.value = 'success'
    if (autoReset) {
      setTimeout(() => {
        reset()
      }, resetDelay)
    }
  }

  const setError = (errorMessage: string) => {
    state.value = 'error'
    error.value = errorMessage
    if (autoReset) {
      setTimeout(() => {
        reset()
      }, resetDelay)
    }
  }

  const reset = () => {
    state.value = 'idle'
    error.value = null
    startTime.value = null
  }

  // 异步操作包装器
  const withLoading = async <T>(
    asyncFn: () => Promise<T>,
    errorHandler?: (error: any) => string
  ): Promise<T | null> => {
    try {
      setLoading()
      const result = await asyncFn()
      setSuccess()
      return result
    } catch (err) {
      const errorMessage = errorHandler 
        ? errorHandler(err)
        : err instanceof Error 
          ? err.message 
          : '操作失败'
      setError(errorMessage)
      return null
    }
  }

  return {
    // 状态
    state: readonly(state),
    error: readonly(error),
    
    // 计算属性
    isIdle,
    isLoading,
    isSuccess,
    isError,
    duration,
    
    // 方法
    setLoading,
    setSuccess,
    setError,
    reset,
    withLoading
  }
}

/**
 * 多个加载状态管理
 */
export function useMultipleLoading() {
  const loadingStates = ref<Map<string, ReturnType<typeof useLoading>>>(new Map())

  const getLoading = (key: string, options?: LoadingOptions) => {
    if (!loadingStates.value.has(key)) {
      loadingStates.value.set(key, useLoading(options))
    }
    return loadingStates.value.get(key)!
  }

  const isAnyLoading = computed(() => {
    return Array.from(loadingStates.value.values()).some(loading => loading.isLoading.value)
  })

  const resetAll = () => {
    loadingStates.value.forEach(loading => loading.reset())
  }

  const removeLoading = (key: string) => {
    loadingStates.value.delete(key)
  }

  return {
    getLoading,
    isAnyLoading,
    resetAll,
    removeLoading
  }
}

/**
 * 全局加载状态管理
 */
export const globalLoading = useLoading({
  autoReset: true,
  resetDelay: 1000
})
