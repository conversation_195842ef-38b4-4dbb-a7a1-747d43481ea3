<template>
  <ArtSearchBar
    :filter="filter as Record<string, any>"
    :items="searchItems"
    @search="handleSearch"
    @reset="handleReset"
    @update:filter="$emit('update:filter', $event as ChaosExecutionSearchParams)"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import type { ChaosExecutionSearchParams } from '@/types/api/chaos'
import type { SearchFormItem } from '@/types/component'

defineOptions({ name: 'ExecutionSearch' })

interface Props {
  filter: ChaosExecutionSearchParams
}

interface Emits {
  (e: 'search'): void
  (e: 'reset'): void
  (e: 'update:filter', value: ChaosExecutionSearchParams): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 搜索项配置
const searchItems = ref<SearchFormItem[]>([
  {
    prop: 'task_name',
    label: '任务名称',
    type: 'input' as const,
    placeholder: '输入任务名称'
  },
  {
    prop: 'status',
    label: '执行状态',
    type: 'select' as const,
    placeholder: '选择状态',
    options: [
      { label: '待执行', value: 'pending' },
      { label: '运行中', value: 'running' },
      { label: '成功', value: 'success' },
      { label: '失败', value: 'failed' },
      { label: '已取消', value: 'cancelled' }
    ]
  }
])

const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}
</script>
