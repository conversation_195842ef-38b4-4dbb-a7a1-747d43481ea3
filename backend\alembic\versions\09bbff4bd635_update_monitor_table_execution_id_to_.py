"""update monitor table execution_id to environment_id

Revision ID: 09bbff4bd635
Revises: add_monitor_data_table
Create Date: 2025-07-29 22:53:15.284147

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '09bbff4bd635'
down_revision: Union[str, None] = 'add_monitor_data_table'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 简化方案：删除表并重新创建（因为这是开发阶段，数据可以丢失）
    op.drop_table('chaos_monitor_data')

    # 重新创建表，使用environment_id
    op.create_table('chaos_monitor_data',
        sa.Column('id', sa.BigInteger(), nullable=False, comment='主键ID'),
        sa.Column('environment_id', sa.Integer(), nullable=False, comment='环境ID'),
        sa.Column('host_id', sa.Integer(), nullable=False, comment='主机ID'),
        sa.Column('metric_type', sa.String(length=20), nullable=False, comment='指标类型: cpu, memory, network, disk'),
        sa.Column('metric_value', sa.DECIMAL(precision=8, scale=2), nullable=False, comment='指标值'),
        sa.Column('collected_at', sa.TIMESTAMP(), nullable=False, comment='采集时间'),
        sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
        sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建者'),
        sa.Column('updated_by', sa.String(length=50), nullable=True, comment='更新者'),
        sa.ForeignKeyConstraint(['environment_id'], ['environment.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='监控数据表'
    )
    op.create_index('idx_environment_host_time', 'chaos_monitor_data', ['environment_id', 'host_id', 'collected_at'], unique=False)
    op.create_index('idx_cleanup_time', 'chaos_monitor_data', ['collected_at'], unique=False)
    op.create_index(op.f('ix_chaos_monitor_data_id'), 'chaos_monitor_data', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chaos_monitor_data', sa.Column('execution_id', mysql.INTEGER(), autoincrement=False, nullable=False, comment='执行记录ID'))
    op.drop_constraint(None, 'chaos_monitor_data', type_='foreignkey')
    op.create_foreign_key('chaos_monitor_data_ibfk_1', 'chaos_monitor_data', 'chaos_executions', ['execution_id'], ['id'])
    op.drop_index('idx_environment_host_time', table_name='chaos_monitor_data')
    op.create_index('idx_execution_host_time', 'chaos_monitor_data', ['execution_id', 'host_id', 'collected_at'], unique=False)
    op.drop_column('chaos_monitor_data', 'environment_id')
    # ### end Alembic commands ###
