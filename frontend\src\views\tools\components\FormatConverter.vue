<template>
  <div class="format-converter">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6f1;</i>
        <h2>格式转换工具</h2>
      </div>
      <p class="tool-description">
        支持 JSON、XML、YAML、CSV 等格式之间的相互转换
      </p>
    </div>

    <div class="tool-content">
      <!-- 转换配置 -->
      <div class="config-section">
        <div class="config-row">
          <div class="config-item">
            <label class="config-label">源格式</label>
            <ElSelect v-model="sourceFormat" @change="handleFormatChange">
              <ElOption
                v-for="format in supportedFormats"
                :key="format.value"
                :label="format.label"
                :value="format.value"
              />
            </ElSelect>
          </div>
          
          <div class="config-item">
            <label class="config-label">目标格式</label>
            <ElSelect v-model="targetFormat" @change="handleFormatChange">
              <ElOption
                v-for="format in supportedFormats"
                :key="format.value"
                :label="format.label"
                :value="format.value"
                :disabled="format.value === sourceFormat"
              />
            </ElSelect>
          </div>
        </div>

        <!-- 转换选项 -->
        <div class="conversion-options">
          <div class="option-row">
            <ElCheckbox v-model="options.prettyFormat">
              美化输出格式
            </ElCheckbox>
            <ElCheckbox v-model="options.preserveOrder" v-if="sourceFormat === 'json' || targetFormat === 'json'">
              保持字段顺序
            </ElCheckbox>
            <ElCheckbox v-model="options.includeHeader" v-if="targetFormat === 'csv'">
              包含CSV表头
            </ElCheckbox>
          </div>
        </div>
      </div>

      <!-- 输入输出区域 -->
      <div class="conversion-section">
        <div class="conversion-panels">
          <!-- 输入面板 -->
          <div class="conversion-panel">
            <div class="panel-header">
              <h3 class="panel-title">输入数据 ({{ getFormatLabel(sourceFormat) }})</h3>
              <div class="panel-actions">
                <ElButton size="small" @click="clearInput">
                  <i class="iconfont-sys">&#xe622;</i>
                  清空
                </ElButton>
                <ElButton size="small" @click="pasteInput">
                  <i class="iconfont-sys">&#xe623;</i>
                  粘贴
                </ElButton>
                <ElButton size="small" @click="loadSample">
                  <i class="iconfont-sys">&#xe629;</i>
                  示例
                </ElButton>
              </div>
            </div>
            
            <ElInput
              v-model="inputData"
              type="textarea"
              :rows="14"
              :placeholder="getInputPlaceholder()"
              class="data-textarea"
              @input="handleInputChange"
            />
            
            <!-- 输入验证错误 -->
            <div v-if="inputError" class="error-message">
              <i class="iconfont-sys">&#xe62a;</i>
              {{ inputError }}
            </div>
          </div>

          <!-- 输出面板 -->
          <div class="conversion-panel">
            <div class="panel-header">
              <h3 class="panel-title">输出结果 ({{ getFormatLabel(targetFormat) }})</h3>
              <div class="panel-actions">
                <ElButton size="small" @click="copyOutput" :disabled="!outputData">
                  <i class="iconfont-sys">&#xe627;</i>
                  复制
                </ElButton>
                <ElButton size="small" @click="downloadOutput" :disabled="!outputData">
                  <i class="iconfont-sys">&#xe62b;</i>
                  下载
                </ElButton>
              </div>
            </div>
            
            <ElInput
              v-model="outputData"
              type="textarea"
              :rows="14"
              readonly
              placeholder="转换结果将在这里显示..."
              class="data-textarea result-textarea"
            />
            
            <!-- 转换统计 -->
            <div v-if="conversionStats" class="conversion-stats">
              <div class="stat-item">
                <label>输入大小：</label>
                <span>{{ formatFileSize(conversionStats.inputSize) }}</span>
              </div>
              <div class="stat-item">
                <label>输出大小：</label>
                <span>{{ formatFileSize(conversionStats.outputSize) }}</span>
              </div>
              <div class="stat-item">
                <label>压缩比：</label>
                <span>{{ conversionStats.compressionRatio }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 转换按钮 -->
      <div class="action-section">
        <ElButton
          type="primary"
          size="large"
          @click="handleConvert"
          :loading="converting"
          :disabled="!inputData.trim()"
          class="convert-button"
        >
          <i class="iconfont-sys">&#xe626;</i>
          转换格式
        </ElButton>
        
        <ElButton size="large" @click="swapFormats">
          <i class="iconfont-sys">&#xe632;</i>
          交换格式
        </ElButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'FormatConverter' })

// 支持的格式
const supportedFormats = [
  { label: 'JSON', value: 'json' },
  { label: 'XML', value: 'xml' },
  { label: 'YAML', value: 'yaml' },
  { label: 'CSV', value: 'csv' },
  { label: 'TSV', value: 'tsv' }
]

// 响应式数据
const sourceFormat = ref('json')
const targetFormat = ref('xml')
const inputData = ref('')
const outputData = ref('')
const inputError = ref('')
const converting = ref(false)
const conversionStats = ref<any>(null)

// 转换选项
const options = ref({
  prettyFormat: true,
  preserveOrder: false,
  includeHeader: true
})

// 示例数据
const sampleData: { [key: string]: string } = {
  json: `{
  "users": [
    {
      "id": 1,
      "name": "张三",
      "email": "<EMAIL>",
      "age": 25,
      "active": true
    },
    {
      "id": 2,
      "name": "李四",
      "email": "<EMAIL>",
      "age": 30,
      "active": false
    }
  ],
  "total": 2,
  "page": 1
}`,
  
  xml: `<?xml version="1.0" encoding="UTF-8"?>
<root>
  <users>
    <user>
      <id>1</id>
      <name>张三</name>
      <email><EMAIL></email>
      <age>25</age>
      <active>true</active>
    </user>
    <user>
      <id>2</id>
      <name>李四</name>
      <email><EMAIL></email>
      <age>30</age>
      <active>false</active>
    </user>
  </users>
  <total>2</total>
  <page>1</page>
</root>`,

  yaml: `users:
  - id: 1
    name: "张三"
    email: "<EMAIL>"
    age: 25
    active: true
  - id: 2
    name: "李四"
    email: "<EMAIL>"
    age: 30
    active: false
total: 2
page: 1`,

  csv: `id,name,email,age,active
1,张三,<EMAIL>,25,true
2,李四,<EMAIL>,30,false`
}

// 方法
const getFormatLabel = (format: string) => {
  const formatObj = supportedFormats.find(f => f.value === format)
  return formatObj?.label || format.toUpperCase()
}

const getInputPlaceholder = () => {
  const placeholders: { [key: string]: string } = {
    json: '请输入 JSON 数据...',
    xml: '请输入 XML 数据...',
    yaml: '请输入 YAML 数据...',
    csv: '请输入 CSV 数据...',
    tsv: '请输入 TSV 数据...'
  }
  return placeholders[sourceFormat.value] || '请输入数据...'
}

const handleFormatChange = () => {
  inputError.value = ''
  outputData.value = ''
  conversionStats.value = null
}

const handleInputChange = () => {
  inputError.value = ''
  outputData.value = ''
  conversionStats.value = null
}

const handleConvert = () => {
  if (!inputData.value.trim()) {
    ElMessage.warning('请输入要转换的数据')
    return
  }

  converting.value = true
  inputError.value = ''
  
  try {
    // 解析输入数据
    const parsedData = parseInputData(inputData.value, sourceFormat.value)
    
    // 转换为目标格式
    const convertedData = convertToTargetFormat(parsedData, targetFormat.value)
    
    outputData.value = convertedData
    
    // 计算统计信息
    conversionStats.value = {
      inputSize: new Blob([inputData.value]).size,
      outputSize: new Blob([convertedData]).size,
      compressionRatio: Math.round((1 - new Blob([convertedData]).size / new Blob([inputData.value]).size) * 100)
    }
    
    ElMessage.success('转换完成')
  } catch (error) {
    inputError.value = error instanceof Error ? error.message : '转换失败'
    outputData.value = ''
    conversionStats.value = null
    ElMessage.error('转换失败')
  } finally {
    converting.value = false
  }
}

const parseInputData = (data: string, format: string): any => {
  switch (format) {
    case 'json':
      return JSON.parse(data)
      
    case 'xml':
      return parseXML(data)
      
    case 'yaml':
      return parseYAML(data)
      
    case 'csv':
      return parseCSV(data)
      
    case 'tsv':
      return parseTSV(data)
      
    default:
      throw new Error(`不支持的源格式: ${format}`)
  }
}

const convertToTargetFormat = (data: any, format: string): string => {
  switch (format) {
    case 'json':
      return options.value.prettyFormat 
        ? JSON.stringify(data, null, 2)
        : JSON.stringify(data)
        
    case 'xml':
      return convertToXML(data)
      
    case 'yaml':
      return convertToYAML(data)
      
    case 'csv':
      return convertToCSV(data)
      
    case 'tsv':
      return convertToTSV(data)
      
    default:
      throw new Error(`不支持的目标格式: ${format}`)
  }
}

// 简单的 XML 解析器
const parseXML = (xmlString: string): any => {
  const parser = new DOMParser()
  const xmlDoc = parser.parseFromString(xmlString, 'text/xml')
  
  if (xmlDoc.getElementsByTagName('parsererror').length > 0) {
    throw new Error('XML 格式错误')
  }
  
  return xmlToObject(xmlDoc.documentElement)
}

const xmlToObject = (node: Element): any => {
  const result: any = {}
  
  // 处理属性
  if (node.attributes.length > 0) {
    result['@attributes'] = {}
    for (let i = 0; i < node.attributes.length; i++) {
      const attr = node.attributes[i]
      result['@attributes'][attr.name] = attr.value
    }
  }
  
  // 处理子节点
  if (node.children.length > 0) {
    for (let i = 0; i < node.children.length; i++) {
      const child = node.children[i]
      const childName = child.tagName
      const childValue = xmlToObject(child)
      
      if (result[childName]) {
        if (!Array.isArray(result[childName])) {
          result[childName] = [result[childName]]
        }
        result[childName].push(childValue)
      } else {
        result[childName] = childValue
      }
    }
  } else {
    // 叶子节点
    const textContent = node.textContent?.trim()
    if (textContent) {
      return isNaN(Number(textContent)) ? textContent : Number(textContent)
    }
  }
  
  return result
}

// 改进的 YAML 解析器
const parseYAML = (yamlString: string): any => {
  const lines = yamlString.split('\n').filter(line => line.trim() && !line.trim().startsWith('#'))
  const result: any = {}
  const stack: any[] = [{ obj: result, indent: -1, key: null }]

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const indent = line.length - line.trimStart().length
    const trimmed = line.trim()

    // 调整栈深度 - 回到合适的层级
    while (stack.length > 1 && stack[stack.length - 1].indent >= indent) {
      stack.pop()
    }

    const current = stack[stack.length - 1]

    if (trimmed.startsWith('- ')) {
      // 数组项
      const value = trimmed.substring(2).trim()

      // 确保当前容器是数组
      if (!Array.isArray(current.obj)) {
        throw new Error('YAML 格式错误：数组项不在数组中')
      }

      if (value.includes(':')) {
        // 数组中的对象
        const obj = {}
        current.obj.push(obj)
        stack.push({ obj, indent, key: null })

        // 解析第一个键值对
        const colonIndex = value.indexOf(':')
        const key = value.substring(0, colonIndex).trim()
        const val = value.substring(colonIndex + 1).trim()
        obj[key] = parseValue(val)
      } else if (value === '') {
        // 空的数组项，下一行可能是对象
        const nextLine = lines[i + 1]
        if (nextLine && nextLine.length - nextLine.trimStart().length > indent) {
          // 下一行缩进更深，是对象
          const obj = {}
          current.obj.push(obj)
          stack.push({ obj, indent, key: null })
        } else {
          current.obj.push(null)
        }
      } else {
        // 简单值
        current.obj.push(parseValue(value))
      }
    } else if (trimmed.includes(':')) {
      // 键值对
      const colonIndex = trimmed.indexOf(':')
      const key = trimmed.substring(0, colonIndex).trim()
      const value = trimmed.substring(colonIndex + 1).trim()

      if (value === '') {
        // 检查下一行是否是数组项
        const nextLine = lines[i + 1]
        if (nextLine && nextLine.trim().startsWith('- ')) {
          // 下一行是数组项，创建数组
          current.obj[key] = []
          stack.push({ obj: current.obj[key], indent, key })
        } else if (nextLine && nextLine.length - nextLine.trimStart().length > indent) {
          // 下一行缩进更深，创建对象
          current.obj[key] = {}
          stack.push({ obj: current.obj[key], indent, key })
        } else {
          // 空值
          current.obj[key] = null
        }
      } else {
        // 有值的键值对
        current.obj[key] = parseValue(value)
      }
    }
  }

  return result
}

const parseValue = (value: string): any => {
  if (!value || value === '') return ''

  // 移除引号
  const trimmed = value.trim()
  if ((trimmed.startsWith('"') && trimmed.endsWith('"')) ||
      (trimmed.startsWith("'") && trimmed.endsWith("'"))) {
    return trimmed.slice(1, -1)
  }

  // 布尔值
  if (trimmed === 'true') return true
  if (trimmed === 'false') return false

  // null 值
  if (trimmed === 'null' || trimmed === '~') return null

  // 数字
  if (/^-?\d+$/.test(trimmed)) return parseInt(trimmed, 10)
  if (/^-?\d+\.\d+$/.test(trimmed)) return parseFloat(trimmed)

  // 字符串
  return trimmed
}

// CSV 解析器
const parseCSV = (csvString: string): any => {
  const lines = csvString.trim().split('\n')
  if (lines.length === 0) throw new Error('CSV 数据为空')
  
  const headers = lines[0].split(',').map(h => h.trim())
  const data = []
  
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim())
    const row: any = {}
    
    headers.forEach((header, index) => {
      row[header] = parseValue(values[index] || '')
    })
    
    data.push(row)
  }
  
  return data
}

// TSV 解析器
const parseTSV = (tsvString: string): any => {
  const csvString = tsvString.replace(/\t/g, ',')
  return parseCSV(csvString)
}

// 转换为 XML
const convertToXML = (data: any): string => {
  const xmlLines = ['<?xml version="1.0" encoding="UTF-8"?>']
  
  const objectToXML = (obj: any, tagName: string = 'root', indent: string = ''): string => {
    if (Array.isArray(obj)) {
      return obj.map(item => objectToXML(item, tagName, indent)).join('\n')
    }
    
    if (typeof obj === 'object' && obj !== null) {
      const lines = [`${indent}<${tagName}>`]
      
      Object.entries(obj).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(item => {
            lines.push(objectToXML(item, key, indent + '  '))
          })
        } else {
          lines.push(objectToXML(value, key, indent + '  '))
        }
      })
      
      lines.push(`${indent}</${tagName}>`)
      return lines.join('\n')
    }
    
    return `${indent}<${tagName}>${obj}</${tagName}>`
  }
  
  xmlLines.push(objectToXML(data))
  return xmlLines.join('\n')
}

// 转换为 YAML
const convertToYAML = (data: any): string => {
  const objectToYAML = (obj: any, indent: string = ''): string => {
    if (Array.isArray(obj)) {
      return obj.map(item => {
        if (typeof item === 'object' && item !== null) {
          // 数组中的对象
          const entries = Object.entries(item)
          if (entries.length === 0) {
            return `${indent}- {}`
          }

          const lines: string[] = []
          entries.forEach(([key, value], index) => {
            if (index === 0) {
              // 第一个属性与 - 在同一行
              if (typeof value === 'object' && value !== null) {
                lines.push(`${indent}- ${key}:`)
                lines.push(objectToYAML(value, indent + '    '))
              } else {
                lines.push(`${indent}- ${key}: ${formatValue(value)}`)
              }
            } else {
              // 后续属性需要正确缩进
              if (typeof value === 'object' && value !== null) {
                lines.push(`${indent}  ${key}:`)
                lines.push(objectToYAML(value, indent + '    '))
              } else {
                lines.push(`${indent}  ${key}: ${formatValue(value)}`)
              }
            }
          })
          return lines.join('\n')
        } else {
          // 数组中的简单值
          return `${indent}- ${formatValue(item)}`
        }
      }).join('\n')
    }

    if (typeof obj === 'object' && obj !== null) {
      return Object.entries(obj).map(([key, value]) => {
        if (Array.isArray(value)) {
          if (value.length === 0) {
            return `${indent}${key}: []`
          }
          return `${indent}${key}:\n${objectToYAML(value, indent + '  ')}`
        } else if (typeof value === 'object' && value !== null) {
          const subYaml = objectToYAML(value, indent + '  ')
          return `${indent}${key}:\n${subYaml}`
        } else {
          return `${indent}${key}: ${formatValue(value)}`
        }
      }).join('\n')
    }

    return formatValue(obj)
  }

  const formatValue = (value: any): string => {
    if (value === null) return 'null'
    if (typeof value === 'string') {
      // 如果字符串包含特殊字符，需要引号
      if (value.includes(':') || value.includes('\n') || value.includes('#') ||
          value.includes('[') || value.includes(']') || value.includes('{') || value.includes('}') ||
          value.trim() !== value) {
        return `"${value.replace(/"/g, '\\"')}"`
      }
      return value
    }
    return String(value)
  }

  return objectToYAML(data)
}

// 转换为 CSV
const convertToCSV = (data: any): string => {
  if (!Array.isArray(data)) {
    throw new Error('CSV 格式要求数据为数组')
  }
  
  if (data.length === 0) return ''
  
  const headers = Object.keys(data[0])
  const lines = []
  
  if (options.value.includeHeader) {
    lines.push(headers.join(','))
  }
  
  data.forEach(row => {
    const values = headers.map(header => String(row[header] || ''))
    lines.push(values.join(','))
  })
  
  return lines.join('\n')
}

// 转换为 TSV
const convertToTSV = (data: any): string => {
  const csvString = convertToCSV(data)
  return csvString.replace(/,/g, '\t')
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const clearInput = () => {
  inputData.value = ''
  outputData.value = ''
  inputError.value = ''
  conversionStats.value = null
}

const pasteInput = async () => {
  try {
    const text = await navigator.clipboard.readText()
    inputData.value = text
    handleInputChange()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const loadSample = () => {
  const sample = sampleData[sourceFormat.value] || sampleData.json
  inputData.value = sample
  handleInputChange()
  ElMessage.success('已加载示例数据')
}

const copyOutput = async () => {
  if (!outputData.value) return
  
  try {
    await navigator.clipboard.writeText(outputData.value)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadOutput = () => {
  if (!outputData.value) return
  
  const extensions: { [key: string]: string } = {
    json: 'json',
    xml: 'xml',
    yaml: 'yaml',
    csv: 'csv',
    tsv: 'tsv'
  }
  
  const ext = extensions[targetFormat.value] || 'txt'
  const filename = `converted_data_${Date.now()}.${ext}`
  
  const blob = new Blob([outputData.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('下载成功')
}

const swapFormats = () => {
  const temp = sourceFormat.value
  sourceFormat.value = targetFormat.value
  targetFormat.value = temp
  
  // 交换输入输出数据
  const tempData = inputData.value
  inputData.value = outputData.value
  outputData.value = tempData
  
  inputError.value = ''
  conversionStats.value = null
  
  ElMessage.success('格式已交换')
}
</script>

<style lang="scss" scoped>
.format-converter {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .config-section,
    .conversion-section,
    .action-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }

    .config-section {
      .config-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 16px;

        .config-item {
          .config-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 8px;
          }
        }
      }

      .conversion-options {
        border-top: 1px solid var(--el-border-color-lighter);
        padding-top: 16px;

        .option-row {
          display: flex;
          gap: 20px;
          flex-wrap: wrap;
        }
      }
    }

    .conversion-section {
      .conversion-panels {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .conversion-panel {
          .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            .panel-title {
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              margin: 0;
            }

            .panel-actions {
              display: flex;
              gap: 8px;
            }
          }

          .data-textarea {
            :deep(.el-textarea__inner) {
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 13px;
              line-height: 1.5;
            }

            &.result-textarea {
              :deep(.el-textarea__inner) {
                background: var(--el-fill-color-lighter);
              }
            }
          }

          .error-message {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            padding: 8px 12px;
            background: var(--el-color-error-light-9);
            color: var(--el-color-error);
            border-radius: 4px;
            font-size: 13px;

            i {
              font-size: 14px;
            }
          }

          .conversion-stats {
            display: flex;
            gap: 16px;
            margin-top: 12px;
            padding: 8px 12px;
            background: var(--el-fill-color-light);
            border-radius: 4px;
            font-size: 13px;

            .stat-item {
              display: flex;
              align-items: center;
              gap: 4px;

              label {
                color: var(--el-text-color-regular);
              }

              span {
                font-weight: 600;
                color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }

    .action-section {
      display: flex;
      justify-content: center;
      gap: 16px;

      .convert-button {
        min-width: 120px;
        height: 40px;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .format-converter {
    .tool-content {
      .config-section {
        .config-row {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        .conversion-options {
          .option-row {
            flex-direction: column;
            gap: 8px;
          }
        }
      }

      .conversion-section {
        .conversion-panels {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }

      .action-section {
        flex-direction: column;
        align-items: center;
      }
    }
  }
}
</style>
