<template>
  <div class="text-diff">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6ee;</i>
        <h2>文本对比工具</h2>
      </div>
      <p class="tool-description">
        对比两段文本的差异，支持逐行对比和字符级对比
      </p>
    </div>

    <div class="tool-content">
      <!-- 对比选项 -->
      <div class="config-section">
        <div class="config-row">
          <div class="config-item">
            <label class="config-label">对比模式</label>
            <ElSelect v-model="compareMode" @change="handleCompare">
              <ElOption label="逐行对比" value="line" />
              <ElOption label="字符对比" value="char" />
              <ElOption label="单词对比" value="word" />
            </ElSelect>
          </div>
          
          <div class="config-item">
            <label class="config-label">忽略选项</label>
            <div class="ignore-options">
              <ElCheckbox v-model="ignoreWhitespace" @change="handleCompare">
                忽略空白字符
              </ElCheckbox>
              <ElCheckbox v-model="ignoreCase" @change="handleCompare">
                忽略大小写
              </ElCheckbox>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <div class="input-panels">
          <!-- 左侧文本 -->
          <div class="input-panel">
            <div class="panel-header">
              <h3 class="panel-title">原始文本</h3>
              <div class="panel-actions">
                <ElButton size="small" @click="clearLeft">
                  <i class="iconfont-sys">&#xe622;</i>
                  清空
                </ElButton>
                <ElButton size="small" @click="pasteLeft">
                  <i class="iconfont-sys">&#xe623;</i>
                  粘贴
                </ElButton>
              </div>
            </div>
            
            <ElInput
              v-model="leftText"
              type="textarea"
              :rows="12"
              placeholder="请输入原始文本..."
              class="text-textarea"
              @input="handleTextChange"
            />
          </div>

          <!-- 右侧文本 -->
          <div class="input-panel">
            <div class="panel-header">
              <h3 class="panel-title">对比文本</h3>
              <div class="panel-actions">
                <ElButton size="small" @click="clearRight">
                  <i class="iconfont-sys">&#xe622;</i>
                  清空
                </ElButton>
                <ElButton size="small" @click="pasteRight">
                  <i class="iconfont-sys">&#xe623;</i>
                  粘贴
                </ElButton>
              </div>
            </div>
            
            <ElInput
              v-model="rightText"
              type="textarea"
              :rows="12"
              placeholder="请输入对比文本..."
              class="text-textarea"
              @input="handleTextChange"
            />
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <ElButton
          type="primary"
          size="large"
          @click="handleCompare"
          :loading="comparing"
          class="compare-button"
        >
          <i class="iconfont-sys">&#xe626;</i>
          开始对比
        </ElButton>
        
        <ElButton size="large" @click="swapTexts">
          <i class="iconfont-sys">&#xe632;</i>
          交换文本
        </ElButton>
      </div>

      <!-- 结果展示区域 -->
      <div v-if="diffResult" class="result-section">
        <div class="section-header">
          <h3 class="section-title">对比结果</h3>
          <div class="section-actions">
            <ElButton size="small" @click="exportDiff">
              <i class="iconfont-sys">&#xe62b;</i>
              导出结果
            </ElButton>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="diff-stats">
          <div class="stat-item added">
            <i class="iconfont-sys">&#xe633;</i>
            <span>新增：{{ diffResult.stats.added }}</span>
          </div>
          <div class="stat-item deleted">
            <i class="iconfont-sys">&#xe634;</i>
            <span>删除：{{ diffResult.stats.deleted }}</span>
          </div>
          <div class="stat-item modified">
            <i class="iconfont-sys">&#xe635;</i>
            <span>修改：{{ diffResult.stats.modified }}</span>
          </div>
          <div class="stat-item unchanged">
            <i class="iconfont-sys">&#xe636;</i>
            <span>相同：{{ diffResult.stats.unchanged }}</span>
          </div>
        </div>
        
        <!-- 差异内容 -->
        <div class="diff-content">
          <div class="diff-panels">
            <!-- 左侧差异 -->
            <div class="diff-panel">
              <div class="panel-title">原始文本</div>
              <div class="diff-lines">
                <div
                  v-for="(line, index) in diffResult.left"
                  :key="index"
                  class="diff-line"
                  :class="line.type"
                >
                  <span class="line-number">{{ line.lineNumber }}</span>
                  <span class="line-content" v-html="line.content"></span>
                </div>
              </div>
            </div>

            <!-- 右侧差异 -->
            <div class="diff-panel">
              <div class="panel-title">对比文本</div>
              <div class="diff-lines">
                <div
                  v-for="(line, index) in diffResult.right"
                  :key="index"
                  class="diff-line"
                  :class="line.type"
                >
                  <span class="line-number">{{ line.lineNumber }}</span>
                  <span class="line-content" v-html="line.content"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'TextDiff' })

// 响应式数据
const leftText = ref('')
const rightText = ref('')
const compareMode = ref('line')
const ignoreWhitespace = ref(false)
const ignoreCase = ref(false)
const comparing = ref(false)
const diffResult = ref<any>(null)

// 方法
const handleTextChange = () => {
  diffResult.value = null
}

const handleCompare = () => {
  if (!leftText.value.trim() && !rightText.value.trim()) {
    ElMessage.warning('请输入要对比的文本')
    return
  }

  comparing.value = true
  
  try {
    const result = performDiff(leftText.value, rightText.value)
    diffResult.value = result
    ElMessage.success('对比完成')
  } catch (error) {
    ElMessage.error('对比失败')
    diffResult.value = null
  } finally {
    comparing.value = false
  }
}

const performDiff = (left: string, right: string) => {
  // 预处理文本
  let leftProcessed = left
  let rightProcessed = right
  
  if (ignoreCase.value) {
    leftProcessed = leftProcessed.toLowerCase()
    rightProcessed = rightProcessed.toLowerCase()
  }
  
  if (ignoreWhitespace.value) {
    leftProcessed = leftProcessed.replace(/\s+/g, ' ').trim()
    rightProcessed = rightProcessed.replace(/\s+/g, ' ').trim()
  }
  
  // 根据模式分割文本
  let leftParts: string[]
  let rightParts: string[]
  
  switch (compareMode.value) {
    case 'line':
      leftParts = leftProcessed.split('\n')
      rightParts = rightProcessed.split('\n')
      break
    case 'word':
      leftParts = leftProcessed.split(/\s+/)
      rightParts = rightProcessed.split(/\s+/)
      break
    case 'char':
      leftParts = leftProcessed.split('')
      rightParts = rightProcessed.split('')
      break
    default:
      leftParts = leftProcessed.split('\n')
      rightParts = rightProcessed.split('\n')
  }
  
  // 执行 LCS 算法
  const diff = computeLCS(leftParts, rightParts)
  
  // 生成差异结果
  return generateDiffResult(diff, leftParts, rightParts)
}

const computeLCS = (left: string[], right: string[]) => {
  const m = left.length
  const n = right.length
  const dp: number[][] = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0))
  
  // 计算 LCS 长度
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (left[i - 1] === right[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1
      } else {
        dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1])
      }
    }
  }
  
  // 回溯生成差异序列
  const diff: Array<{ type: string; leftIndex?: number; rightIndex?: number }> = []
  let i = m, j = n
  
  while (i > 0 || j > 0) {
    if (i > 0 && j > 0 && left[i - 1] === right[j - 1]) {
      diff.unshift({ type: 'equal', leftIndex: i - 1, rightIndex: j - 1 })
      i--
      j--
    } else if (j > 0 && (i === 0 || dp[i][j - 1] >= dp[i - 1][j])) {
      diff.unshift({ type: 'insert', rightIndex: j - 1 })
      j--
    } else if (i > 0) {
      diff.unshift({ type: 'delete', leftIndex: i - 1 })
      i--
    }
  }
  
  return diff
}

const generateDiffResult = (diff: any[], leftParts: string[], rightParts: string[]) => {
  const leftLines: any[] = []
  const rightLines: any[] = []
  const stats = { added: 0, deleted: 0, modified: 0, unchanged: 0 }
  
  let leftLineNum = 1
  let rightLineNum = 1
  
  for (const item of diff) {
    switch (item.type) {
      case 'equal':
        leftLines.push({
          type: 'unchanged',
          lineNumber: leftLineNum++,
          content: escapeHtml(leftParts[item.leftIndex])
        })
        rightLines.push({
          type: 'unchanged',
          lineNumber: rightLineNum++,
          content: escapeHtml(rightParts[item.rightIndex])
        })
        stats.unchanged++
        break
        
      case 'delete':
        leftLines.push({
          type: 'deleted',
          lineNumber: leftLineNum++,
          content: escapeHtml(leftParts[item.leftIndex])
        })
        rightLines.push({
          type: 'empty',
          lineNumber: '',
          content: ''
        })
        stats.deleted++
        break
        
      case 'insert':
        leftLines.push({
          type: 'empty',
          lineNumber: '',
          content: ''
        })
        rightLines.push({
          type: 'added',
          lineNumber: rightLineNum++,
          content: escapeHtml(rightParts[item.rightIndex])
        })
        stats.added++
        break
    }
  }
  
  return {
    left: leftLines,
    right: rightLines,
    stats
  }
}

const escapeHtml = (text: string): string => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

const clearLeft = () => {
  leftText.value = ''
  diffResult.value = null
}

const clearRight = () => {
  rightText.value = ''
  diffResult.value = null
}

const pasteLeft = async () => {
  try {
    const text = await navigator.clipboard.readText()
    leftText.value = text
    diffResult.value = null
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const pasteRight = async () => {
  try {
    const text = await navigator.clipboard.readText()
    rightText.value = text
    diffResult.value = null
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const swapTexts = () => {
  const temp = leftText.value
  leftText.value = rightText.value
  rightText.value = temp
  diffResult.value = null
  ElMessage.success('文本已交换')
}

const exportDiff = () => {
  if (!diffResult.value) return
  
  const content = generateDiffReport()
  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `text_diff_${Date.now()}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('导出成功')
}

const generateDiffReport = (): string => {
  if (!diffResult.value) return ''
  
  const { stats } = diffResult.value
  let report = '文本对比报告\n'
  report += '=' .repeat(50) + '\n\n'
  report += `对比模式：${compareMode.value === 'line' ? '逐行对比' : compareMode.value === 'word' ? '单词对比' : '字符对比'}\n`
  report += `忽略大小写：${ignoreCase.value ? '是' : '否'}\n`
  report += `忽略空白字符：${ignoreWhitespace.value ? '是' : '否'}\n\n`
  report += '统计信息：\n'
  report += `- 新增：${stats.added}\n`
  report += `- 删除：${stats.deleted}\n`
  report += `- 修改：${stats.modified}\n`
  report += `- 相同：${stats.unchanged}\n\n`
  report += '详细差异：\n'
  report += '-'.repeat(50) + '\n'
  
  // 添加详细差异内容
  for (let i = 0; i < diffResult.value.left.length; i++) {
    const leftLine = diffResult.value.left[i]
    const rightLine = diffResult.value.right[i]
    
    if (leftLine.type === 'deleted') {
      report += `- ${leftLine.content}\n`
    } else if (rightLine.type === 'added') {
      report += `+ ${rightLine.content}\n`
    } else if (leftLine.type === 'unchanged') {
      report += `  ${leftLine.content}\n`
    }
  }
  
  return report
}
</script>

<style lang="scss" scoped>
.text-diff {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .config-section,
    .input-section,
    .action-section,
    .result-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }

        .section-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .config-section {
      .config-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .config-item {
          .config-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 8px;
          }

          .ignore-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }

    .input-section {
      .input-panels {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .input-panel {
          .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            .panel-title {
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              margin: 0;
            }

            .panel-actions {
              display: flex;
              gap: 8px;
            }
          }
        }
      }
    }

    .text-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }

    .action-section {
      display: flex;
      justify-content: center;
      gap: 16px;

      .compare-button {
        min-width: 120px;
        height: 40px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .diff-stats {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;
      flex-wrap: wrap;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;

        &.added {
          background: var(--el-color-success-light-9);
          color: var(--el-color-success);
        }

        &.deleted {
          background: var(--el-color-error-light-9);
          color: var(--el-color-error);
        }

        &.modified {
          background: var(--el-color-warning-light-9);
          color: var(--el-color-warning);
        }

        &.unchanged {
          background: var(--el-fill-color-light);
          color: var(--el-text-color-regular);
        }

        i {
          font-size: 16px;
        }
      }
    }

    .diff-content {
      .diff-panels {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1px;
        border: 1px solid var(--el-border-color);
        border-radius: 6px;
        overflow: hidden;

        .diff-panel {
          background: var(--el-bg-color);

          .panel-title {
            padding: 8px 12px;
            background: var(--el-fill-color-light);
            font-size: 13px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            border-bottom: 1px solid var(--el-border-color);
          }

          .diff-lines {
            max-height: 400px;
            overflow-y: auto;

            .diff-line {
              display: flex;
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 12px;
              line-height: 1.4;
              border-bottom: 1px solid var(--el-border-color-lighter);

              &:last-child {
                border-bottom: none;
              }

              .line-number {
                width: 40px;
                padding: 4px 8px;
                background: var(--el-fill-color-lighter);
                color: var(--el-text-color-placeholder);
                text-align: right;
                border-right: 1px solid var(--el-border-color-lighter);
                flex-shrink: 0;
              }

              .line-content {
                padding: 4px 8px;
                flex: 1;
                white-space: pre-wrap;
                word-break: break-all;
              }

              &.added {
                background: var(--el-color-success-light-9);
                
                .line-content {
                  color: var(--el-color-success-dark-2);
                }
              }

              &.deleted {
                background: var(--el-color-error-light-9);
                
                .line-content {
                  color: var(--el-color-error-dark-2);
                }
              }

              &.unchanged {
                background: var(--el-bg-color);
              }

              &.empty {
                background: var(--el-fill-color-lighter);
                
                .line-content {
                  color: transparent;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .text-diff {
    .tool-content {
      .config-section {
        .config-row {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }

      .input-section {
        .input-panels {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }

      .action-section {
        flex-direction: column;
        align-items: center;
      }

      .diff-content {
        .diff-panels {
          grid-template-columns: 1fr;
        }
      }

      .diff-stats {
        justify-content: center;
      }
    }
  }
}
</style>
