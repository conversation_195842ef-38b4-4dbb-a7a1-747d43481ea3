<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
    width="30%"
    align-center
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <ElForm 
      ref="formRef" 
      :model="formData" 
      :rules="rules" 
      label-width="80px"
      v-loading="isLoading"
      element-loading-text="处理中..."
    >
      <ElFormItem label="账号" prop="username">
        <ElInput 
          v-model="formData.username" 
          :disabled="dialogType === 'edit'"
          placeholder="请输入用户账号"
        />
      </ElFormItem>
      <ElFormItem label="用户名" prop="nickname">
        <ElInput 
          v-model="formData.nickname" 
          placeholder="请输入用户名"
        />
      </ElFormItem>
      <ElFormItem label="邮箱" prop="email">
        <ElInput 
          v-model="formData.email" 
          placeholder="请输入邮箱"
        />
      </ElFormItem>
      <ElFormItem 
        v-if="dialogType === 'add'" 
        label="密码" 
        prop="password"
      >
        <ElInput 
          v-model="formData.password" 
          type="password" 
          placeholder="请输入密码"
          show-password
        />
      </ElFormItem>
      <ElFormItem label="角色" prop="role_ids">
        <ElSelect 
          v-model="formData.role_ids" 
          multiple 
          placeholder="请选择角色"
          style="width: 100%"
          :loading="roleLoading"
        >
          <ElOption
            v-for="role in roleList"
            :key="role.id"
            :value="role.id"
            :label="role.roleName"
          />
        </ElSelect>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel" :disabled="isLoading">取消</ElButton>
        <ElButton 
          type="primary" 
          @click="handleSubmit" 
          :loading="isLoading"
        >
          {{ dialogType === 'add' ? '新增' : '更新' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { UserService } from '@/api/usersApi'

  interface Props {
    visible: boolean
    type: string
    userData?: any
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 加载状态
  const isLoading = ref(false)
  const roleLoading = ref(false)
  
  // 角色列表数据
  const roleList = ref<Api.User.RoleInfo[]>([])

  // 对话框显示控制
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogType = computed(() => props.type)

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    username: '',
    nickname: '',
    email: '',
    password: '',
    role_ids: [] as number[]
  })

  // 表单验证规则
  const rules: FormRules = {
    username: [
      { required: true, message: '请输入用户账号', trigger: 'blur' },
      { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
    ],
    nickname: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 50, message: '长度在 6 到 50 个字符', trigger: 'blur' }
    ],
    role_ids: [{ required: true, message: '请选择角色', trigger: 'change' }]
  }

  // 获取角色列表
  const fetchRoleList = async () => {
  try {
    roleLoading.value = true
    const roleData = await UserService.getRoleList() // HTTP工具已返回data部分
    roleList.value = (roleData as any) || []
  } catch (error) {
    ElMessage.error('获取角色列表失败，请重试')
  } finally {
    roleLoading.value = false
  }
}

  // 初始化表单数据
  const initFormData = () => {
    const isEdit = props.type === 'edit' && props.userData
    const row = props.userData

    // 重置表单数据
    Object.assign(formData, {
      username: isEdit ? row.username || '' : '',
      nickname: isEdit ? row.nickName || '' : '',
      email: isEdit ? row.userEmail || '' : '',
      password: '',
      role_ids: isEdit ? (Array.isArray(row.userRoles) ? 
        roleList.value
          .filter(role => row.userRoles.includes(role.roleCode))
          .map(role => role.id) : []) : []
    })
  }

  // 统一监听对话框状态变化
  watch(
    () => [props.visible, props.type, props.userData],
    async ([visible]) => {
      if (visible) {
        // 获取角色列表
        if (roleList.value.length === 0) {
          await fetchRoleList()
        }
        
        // 初始化表单数据
        initFormData()
        
        // 清除验证信息
        nextTick(() => {
          formRef.value?.clearValidate()
        })
      }
    },
    { immediate: true }
  )

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      isLoading.value = true

      const submitData = {
        username: formData.username,
        nickname: formData.nickname,
        email: formData.email,
        role_ids: formData.role_ids
      }

      let response: Api.Common.BaseResponse

      if (dialogType.value === 'add') {
        // 新增用户
        response = await UserService.addUser({
          ...submitData,
          password: formData.password
        })
      } else {
        // 编辑用户
        const userId = props.userData?.id
        if (!userId) {
          ElMessage.error('用户ID不存在')
          return
        }
        response = await UserService.updateUser(userId, submitData)
      }

      if (response) {
        ElMessage.success(response.message || (dialogType.value === 'add' ? '添加成功' : '更新成功'))
        dialogVisible.value = false
        emit('submit')
      } else {
        ElMessage.error(response || '操作失败')
      }
    } catch (error: any) {
      
      // 处理特定错误信息
      let errorMessage = '操作失败，请重试'
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      }
      
      ElMessage.error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  // 取消操作
  const handleCancel = () => {
    if (isLoading.value) return
    
    // 检查表单是否有未保存的更改
    const hasChanges = dialogType.value === 'add' ? 
      formData.username || formData.nickname || formData.email || formData.password || formData.role_ids.length > 0 :
      formData.nickname !== (props.userData?.nickName || '') ||
      formData.email !== (props.userData?.userEmail || '') ||
      JSON.stringify(formData.role_ids) !== JSON.stringify(
        roleList.value
          .filter(role => props.userData?.userRoles?.includes(role.roleCode))
          .map(role => role.id) || []
      )

    if (hasChanges) {
      ElMessageBox.confirm(
        '确定要关闭吗？未保存的更改将会丢失。',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        dialogVisible.value = false
      }).catch(() => {
        // 用户取消关闭
      })
    } else {
      dialogVisible.value = false
    }
  }

  // 对话框关闭后的清理
  const handleDialogClosed = () => {
    // 重置表单数据
    Object.assign(formData, {
      username: '',
      nickname: '',
      email: '',
      password: '',
      role_ids: []
    })
    
    // 清除验证信息
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  // 页面卸载时清理
  onUnmounted(() => {
    roleList.value = []
  })
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
