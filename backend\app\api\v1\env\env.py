"""
环境管理API路由
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, Path

from app.core.dependencies import EnvironmentServiceType
from app.core.dependencies import get_pagination_params
from app.core.responses import response_builder
from app.schemas.base import APIResponse, PaginationResponse
from app.schemas.env.env import EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse, ConnectionTestRequest, ConnectionTestResponse
from app.schemas.common import PaginationParams
from app.models.user.user import User
from app.api.deps import get_current_active_user

router = APIRouter()


@router.get("/available", response_model=APIResponse[List[EnvironmentResponse]], summary="获取可用环境列表")
async def get_available_environments(
    service: EnvironmentServiceType
):
    """获取可用环境列表（简化版，只包含基本信息）"""
    # 构建搜索参数，获取所有已连接的环境
    from app.schemas.common import SearchParams
    search_params = SearchParams(
        current=1,
        size=100,  # 获取大量数据，实际应该根据需要调整
        keyword=None
    )

    environments, total = await service.list_environments(
        search_params,
        status="connected" ,
        env_type="ssh" # 只获取已连接的环境
    )

    return response_builder.success(data=environments, message="获取可用环境列表成功")


@router.post("/", response_model=APIResponse[EnvironmentResponse], summary="创建环境")
async def create_environment(
    env_data: EnvironmentCreate,
    service: EnvironmentServiceType,
    current_user: User = Depends(get_current_active_user)
):
    """创建新环境配置"""
    environment = await service.create_environment(env_data, str(current_user.id))
    return response_builder.success(data=environment, message="创建环境成功")


@router.get("/list", response_model=PaginationResponse[EnvironmentResponse], summary="获取环境列表")
async def get_environment_list(
    service: EnvironmentServiceType,
    name: str = Query(None, description="环境名称搜索关键词"),
    env_type: str = Query(None, description="环境类型"),
    status: str = Query(None, description="环境状态"),
    tags: str = Query(None, description="标签"),
    pagination: PaginationParams = Depends(get_pagination_params)
):
    """获取环境列表"""
    # 构建搜索参数
    from app.schemas.common import SearchParams
    search_params = SearchParams(
        current=pagination.current,
        size=pagination.size,
        keyword=name
    )
    
    environments, total = await service.list_environments(
        search_params, 
        env_type=env_type, 
        status=status, 
        tags=tags
    )
    
    return response_builder.paginated(
        records=environments,
        total=total,
        current=pagination.current,
        size=pagination.size,
        message="获取环境列表成功"
    )


@router.get("/{env_id}", response_model=APIResponse[EnvironmentResponse], summary="获取环境详情")
async def get_environment(
    service: EnvironmentServiceType,
    env_id: int = Path(..., description="环境ID")
):
    """获取环境详情"""
    environment = await service.get_environment(env_id)
    return response_builder.success(data=environment, message="获取环境详情成功")


@router.put("/{env_id}", response_model=APIResponse[EnvironmentResponse], summary="更新环境")
async def update_environment(
    service: EnvironmentServiceType,
    env_data: EnvironmentUpdate,
    env_id: int = Path(..., description="环境ID"),
    current_user: User = Depends(get_current_active_user)
):
    """更新环境配置"""
    environment = await service.update_environment(env_id, env_data, str(current_user.id))
    return response_builder.success(data=environment, message="更新环境成功")


@router.delete("/{env_id}", response_model=APIResponse[bool], summary="删除环境")
async def delete_environment(
    service: EnvironmentServiceType,
    env_id: int = Path(..., description="环境ID")
):
    """删除环境"""
    await service.delete_environment(env_id)
    return response_builder.success(data=True, message="删除环境成功")


@router.post("/{env_id}/test", response_model=APIResponse[ConnectionTestResponse], summary="测试指定环境连接")
async def test_environment_connection(
    service: EnvironmentServiceType,
    env_id: int = Path(..., description="环境ID"),
    test_request: ConnectionTestRequest = ConnectionTestRequest()
):
    """测试指定环境的连接"""
    result = await service.test_environment_connection(env_id, test_request)
    return response_builder.success(data=result, message="环境连接测试完成")


@router.post("/test", response_model=APIResponse[ConnectionTestResponse], summary="测试环境连接")
async def test_connection(
    service: EnvironmentServiceType,
    test_request: ConnectionTestRequest
):
    """测试环境连接"""
    result = await service.test_connection(test_request)
    return response_builder.success(data=result, message="连接测试完成")


@router.post("/test-config", response_model=APIResponse[ConnectionTestResponse], summary="测试环境配置连接")
async def test_config_connection(
    service: EnvironmentServiceType,
    config_data: dict
):
    """
    直接测试环境配置连接（不需要先保存环境）

    Args:
        config_data: 环境配置数据
            - type: 环境类型
            - host: 主机地址
            - port: 端口
            - config: 详细配置
    """
    # 构建测试请求
    test_request = ConnectionTestRequest(
        type=config_data.get('type'),
        config={
            'host': config_data.get('host'),
            'port': config_data.get('port'),
            **config_data.get('config', {})
        }
    )

    result = await service.test_connection(test_request)
    return response_builder.success(data=result, message="配置连接测试完成")


@router.post("/batch-test", response_model=APIResponse[List[dict]], summary="批量测试环境连接")
async def batch_test_connections(
    service: EnvironmentServiceType,
    env_ids: List[int]
):
    """批量测试环境连接"""
    results = await service.batch_test_connections(env_ids)
    return response_builder.success(data=results, message="批量连接测试完成")


@router.get("/stats/overview", response_model=APIResponse[dict], summary="获取环境统计信息")
async def get_environment_stats(
    service: EnvironmentServiceType
):
    """获取环境统计信息"""
    stats = await service.get_environment_stats()
    return response_builder.success(data=stats, message="获取统计信息成功")


@router.get("/types/supported", response_model=APIResponse[List[Dict[str, Any]]], summary="获取支持的环境类型")
async def get_supported_types(
    service: EnvironmentServiceType
):
    """获取系统支持的环境类型"""
    types = await service.get_supported_types()
    return response_builder.success(data=types, message="获取支持的环境类型成功")