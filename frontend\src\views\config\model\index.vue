<template>
  <div class=" art-full-height">
    <!-- 搜索栏 -->
    <ModelSearch v-model:filter="defaultFilter" @reset="resetSearch" @search="handleSearch" />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton @click="showDialog('add')" type="primary" >新增模型</ElButton>
          <ElButton 
            v-if="selectedRows.length > 0" 
            @click="batchHealthCheck"
            :loading="isLoading"
          >
            批量健康检查
          </ElButton>
        </template>
        <template #right>
          <ElButton @click="showStatsDialog" type="success" style="margin-left: 5px;">统计信息</ElButton>
          <ElButton @click="showCallDialog" type="primary" >模型调用</ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        @selection-change="handleSelectionChange"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
      </ArtTable>

      <!-- 模型对话框 -->
      <ModelDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :model-config="currentModelConfig"
        @submit="handleDialogSubmit"
      />

      <!-- 统计信息对话框 -->
      <ModelStatsDialog
        v-model:visible="statsDialogVisible"
      />

      <!-- 模型调用对话框 -->
      <ModelCallDialog
        v-model:visible="callDialogVisible"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
import { useTable } from '@/composables/useTable'
import ArtTableHeader from '@/components/core/tables/art-table-header/index.vue'
import ArtTable from '@/components/core/tables/art-table/index.vue'
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
import { useModelStore } from '@/store/business/model/index'
import type { ModelConfigResponse } from '@/types/api/model'
import { MODEL_PLATFORM_CONFIG, MODEL_STATUS_CONFIG, MODEL_HEALTH_STATUS_CONFIG } from '@/types/api/model'
import ModelSearch from './modules/model-search.vue'
import ModelDialog from './modules/model-dialog.vue'
import ModelStatsDialog from './modules/model-stats-dialog.vue'
import ModelCallDialog from './modules/model-call-dialog.vue'

defineOptions({ name: 'ModelConfig' })

const modelStore = useModelStore()

// 弹窗相关
const dialogType = ref<Form.DialogType>('add')
const dialogVisible = ref(false)
const statsDialogVisible = ref(false)
const callDialogVisible = ref(false)
const currentModelConfig = ref<ModelConfigResponse | null>(null)

// 选中行
const selectedRows = ref<ModelConfigResponse[]>([])

// 表单搜索初始值
const defaultFilter = ref({
  name: undefined,
  platform: undefined,
  status: undefined,
  health_status: undefined
})

// 表格配置
const {
  isLoading,
  tableData,
  paginationState,
  columnChecks,
  onPageSizeChange,
  onCurrentPageChange,
  refreshAll
} = useTable({
  core: {
    apiFn: async (params) => {
      const response = await modelStore.fetchModelConfigs(params)
      if (!response) {
        return {
          records: [],
          total: 0,
          current: 1,
          size: 20
        }
      }
      return {
        records: response.records,
        total: response.total,
        current: response.current,
        size: response.size
      }
    },
    apiParams: defaultFilter.value,
    immediate: true
  }
})

// 选中行处理
const handleSelectionChange = (selection: ModelConfigResponse[]) => {
  selectedRows.value = selection
}

// 表格列配置
const columns = computed(() => [
  { type: 'selection' as const, width: 50 },
  { 
    prop: 'name', 
    label: '模型名称', 
    minWidth: 120,
    showOverflowTooltip: true
  },
  {
    prop: 'platform',
    label: '平台类型',
    width: 120,
    formatter: (row: ModelConfigResponse) => {
      const config = MODEL_PLATFORM_CONFIG[row.platform]
      // 修复 ElTag 类型问题：default 类型不被支持，使用 info 替代
      const tagType = config.type === 'primary' ? 'info' : config.type
      return h(ElTag, { type: tagType }, () => config.text)
    }
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    formatter: (row: ModelConfigResponse) => {
      const config = MODEL_STATUS_CONFIG[row.status]
      return h(ElTag, { type: config.type }, () => config.text)
    }
  },
  {
    prop: 'health_status',
    label: '健康状态',
    width: 120,
    formatter: (row: ModelConfigResponse) => {
      const config = MODEL_HEALTH_STATUS_CONFIG[row.health_status]
      return h(ElTag, { type: config.type }, () => config.text)
    }
  },
  {
    prop: 'api_url',
    label: 'API地址',
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'timeout_seconds',
    label: '超时时间',
    width: 100,
    formatter: (row: ModelConfigResponse) => `${row.timeout_seconds}秒`
  },
  {
    prop: 'last_health_check',
    label: '最后检查',
    width: 200,
    formatter: (row: ModelConfigResponse) =>
      row.last_health_check || '未检查'
  },
  {
    prop: 'description',
    label: '描述',
    minWidth: 100,
    showOverflowTooltip: true
  },
  {
    prop: 'updated_at',
    label: '更新时间',
    width: 160,
    formatter: (row: ModelConfigResponse) =>
      new Date(row.updated_at).toLocaleString()
  },
  {
    prop: 'operation',
    label: '操作',
    width: 280,
    fixed: 'right' as const,
    formatter: (row: ModelConfigResponse) =>
      h('div', { style: 'display: flex; gap: 8px; align-items: center;' }, [
        h(ArtButtonTable, {
          type: 'test',
          onClick: () => healthCheckSingle(row.id)
        }),
        h(ArtButtonTable, {
          type: 'edit',
          onClick: () => showDialog('edit', row)
        }),
 
        h(ArtButtonTable, {
          type: 'view',
          onClick: () => toggleModelStatus(row)
        }),
        h(ArtButtonTable, {
          type: 'delete',
          onClick: () => deleteModel(row)
        })
      ])
  }
])

// 搜索处理
const handleSearch = (params: any) => {
  Object.assign(defaultFilter.value, params)
  refreshAll()
}

const resetSearch = () => {
  defaultFilter.value = {
    name: undefined,
    platform: undefined,
    status: undefined,
    health_status: undefined
  }
  refreshAll()
}

// 对话框处理
const showDialog = (type: Form.DialogType, modelConfig?: ModelConfigResponse) => {
  dialogType.value = type
  currentModelConfig.value = modelConfig || null
  dialogVisible.value = true
}

const showStatsDialog = () => {
  statsDialogVisible.value = true
}

const showCallDialog = () => {
  callDialogVisible.value = true
}

const handleDialogSubmit = () => {
  dialogVisible.value = false
  refreshAll()
}

// 模型操作
const toggleModelStatus = async (model: ModelConfigResponse) => {
  try {
    if (model.status === 'enabled') {
      await modelStore.disableModel(model.id)
    } else {
      await modelStore.enableModel(model.id)
    }
    refreshAll()
  } catch (error) {
    console.error('切换模型状态失败:', error)
  }
}

const deleteModel = async (model: ModelConfigResponse) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${model.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await modelStore.deleteModelConfig(model.id)
    refreshAll()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模型失败:', error)
    }
  }
}

const healthCheckSingle = async (modelId: number) => {
  try {
    await modelStore.batchHealthCheck([modelId])
    refreshAll()
  } catch (error) {
    console.error('健康检查失败:', error)
  }
}

const batchHealthCheck = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要检查的模型')
    return
  }
  
  try {
    const modelIds = selectedRows.value.map(row => row.id)
    await modelStore.batchHealthCheck(modelIds)
    refreshAll()
  } catch (error) {
    console.error('批量健康检查失败:', error)
  }
}

onMounted(() => {
  refreshAll()
})
</script>

<style scoped>



</style>
