"""
混沌测试监控API路由
"""
from typing import Dict, Any
from fastapi import APIRouter, Depends, Path, Query, Body

from app.api.deps import get_current_user
from app.core.dependencies import get_chaos_monitor_service, get_environment_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_monitor_service import ChaosMonitorService
from app.services.env.env import EnvironmentService
from app.core.exceptions import raise_not_found, raise_validation_error
from app.schemas.base import APIResponse
from app.schemas.chaos.chaos_monitor import (
    ChaosMonitorStartRequest, ChaosMonitorStopRequest, ChaosMonitorStatusResponse,
    ChaosMonitorDataQueryRequest, ChaosMonitorDataResponse, ChaosMonitorConfig
)

router = APIRouter()


@router.post("/start", response_model=APIResponse[ChaosMonitorStatusResponse], summary="启动监控")
async def start_monitoring(
    request: ChaosMonitorStartRequest,
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    env_service: EnvironmentService = Depends(get_environment_service),
    current_user: User = Depends(get_current_user)
):
    """启动主机监控"""
    # 从数据库获取真实的环境信息
    try:
        environment = await env_service.get_environment(request.environment_id)
        if not environment:
            raise_not_found(f"环境ID {request.environment_id} 不存在")

        # 验证环境类型是否为SSH
        if environment.type != "ssh":
            raise_validation_error(f"环境类型必须为SSH，当前类型为: {environment.type}")

        # 验证环境连接信息是否完整
        if not environment.host:
            raise_validation_error(f"环境 '{environment.name}' 的主机地址不能为空，请先配置环境的主机信息")

        # 构建主机连接信息
        host_info = {
            "id": environment.id,
            "host": environment.host,
            "port": environment.port or 22,
            "username": environment.config.get("username", "root") if environment.config else "root",
            "password": environment.config.get("password") if environment.config else None,
            "private_key_path": environment.config.get("private_key_path") if environment.config else None
        }



        # 验证认证信息
        if not host_info["password"] and not host_info["private_key_path"]:
            raise_validation_error("环境必须配置密码或私钥路径")

    except Exception as e:
        if hasattr(e, 'message'):
            raise e
        raise_validation_error(f"获取环境信息失败: {str(e)}")

    status = await service.start_monitoring(
        request.environment_id,
        request.host_id,
        host_info,
        request.config
    )

    return response_builder.success(data=status, message="监控启动成功")


@router.post("/stop", response_model=APIResponse[bool], summary="停止监控")
async def stop_monitoring(
    request: ChaosMonitorStopRequest,
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    current_user: User = Depends(get_current_user)
):
    """停止主机监控"""
    result = await service.stop_monitoring(request.environment_id, request.host_id)
    return response_builder.success(data=result, message="监控停止成功")


@router.get("/{environment_id}/status", response_model=APIResponse[ChaosMonitorStatusResponse], summary="获取监控状态")
async def get_monitoring_status(
    environment_id: int = Path(..., description="环境ID"),
    host_id: int = Query(..., description="主机ID"),
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    current_user: User = Depends(get_current_user)
):
    """获取监控状态"""
    status = await service.get_monitoring_status(environment_id, host_id)
    return response_builder.success(data=status, message="获取监控状态成功")


@router.get("/{environment_id}/data", response_model=APIResponse[ChaosMonitorDataResponse], summary="获取监控数据")
async def get_monitor_data(
    environment_id: int = Path(..., description="环境ID"),
    host_id: int = Query(..., description="主机ID"),
    hours: int = Query(1, ge=1, le=24, description="查询最近几小时的数据"),
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    current_user: User = Depends(get_current_user)
):
    """获取监控数据"""
    data = await service.get_monitor_data(environment_id, host_id, hours)
    return response_builder.success(data=data, message="获取监控数据成功")


@router.post("/cleanup", response_model=APIResponse[int], summary="清理过期数据")
async def cleanup_old_data(
    request: dict = Body(..., description="清理请求参数"),
    service: ChaosMonitorService = Depends(get_chaos_monitor_service),
    current_user: User = Depends(get_current_user)
):
    """清理过期监控数据"""
    hours = request.get("hours", 24)
    deleted_count = await service.cleanup_old_data(hours)
    return response_builder.success(data=deleted_count, message=f"清理了 {deleted_count} 条过期数据")
