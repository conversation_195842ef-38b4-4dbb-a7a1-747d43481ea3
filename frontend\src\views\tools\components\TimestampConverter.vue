<template>
  <div class="timestamp-converter">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6ea;</i>
        <h2>时间戳转换工具</h2>
      </div>
      <p class="tool-description">
        支持10位/13位时间戳与日期格式互转，支持多时区转换
      </p>
    </div>

    <div class="tool-content">
      <!-- 输入区域 -->
      <div class="input-section">
        <div class="section-header">
          <h3 class="section-title">输入</h3>
          <div class="section-actions">
            <ElButton size="small" @click="clearInput">
              <i class="iconfont-sys">&#xe622;</i>
              清空
            </ElButton>
            <ElButton size="small" @click="pasteInput">
              <i class="iconfont-sys">&#xe623;</i>
              粘贴
            </ElButton>
          </div>
        </div>
        
        <ElInput
          v-model="inputValue"
          type="textarea"
          :rows="3"
          placeholder="请输入时间戳（10位/13位）或日期（如 2023-10-01 12:00:00）"
          class="input-textarea"
          @input="handleInputChange"
        />
      </div>

      <!-- 参数配置区域 -->
      <div class="config-section">
        <div class="config-row">
          <div class="config-item">
            <label class="config-label">转换类型</label>
            <ElSelect v-model="convertType" @change="handleConvert">
              <ElOption label="时间戳 → 日期" value="timestamp-to-date" />
              <ElOption label="日期 → 时间戳" value="date-to-timestamp" />
              <ElOption label="自动识别" value="auto" />
            </ElSelect>
          </div>
          
          <div class="config-item">
            <label class="config-label">时区选择</label>
            <ElSelect v-model="timezone" @change="handleConvert">
              <ElOption label="北京时间 (GMT+8)" value="Asia/Shanghai" />
              <ElOption label="UTC时间 (GMT+0)" value="UTC" />
              <ElOption label="纽约时间 (GMT-5)" value="America/New_York" />
              <ElOption label="伦敦时间 (GMT+0)" value="Europe/London" />
              <ElOption label="东京时间 (GMT+9)" value="Asia/Tokyo" />
            </ElSelect>
          </div>
        </div>

        <div class="convert-button-wrapper">
          <ElButton
            type="primary"
            size="large"
            @click="handleConvert"
            :loading="converting"
            class="convert-button"
          >
            <i class="iconfont-sys">&#xe626;</i>
            转换
          </ElButton>
        </div>
      </div>

      <!-- 结果展示区域 -->
      <div v-if="result" class="result-section">
        <div class="section-header">
          <h3 class="section-title">转换结果</h3>
          <div class="section-actions">
            <ElButton size="small" @click="copyResult">
              <i class="iconfont-sys">&#xe627;</i>
              复制结果
            </ElButton>
          </div>
        </div>
        
        <div class="result-content">
          <div class="result-item" v-for="item in result" :key="item.label">
            <label class="result-label">{{ item.label }}</label>
            <div class="result-value">{{ item.value }}</div>
          </div>
        </div>
      </div>

      <!-- 当前时间显示 -->
      <div class="current-time-section">
        <div class="section-header">
          <h3 class="section-title">当前时间</h3>
          <div class="section-actions">
            <ElButton size="small" @click="useCurrentTime">
              <i class="iconfont-sys">&#xe628;</i>
              使用当前时间
            </ElButton>
          </div>
        </div>
        
        <div class="current-time-content">
          <div class="time-item">
            <label class="time-label">当前时间戳（10位）</label>
            <div class="time-value">{{ currentTimestamp10 }}</div>
          </div>
          <div class="time-item">
            <label class="time-label">当前时间戳（13位）</label>
            <div class="time-value">{{ currentTimestamp13 }}</div>
          </div>
          <div class="time-item">
            <label class="time-label">当前日期时间</label>
            <div class="time-value">{{ currentDateTime }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'TimestampConverter' })

// 响应式数据
const inputValue = ref('')
const convertType = ref('auto')
const timezone = ref('Asia/Shanghai')
const converting = ref(false)
const result = ref<Array<{ label: string; value: string }>>([])
const currentTime = ref(new Date())

// 定时器
let timer: NodeJS.Timeout | null = null

// 计算属性
const currentTimestamp10 = computed(() => Math.floor(currentTime.value.getTime() / 1000))
const currentTimestamp13 = computed(() => currentTime.value.getTime())
const currentDateTime = computed(() => {
  return currentTime.value.toLocaleString('zh-CN', {
    timeZone: timezone.value,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

// 方法
const handleInputChange = () => {
  if (convertType.value === 'auto') {
    handleConvert()
  }
}

const handleConvert = async () => {
  if (!inputValue.value.trim()) {
    result.value = []
    return
  }

  converting.value = true
  
  try {
    const input = inputValue.value.trim()
    const results: Array<{ label: string; value: string }> = []
    
    // 自动识别输入类型
    if (convertType.value === 'auto') {
      if (/^\d{10}$/.test(input)) {
        // 10位时间戳
        const timestamp = parseInt(input) * 1000
        const date = new Date(timestamp)
        results.push(
          { label: '输入类型', value: '10位时间戳' },
          { label: '北京时间', value: formatDate(date, 'Asia/Shanghai') },
          { label: 'UTC时间', value: formatDate(date, 'UTC') },
          { label: '13位时间戳', value: timestamp.toString() }
        )
      } else if (/^\d{13}$/.test(input)) {
        // 13位时间戳
        const timestamp = parseInt(input)
        const date = new Date(timestamp)
        results.push(
          { label: '输入类型', value: '13位时间戳' },
          { label: '北京时间', value: formatDate(date, 'Asia/Shanghai') },
          { label: 'UTC时间', value: formatDate(date, 'UTC') },
          { label: '10位时间戳', value: Math.floor(timestamp / 1000).toString() }
        )
      } else {
        // 尝试解析为日期
        const date = new Date(input)
        if (!isNaN(date.getTime())) {
          const timestamp = date.getTime()
          results.push(
            { label: '输入类型', value: '日期时间' },
            { label: '10位时间戳', value: Math.floor(timestamp / 1000).toString() },
            { label: '13位时间戳', value: timestamp.toString() },
            { label: '标准格式', value: formatDate(date, timezone.value) }
          )
        } else {
          throw new Error('无法识别输入格式')
        }
      }
    } else if (convertType.value === 'timestamp-to-date') {
      // 时间戳转日期
      let timestamp: number
      if (/^\d{10}$/.test(input)) {
        timestamp = parseInt(input) * 1000
      } else if (/^\d{13}$/.test(input)) {
        timestamp = parseInt(input)
      } else {
        throw new Error('请输入有效的时间戳')
      }
      
      const date = new Date(timestamp)
      results.push(
        { label: '北京时间', value: formatDate(date, 'Asia/Shanghai') },
        { label: 'UTC时间', value: formatDate(date, 'UTC') },
        { label: '指定时区时间', value: formatDate(date, timezone.value) }
      )
    } else if (convertType.value === 'date-to-timestamp') {
      // 日期转时间戳
      const date = new Date(input)
      if (isNaN(date.getTime())) {
        throw new Error('请输入有效的日期格式')
      }
      
      const timestamp = date.getTime()
      results.push(
        { label: '10位时间戳', value: Math.floor(timestamp / 1000).toString() },
        { label: '13位时间戳', value: timestamp.toString() },
        { label: '标准格式', value: formatDate(date, timezone.value) }
      )
    }
    
    result.value = results
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '转换失败')
    result.value = []
  } finally {
    converting.value = false
  }
}

const formatDate = (date: Date, timeZone: string): string => {
  return date.toLocaleString('zh-CN', {
    timeZone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const clearInput = () => {
  inputValue.value = ''
  result.value = []
}

const pasteInput = async () => {
  try {
    const text = await navigator.clipboard.readText()
    inputValue.value = text
    if (convertType.value === 'auto') {
      handleConvert()
    }
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const copyResult = async () => {
  if (!result.value.length) return
  
  const text = result.value.map(item => `${item.label}: ${item.value}`).join('\n')
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const useCurrentTime = () => {
  inputValue.value = currentTimestamp13.value.toString()
  if (convertType.value === 'auto') {
    handleConvert()
  }
}

const updateCurrentTime = () => {
  currentTime.value = new Date()
}

// 生命周期
onMounted(() => {
  // 每秒更新当前时间
  timer = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style lang="scss" scoped>
.timestamp-converter {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .input-section,
    .config-section,
    .result-section,
    .current-time-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }

        .section-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .input-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 14px;
      }
    }

    .config-section {
      .config-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;

        .config-item {
          .config-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 8px;
          }
        }
      }

      .convert-button-wrapper {
        text-align: center;

        .convert-button {
          min-width: 120px;
          height: 40px;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    .result-section {
      .result-content {
        .result-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid var(--el-border-color-lighter);

          &:last-child {
            border-bottom: none;
          }

          .result-label {
            width: 120px;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-regular);
          }

          .result-value {
            flex: 1;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            color: var(--el-text-color-primary);
            background: var(--el-fill-color-light);
            padding: 8px 12px;
            border-radius: 4px;
          }
        }
      }
    }

    .current-time-section {
      .current-time-content {
        .time-item {
          display: flex;
          align-items: center;
          padding: 8px 0;

          .time-label {
            width: 140px;
            font-size: 14px;
            color: var(--el-text-color-regular);
          }

          .time-value {
            flex: 1;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            color: var(--el-color-primary);
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .timestamp-converter {
    .tool-content {
      .config-section {
        .config-row {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }

      .result-section {
        .result-content {
          .result-item {
            flex-direction: column;
            align-items: flex-start;

            .result-label {
              width: auto;
              margin-bottom: 4px;
            }

            .result-value {
              width: 100%;
            }
          }
        }
      }

      .current-time-section {
        .current-time-content {
          .time-item {
            flex-direction: column;
            align-items: flex-start;

            .time-label {
              width: auto;
              margin-bottom: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
