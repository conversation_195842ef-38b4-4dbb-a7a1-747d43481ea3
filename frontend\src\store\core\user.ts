/**
 * 用户核心状态管理
 * 迁移自 src/store/modules/user.ts
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useLoading } from '@/store/shared/loading'
import { globalCache } from '@/store/shared/cache'

// 这里需要根据实际的用户API和类型定义进行调整
// import userApi from '@/api/userApi'
// import type { UserInfo, UserLoginRequest } from '@/types/api/user'

export const useUserStore = defineStore('user', () => {
  // ==================== 状态定义 ====================
  
  const userInfo = ref<any>(null)
  const token = ref<string>('')
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])
  
  // 加载状态
  const loading = useLoading()

  // ==================== 计算属性 ====================
  
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const isAdmin = computed(() => roles.value.includes('admin'))
  const isSuperUser = computed(() => roles.value.includes('superuser'))
  
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })
  
  const hasRole = computed(() => (role: string) => {
    return roles.value.includes(role)
  })

  // ==================== 用户管理方法 ====================
  
  /**
   * 用户登录
   */
  const login = async (loginData: any) => {
    return await loading.withLoading(async () => {
      // const response = await userApi.login(loginData)
      // token.value = response.token
      // userInfo.value = response.user
      // permissions.value = response.permissions || []
      // roles.value = response.roles || []
      
      // 缓存用户信息
      // globalCache.set('user_info', response.user, 30 * 60 * 1000) // 30分钟缓存
      
      // 存储到localStorage
      // localStorage.setItem('token', response.token)
      // localStorage.setItem('user_info', JSON.stringify(response.user))
      
      // return response
      
      // 临时实现，需要根据实际API调整
      console.log('Login method needs to be implemented with actual API')
      return null
    })
  }

  /**
   * 用户登出
   */
  const logout = async () => {
    return await loading.withLoading(async () => {
      try {
        // await userApi.logout()
      } catch (error) {
        console.error('Logout API error:', error)
      } finally {
        // 清除状态
        token.value = ''
        userInfo.value = null
        permissions.value = []
        roles.value = []
        
        // 清除缓存
        globalCache.clear()
        
        // 清除localStorage
        localStorage.removeItem('token')
        localStorage.removeItem('user_info')
      }
    })
  }

  /**
   * 获取用户信息
   */
  const fetchUserInfo = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('user_info')
    if (cached) {
      userInfo.value = cached
      return cached
    }

    return await loading.withLoading(async () => {
      // const response = await userApi.getUserInfo()
      // userInfo.value = response.user
      // permissions.value = response.permissions || []
      // roles.value = response.roles || []
      
      // 缓存用户信息
      // globalCache.set('user_info', response.user, 30 * 60 * 1000)
      
      // return response
      
      // 临时实现
      console.log('FetchUserInfo method needs to be implemented with actual API')
      return null
    })
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = async (updateData: any) => {
    return await loading.withLoading(async () => {
      // const response = await userApi.updateUserInfo(updateData)
      // userInfo.value = { ...userInfo.value, ...response }
      
      // 更新缓存
      // globalCache.set('user_info', userInfo.value, 30 * 60 * 1000)
      
      // 更新localStorage
      // localStorage.setItem('user_info', JSON.stringify(userInfo.value))
      
      // return response
      
      // 临时实现
      console.log('UpdateUserInfo method needs to be implemented with actual API')
      return null
    })
  }

  /**
   * 修改密码
   */
  const changePassword = async (passwordData: any) => {
    return await loading.withLoading(async () => {
      // const response = await userApi.changePassword(passwordData)
      // return response
      
      // 临时实现
      console.log('ChangePassword method needs to be implemented with actual API')
      return null
    })
  }

  /**
   * 刷新Token
   */
  const refreshToken = async () => {
    return await loading.withLoading(async () => {
      // const response = await userApi.refreshToken()
      // token.value = response.token
      
      // 更新localStorage
      // localStorage.setItem('token', response.token)
      
      // return response
      
      // 临时实现
      console.log('RefreshToken method needs to be implemented with actual API')
      return null
    })
  }

  // ==================== 初始化方法 ====================
  
  /**
   * 从localStorage初始化状态
   */
  const initializeFromStorage = () => {
    const storedToken = localStorage.getItem('token')
    const storedUserInfo = localStorage.getItem('user_info')
    
    if (storedToken) {
      token.value = storedToken
    }
    
    if (storedUserInfo) {
      try {
        userInfo.value = JSON.parse(storedUserInfo)
      } catch (error) {
        console.error('Failed to parse stored user info:', error)
        localStorage.removeItem('user_info')
      }
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    userInfo.value = null
    token.value = ''
    permissions.value = []
    roles.value = []
    loading.reset()
  }

  // 初始化
  initializeFromStorage()

  return {
    // 状态
    userInfo: readonly(userInfo),
    token: readonly(token),
    permissions: readonly(permissions),
    roles: readonly(roles),
    loading,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    isSuperUser,
    hasPermission,
    hasRole,
    
    // 方法
    login,
    logout,
    fetchUserInfo,
    updateUserInfo,
    changePassword,
    refreshToken,
    initializeFromStorage,
    resetState
  }
})
