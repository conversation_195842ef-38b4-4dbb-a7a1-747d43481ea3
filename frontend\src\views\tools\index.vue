<template>
  <div class="tools-platform">
    <!-- 左侧工具分类导航 -->
    <div class="tools-sidebar">
      <div class="sidebar-header">
        <h3 class="sidebar-title">工具分类</h3>
      </div>
      <div class="sidebar-content">
        <div
          v-for="category in toolCategories"
          :key="category.id"
          class="category-group"
        >
          <div
            class="category-header"
            :class="{ active: activeCategory === category.id }"
            @click="toggleCategory(category.id)"
          >
            <i class="category-icon iconfont-sys" v-html="category.icon"></i>
            <span class="category-name">{{ category.name }}</span>
            <i
              class="expand-icon iconfont-sys"
              :class="{ expanded: expandedCategories.includes(category.id) }"
              v-html="expandedCategories.includes(category.id) ? '&#xe625;' : '&#xe624;'"
            ></i>
          </div>
          <div
            v-show="expandedCategories.includes(category.id)"
            class="category-tools"
          >
            <div
              v-for="tool in category.tools"
              :key="tool.id"
              class="tool-item"
              :class="{ active: activeTool === tool.id }"
              @click="selectTool(tool)"
            >
              <i class="tool-icon iconfont-sys" v-html="tool.icon"></i>
              <span class="tool-name">{{ tool.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧工具操作区 -->
    <div class="tools-main">
      <div class="main-header">
        <div class="breadcrumb">
          <span class="breadcrumb-item">通用工具</span>
          <i class="breadcrumb-separator iconfont-sys">&#xe624;</i>
          <span class="breadcrumb-item">{{ currentCategoryName }}</span>
          <i v-if="currentToolName" class="breadcrumb-separator iconfont-sys">&#xe624;</i>
          <span v-if="currentToolName" class="breadcrumb-item active">{{ currentToolName }}</span>
        </div>
      </div>
      
      <div class="main-content">
        <!-- 未选择工具时的默认页面 -->
        <div v-if="!activeTool" class="welcome-section">
          <div class="welcome-header">
            <h2 class="welcome-title">欢迎使用通用工具平台</h2>
            <p class="welcome-desc">高频刚需、即开即用、覆盖全链路的开发测试工具集合</p>
          </div>
          
          <div class="popular-tools">
            <h3 class="section-title">热门工具</h3>
            <div class="tools-grid">
              <div
                v-for="tool in popularTools"
                :key="tool.id"
                class="tool-card"
                @click="selectTool(tool)"
              >
                <div class="tool-card-icon">
                  <i class="iconfont-sys" v-html="tool.icon"></i>
                </div>
                <div class="tool-card-content">
                  <h4 class="tool-card-title">{{ tool.name }}</h4>
                  <p class="tool-card-desc">{{ tool.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 工具详情页面 -->
        <div v-else class="tool-detail">
          <component
            :is="currentToolComponent"
            :tool-config="currentTool"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import TimestampConverter from './components/TimestampConverter.vue'
import JsonTools from './components/JsonTools.vue'
import CodeFormatter from './components/CodeFormatter.vue'
import TextDiff from './components/TextDiff.vue'
import RegexTester from './components/RegexTester.vue'
import FormatConverter from './components/FormatConverter.vue'
import SizeConverter from './components/SizeConverter.vue'
import UrlEncoder from './components/UrlEncoder.vue'
import Base64Encoder from './components/Base64Encoder.vue'
import HashGenerator from './components/HashGenerator.vue'
import UuidGenerator from './components/UuidGenerator.vue'
import StringProcessor from './components/StringProcessor.vue'

defineOptions({ name: 'ToolsPlatform' })

// 组件映射
const componentMap = {
  TimestampConverter,
  JsonTools,
  CodeFormatter,
  TextDiff,
  RegexTester,
  FormatConverter,
  SizeConverter,
  UrlEncoder,
  Base64Encoder,
  HashGenerator,
  UuidGenerator,
  StringProcessor
}

// 工具配置接口
interface ToolConfig {
  id: string
  name: string
  description: string
  icon: string
  category: string
  component: string
  popular: boolean
}

interface ToolCategory {
  id: string
  name: string
  icon: string
  tools: ToolConfig[]
}

// 响应式数据
const activeCategory = ref<string>('')
const activeTool = ref<string>('')
const expandedCategories = ref<string[]>(['datetime'])

// 工具分类配置
const toolCategories = ref<ToolCategory[]>([
  {
    id: 'datetime',
    name: '日期时间',
    icon: '&#xe6e9;',
    tools: [
      {
        id: 'timestamp',
        name: '时间戳转换',
        description: '时间戳与日期格式互转，支持多时区',
        icon: '&#xe6ea;',
        category: 'datetime',
        component: 'TimestampConverter',
        popular: true
      }
    ]
  },
  {
    id: 'text',
    name: '文本处理',
    icon: '&#xe6ec;',
    tools: [
      {
        id: 'formatter',
        name: '代码格式化',
        description: '多语言代码格式化美化',
        icon: '&#xe6ed;',
        category: 'text',
        component: 'CodeFormatter',
        popular: true
      },
      {
        id: 'diff',
        name: '文本对比',
        description: '文本差异对比分析',
        icon: '&#xe6ee;',
        category: 'text',
        component: 'TextDiff',
        popular: true
      }
    ]
  },
  {
    id: 'data',
    name: '数据转换',
    icon: '&#xe6ef;',
    tools: [
      {
        id: 'json-tools',
        name: 'JSON工具',
        description: 'JSON格式化、校验、路径提取',
        icon: '&#xe6f0;',
        category: 'data',
        component: 'JsonTools',
        popular: true
      },
      {
        id: 'converter',
        name: '格式转换',
        description: 'JSON、XML、YAML、CSV互转',
        icon: '&#xe633;',
        category: 'data',
        component: 'FormatConverter',
        popular: true
      },
      {
        id: 'regex',
        name: '正则测试',
        description: '正则表达式测试和验证',
        icon: '&#xe631;',
        category: 'data',
        component: 'RegexTester',
        popular: false
      },
      {
        id: 'size-converter',
        name: '大小转换',
        description: '数据大小单位转换计算',
        icon: '&#xe6f2;',
        category: 'data',
        component: 'SizeConverter',
        popular: false
      }
    ]
  },
  {
    id: 'encode',
    name: '编码工具',
    icon: '&#xe6f5;',
    tools: [
      {
        id: 'url-encoder',
        name: 'URL编码',
        description: 'URL编码解码、组件分解、参数解析',
        icon: '&#xe6f3;',
        category: 'encode',
        component: 'UrlEncoder',
        popular: true
      },
      {
        id: 'base64-encoder',
        name: 'Base64编码',
        description: '文本和文件的Base64编码解码',
        icon: '&#xe6f4;',
        category: 'encode',
        component: 'Base64Encoder',
        popular: true
      },
      {
        id: 'hash-generator',
        name: 'MD5/SHA哈希',
        description: '生成MD5、SHA1、SHA256等哈希值',
        icon: '&#xe6f6;',
        category: 'encode',
        component: 'HashGenerator',
        popular: false
      },
      {
        id: 'uuid-generator',
        name: 'UUID生成器',
        description: '生成各种版本的UUID/GUID',
        icon: '&#xe6f7;',
        category: 'encode',
        component: 'UuidGenerator',
        popular: false
      },
      {
        id: 'string-processor',
        name: '字符串处理',
        description: '大小写转换、去空格、字符统计等',
        icon: '&#xe6f8;',
        category: 'encode',
        component: 'StringProcessor',
        popular: false
      }
    ]
  }
])

// 计算属性
const popularTools = computed(() => {
  const allTools: ToolConfig[] = []
  toolCategories.value.forEach(category => {
    allTools.push(...category.tools.filter(tool => tool.popular))
  })
  return allTools
})

const currentCategoryName = computed(() => {
  if (!activeCategory.value) return '常用工具'
  const category = toolCategories.value.find(cat => cat.id === activeCategory.value)
  return category?.name || '常用工具'
})

const currentToolName = computed(() => {
  if (!activeTool.value) return ''
  const allTools = toolCategories.value.flatMap(cat => cat.tools)
  const tool = allTools.find(t => t.id === activeTool.value)
  return tool?.name || ''
})

const currentTool = computed(() => {
  if (!activeTool.value) return null
  const allTools = toolCategories.value.flatMap(cat => cat.tools)
  return allTools.find(t => t.id === activeTool.value) || null
})

const currentToolComponent = computed(() => {
  if (!currentTool.value?.component) return null
  return componentMap[currentTool.value.component as keyof typeof componentMap] || null
})

// 方法
const toggleCategory = (categoryId: string) => {
  activeCategory.value = categoryId
  
  if (expandedCategories.value.includes(categoryId)) {
    expandedCategories.value = expandedCategories.value.filter(id => id !== categoryId)
  } else {
    expandedCategories.value.push(categoryId)
  }
}

const selectTool = (tool: ToolConfig) => {
  activeTool.value = tool.id
  activeCategory.value = tool.category

  // 确保对应分类是展开的
  if (!expandedCategories.value.includes(tool.category)) {
    expandedCategories.value.push(tool.category)
  }
}

// 生命周期
onMounted(() => {
  // 默认展开第一个分类
  if (toolCategories.value.length > 0) {
    activeCategory.value = toolCategories.value[0].id
  }
})
</script>

<style lang="scss" scoped>
@use '@styles/variables.scss' as *;

.tools-platform {
  display: flex;
  height: 100%;
  background: var(--el-bg-color-page);
}

// 左侧工具分类导航 (20%)
.tools-sidebar {
  width: 15%;
  min-width: 240px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 20px 16px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .sidebar-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
  }
}

.category-group {
  margin-bottom: 4px;

  .category-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      background: var(--el-fill-color-light);
    }

    &.active {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: var(--el-color-primary);
      }
    }

    .category-icon {
      font-size: 18px;
      margin-right: 8px;
    }

    .category-name {
      flex: 1;
      font-size: 14px;
      font-weight: 500;
    }

    .expand-icon {
      font-size: 12px;
      transition: transform 0.3s ease;

      &.expanded {
        transform: rotate(90deg);
      }
    }
  }

  .category-tools {
    .tool-item {
      display: flex;
      align-items: center;
      padding: 8px 16px 8px 40px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 13px;

      &:hover {
        background: var(--el-fill-color-light);
      }

      &.active {
        background: var(--el-color-primary-light-8);
        color: var(--el-color-primary);
      }

      .tool-icon {
        font-size: 14px;
        margin-right: 8px;
      }

      .tool-name {
        font-weight: 400;
      }
    }
  }
}

// 右侧工具操作区 (80%)
.tools-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .main-header {
    padding: 16px 24px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-lighter);

    .breadcrumb {
      display: flex;
      align-items: center;
      font-size: 14px;

      .breadcrumb-item {
        color: var(--el-text-color-regular);

        &.active {
          color: var(--el-color-primary);
          font-weight: 500;
        }
      }

      .breadcrumb-separator {
        margin: 0 8px;
        font-size: 12px;
        color: var(--el-text-color-placeholder);
      }
    }
  }

  .main-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }
}

// 欢迎页面
.welcome-section {
  .welcome-header {
    text-align: center;
    margin-bottom: 48px;

    .welcome-title {
      font-size: 28px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0 0 12px 0;
    }

    .welcome-desc {
      font-size: 16px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .popular-tools {
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0 0 24px 0;
    }

    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }
  }
}

.tool-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .tool-card-icon {
    width: 56px;
    height: 56px;
    background: var(--el-color-primary-light-9);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    i {
      font-size: 28px;
      color: var(--el-color-primary);
    }
  }

  .tool-card-content {
    flex: 1;
    min-width: 0;

    .tool-card-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0 0 6px 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .tool-card-desc {
      font-size: 13px;
      color: var(--el-text-color-regular);
      margin: 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}

// 工具详情页面
.tool-detail {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 响应式设计
@media only screen and (max-width: $device-ipad-pro) {
  .tools-platform {
    flex-direction: column;
  }

  .tools-sidebar {
    width: 100%;
    min-width: auto;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .tools-main {
    .main-content {
      padding: 16px;
    }
  }

  .welcome-section {
    .tools-grid {
      grid-template-columns: 1fr;
    }

    .tool-card {
      padding: 16px;
      gap: 12px;

      .tool-card-icon {
        width: 48px;
        height: 48px;

        i {
          font-size: 24px;
        }
      }

      .tool-card-content {
        .tool-card-title {
          font-size: 15px;
        }

        .tool-card-desc {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
