"""
角色数据模型
"""
from sqlalchemy import Column, String, Text, Boolean, JSON
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Role(BaseModel):
    """
    角色模型
    """
    __tablename__ = "role"

    # 基础信息
    name = Column(String(50), unique=True, index=True, nullable=False, comment="角色名称")
    code = Column(String(50), unique=True, index=True, nullable=False, comment="角色代码")
    description = Column(Text, nullable=True, comment="角色描述")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 权限信息
    permissions = Column(JSON, default=list, comment="权限列表")
    menu_permissions = Column(JSON, default=list, comment="菜单权限")
    button_permissions = Column(JSON, default=list, comment="按钮权限")
    
    # 关联关系
    users = relationship("User", secondary="user_roles", back_populates="roles")

    def __repr__(self) -> str:
        return f"<Role(id={self.id}, name={self.name})>"

    def add_permission(self, permission: str) -> None:
        """添加权限"""
        if self.permissions is None:
            self.permissions = []
        if permission not in self.permissions:
            self.permissions.append(permission)

    def remove_permission(self, permission: str) -> None:
        """移除权限"""
        if self.permissions and permission in self.permissions:
            self.permissions.remove(permission)

    def has_permission(self, permission: str) -> bool:
        """检查是否具有指定权限"""
        return self.permissions and permission in self.permissions 