"""
API基类
提供标准化的API响应处理和通用功能
"""
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic
from abc import ABC, abstractmethod
from fastapi import APIRouter, Depends, Query, Path
from pydantic import BaseModel

from app.core.dependencies import DatabaseDep, PaginationDep, SearchDep
from app.core.responses import response_builder
from app.schemas.base import APIResponse, PaginationResponse
from app.schemas.common import PaginationParams, SearchParams
from app.services.base import BaseService

# 泛型类型定义
ServiceType = TypeVar("ServiceType", bound=BaseService)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ResponseSchemaType = TypeVar("ResponseSchemaType", bound=BaseModel)


class BaseAPIController(Generic[ServiceType, CreateSchemaType, UpdateSchemaType, ResponseSchemaType], ABC):
    """
    API控制器基类
    
    提供标准化的CRUD API端点和响应处理
    所有API控制器都应继承此类
    
    泛型参数:
        ServiceType: 服务类型
        CreateSchemaType: 创建请求Schema类型
        UpdateSchemaType: 更新请求Schema类型
        ResponseSchemaType: 响应Schema类型
    """
    
    def __init__(self, prefix: str, tags: List[str]):
        self.router = APIRouter(prefix=prefix, tags=tags)
        self._register_routes()
    
    @property
    @abstractmethod
    def service_dependency(self):
        """返回服务依赖注入函数"""
        pass
    
    @property
    @abstractmethod
    def resource_name(self) -> str:
        """返回资源名称（用于响应消息）"""
        pass
    
    @property
    @abstractmethod
    def create_schema_class(self) -> Type[CreateSchemaType]:
        """返回创建Schema类"""
        pass
    
    @property
    @abstractmethod
    def update_schema_class(self) -> Type[UpdateSchemaType]:
        """返回更新Schema类"""
        pass
    
    @property
    @abstractmethod
    def response_schema_class(self) -> Type[ResponseSchemaType]:
        """返回响应Schema类"""
        pass
    
    def _register_routes(self):
        """注册标准CRUD路由"""
        
        # 列表查询
        @self.router.get("/", response_model=PaginationResponse[self.response_schema_class])
        async def list_items(
            params: SearchDep,
            service: ServiceType = Depends(self.service_dependency)
        ):
            """获取列表"""
            result = await service.list_with_pagination(params)
            return response_builder.paginated(
                records=result.records,
                total=result.total,
                current=result.current,
                size=result.size,
                message=f"获取{self.resource_name}列表成功"
            )
        
        # 详情查询
        @self.router.get("/{item_id}", response_model=APIResponse[self.response_schema_class])
        async def get_item(
            item_id: int = Path(..., description=f"{self.resource_name}ID"),
            service: ServiceType = Depends(self.service_dependency)
        ):
            """获取详情"""
            result = await service.get_by_id(item_id)
            return response_builder.success(
                data=result,
                message=f"获取{self.resource_name}详情成功"
            )
        
        # 创建
        @self.router.post("/", response_model=APIResponse[self.response_schema_class])
        async def create_item(
            item_data: self.create_schema_class,
            service: ServiceType = Depends(self.service_dependency),
            current_user: str = "system"  # TODO: 从认证中获取
        ):
            """创建"""
            result = await service.create(item_data, current_user)
            return response_builder.success(
                data=result,
                message=f"创建{self.resource_name}成功"
            )
        
        # 更新
        @self.router.put("/{item_id}", response_model=APIResponse[self.response_schema_class])
        async def update_item(
            item_id: int = Path(..., description=f"{self.resource_name}ID"),
            item_data: self.update_schema_class,
            service: ServiceType = Depends(self.service_dependency),
            current_user: str = "system"  # TODO: 从认证中获取
        ):
            """更新"""
            result = await service.update(item_id, item_data, current_user)
            return response_builder.success(
                data=result,
                message=f"更新{self.resource_name}成功"
            )
        
        # 删除
        @self.router.delete("/{item_id}", response_model=APIResponse[bool])
        async def delete_item(
            item_id: int = Path(..., description=f"{self.resource_name}ID"),
            service: ServiceType = Depends(self.service_dependency)
        ):
            """删除"""
            await service.delete(item_id)
            return response_builder.success(
                data=True,
                message=f"删除{self.resource_name}成功"
            )
    
    def add_custom_route(self, method: str, path: str, **kwargs):
        """添加自定义路由"""
        def decorator(func):
            route_method = getattr(self.router, method.lower())
            return route_method(path, **kwargs)(func)
        return decorator


class APIResponseHelper:
    """API响应辅助类"""
    
    @staticmethod
    def success_response(
        data: Any = None,
        message: str = "操作成功",
        code: int = 200
    ) -> APIResponse[Any]:
        """构建成功响应"""
        return response_builder.success(data, message, code)
    
    @staticmethod
    def error_response(
        message: str = "操作失败",
        code: int = 400,
        data: Any = None
    ) -> APIResponse[Any]:
        """构建错误响应"""
        return response_builder.error(message, code, data)
    
    @staticmethod
    def paginated_response(
        records: List[Any],
        total: int,
        current: int,
        size: int,
        message: str = "查询成功"
    ) -> PaginationResponse[Any]:
        """构建分页响应"""
        return response_builder.paginated(records, total, current, size, message)
    
    @staticmethod
    def created_response(
        data: Any,
        message: str = "创建成功"
    ) -> APIResponse[Any]:
        """构建创建成功响应"""
        return response_builder.success(data, message, 201)
    
    @staticmethod
    def updated_response(
        data: Any,
        message: str = "更新成功"
    ) -> APIResponse[Any]:
        """构建更新成功响应"""
        return response_builder.success(data, message, 200)
    
    @staticmethod
    def deleted_response(
        message: str = "删除成功"
    ) -> APIResponse[bool]:
        """构建删除成功响应"""
        return response_builder.success(True, message, 200)


class APIValidationHelper:
    """API验证辅助类"""
    
    @staticmethod
    def validate_pagination_params(params: PaginationParams) -> PaginationParams:
        """验证分页参数"""
        if params.current < 1:
            params.current = 1
        if params.size < 1:
            params.size = 20
        if params.size > 100:
            params.size = 100
        return params
    
    @staticmethod
    def validate_search_params(params: SearchParams) -> SearchParams:
        """验证搜索参数"""
        params = APIValidationHelper.validate_pagination_params(params)
        if params.keyword:
            params.keyword = params.keyword.strip()
            if len(params.keyword) > 100:
                params.keyword = params.keyword[:100]
        return params
    
    @staticmethod
    def validate_id_param(item_id: int, resource_name: str = "资源") -> int:
        """验证ID参数"""
        if item_id <= 0:
            from app.core.exceptions import raise_validation_error
            raise_validation_error(f"无效的{resource_name}ID: {item_id}")
        return item_id


# 全局辅助实例
api_response = APIResponseHelper()
api_validation = APIValidationHelper()


# ==================== API装饰器 ====================

from functools import wraps
from typing import Callable
import asyncio
import time
from app.utils.logger import setup_logger

logger = setup_logger()


def api_endpoint(
    success_message: str = "操作成功",
    error_message: str = "操作失败",
    log_request: bool = True,
    log_response: bool = False
):
    """
    API端点装饰器

    提供统一的响应处理、日志记录和性能监控

    Args:
        success_message: 成功消息
        error_message: 错误消息
        log_request: 是否记录请求日志
        log_response: 是否记录响应日志
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()

            # 记录请求日志
            if log_request:
                logger.info(f"API请求: {func.__name__} - 参数: {kwargs}")

            try:
                # 执行API函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)

                # 计算执行时间
                duration = time.time() - start_time

                # 记录响应日志
                if log_response:
                    logger.info(f"API响应: {func.__name__} - 耗时: {duration:.3f}s")

                # 如果结果已经是APIResponse，直接返回
                if hasattr(result, 'success'):
                    return result

                # 否则包装为成功响应
                return api_response.success_response(
                    data=result,
                    message=success_message
                )

            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"API异常: {func.__name__} - 错误: {str(e)} - 耗时: {duration:.3f}s")

                # 重新抛出异常，让全局异常处理器处理
                raise

        return wrapper
    return decorator


def validate_request(validator_func: Callable = None):
    """
    请求验证装饰器

    Args:
        validator_func: 验证函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 执行验证
            if validator_func:
                if asyncio.iscoroutinefunction(validator_func):
                    await validator_func(*args, **kwargs)
                else:
                    validator_func(*args, **kwargs)

            # 执行原函数
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)

        return wrapper
    return decorator


def cache_response(ttl: int = 300, key_func: Callable = None):
    """
    响应缓存装饰器

    Args:
        ttl: 缓存时间（秒）
        key_func: 缓存键生成函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # TODO: 实现缓存逻辑
            # 这里可以集成Redis或其他缓存系统

            # 暂时直接执行函数
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)

        return wrapper
    return decorator
