"""
真正的加密/解密工具
使用 AES 对称加密安全地存储和解密 API 密钥
"""
import base64
import hashlib
from typing import Optional

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from app.core.config import settings
from app.utils.logger import setup_logger

logger = setup_logger()


class EncryptionManager:
    """加密管理器
    
    使用 AES 对称加密来安全地存储和解密敏感数据
    """
    
    def __init__(self):
        self._fernet = None
        self._salt = b'DpTestPlatform_Salt_2025'  # 固定盐值，确保一致性
    
    def _get_fernet(self) -> Fernet:
        """获取或创建 Fernet 加密器"""
        if self._fernet is None:
            # 基于应用密钥生成加密密钥
            password = settings.SECRET_KEY.encode()
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=self._salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            self._fernet = Fernet(key)
        
        return self._fernet
    
    def encrypt(self, plaintext: str) -> str:
        """加密字符串
        
        Args:
            plaintext: 明文字符串
            
        Returns:
            加密后的字符串（Base64编码）
            
        Raises:
            Exception: 加密失败时抛出异常
        """
        try:
            if not plaintext:
                return ""
            
            fernet = self._get_fernet()
            encrypted_bytes = fernet.encrypt(plaintext.encode('utf-8'))
            encrypted_str = base64.urlsafe_b64encode(encrypted_bytes).decode('utf-8')
            
            logger.debug(f"成功加密数据，长度: {len(plaintext)} -> {len(encrypted_str)}")
            return encrypted_str
            
        except Exception as e:
            logger.error(f"加密失败: {str(e)}")
            raise Exception(f"加密失败: {str(e)}")
    
    def decrypt(self, encrypted_text: str) -> str:
        """解密字符串
        
        Args:
            encrypted_text: 加密的字符串（Base64编码）
            
        Returns:
            解密后的明文字符串
            
        Raises:
            Exception: 解密失败时抛出异常
        """
        try:
            if not encrypted_text:
                return ""
            
            fernet = self._get_fernet()
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_text.encode('utf-8'))
            decrypted_bytes = fernet.decrypt(encrypted_bytes)
            decrypted_str = decrypted_bytes.decode('utf-8')
            
            logger.debug(f"成功解密数据，长度: {len(encrypted_text)} -> {len(decrypted_str)}")
            return decrypted_str
            
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            raise Exception(f"解密失败: {str(e)}")
    
    def is_encrypted(self, text: str) -> bool:
        """检查字符串是否是加密格式
        
        Args:
            text: 要检查的字符串
            
        Returns:
            True 如果是加密格式，False 否则
        """
        try:
            if not text:
                return False
            
            # 尝试解密来判断是否是加密格式
            self.decrypt(text)
            return True
            
        except Exception:
            return False
    
    def safe_decrypt(self, text: str) -> str:
        """安全解密
        
        如果解密失败，返回原文（可能是明文或旧格式）
        
        Args:
            text: 要解密的字符串
            
        Returns:
            解密后的字符串，如果解密失败则返回原文
        """
        try:
            return self.decrypt(text)
        except Exception:
            logger.warning(f"解密失败，返回原文（可能是明文或旧格式）")
            return text


# 全局加密管理器实例
encryption_manager = EncryptionManager()


def encrypt_api_key(api_key: str) -> str:
    """加密 API 密钥
    
    Args:
        api_key: 明文 API 密钥
        
    Returns:
        加密后的 API 密钥
    """
    return encryption_manager.encrypt(api_key)


def decrypt_api_key(encrypted_api_key: str) -> str:
    """解密 API 密钥
    
    Args:
        encrypted_api_key: 加密的 API 密钥
        
    Returns:
        解密后的明文 API 密钥
    """
    return encryption_manager.decrypt(encrypted_api_key)


def safe_decrypt_api_key(encrypted_api_key: str) -> str:
    """安全解密 API 密钥
    
    如果解密失败，返回原文（向后兼容）
    
    Args:
        encrypted_api_key: 加密的 API 密钥
        
    Returns:
        解密后的明文 API 密钥，如果解密失败则返回原文
    """
    return encryption_manager.safe_decrypt(encrypted_api_key)


def is_api_key_encrypted(api_key: str) -> bool:
    """检查 API 密钥是否是加密格式
    
    Args:
        api_key: API 密钥
        
    Returns:
        True 如果是加密格式，False 否则
    """
    return encryption_manager.is_encrypted(api_key)


def mask_api_key(api_key: str) -> str:
    """脱敏显示 API 密钥
    
    Args:
        api_key: API 密钥（明文或密文）
        
    Returns:
        脱敏后的 API 密钥
    """
    if not api_key or len(api_key) <= 8:
        return "****"
    return f"{api_key[:4]}****{api_key[-4:]}"


def verify_encryption_key() -> bool:
    """验证加密密钥是否正常工作
    
    Returns:
        True 如果加密系统正常，False 否则
    """
    try:
        test_data = "test_encryption_key_verification"
        encrypted = encryption_manager.encrypt(test_data)
        decrypted = encryption_manager.decrypt(encrypted)
        return decrypted == test_data
    except Exception as e:
        logger.error(f"加密系统验证失败: {str(e)}")
        return False


# 在模块加载时验证加密系统
if not verify_encryption_key():
    logger.error("⚠️ 加密系统初始化失败！API 密钥可能无法正常工作！")
else:
    logger.info("✅ 加密系统初始化成功")
