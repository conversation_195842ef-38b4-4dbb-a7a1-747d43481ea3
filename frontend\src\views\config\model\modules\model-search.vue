<!-- 模型搜索栏 -->
<template>
  <ArtSearchBar
    v-model:filter="searchFormState"
    :items="formItems"
    @reset="handleReset"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { SearchFormItem } from '@/types'
import { MODEL_PLATFORM_CONFIG, MODEL_STATUS_CONFIG, MODEL_HEALTH_STATUS_CONFIG } from '@/types/api/model'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'

interface Emits {
  (e: 'search', params: Record<string, any>): void
  (e: 'reset'): void
}

const props = defineProps<{
  filter: Record<string, any>
}>()

const emit = defineEmits<Emits>()

const searchFormState = ref({ ...props.filter })

// 搜索表单配置
const formItems: SearchFormItem[] = [
  {
    prop: 'name',
    type: 'input',
    label: '模型名称',
    placeholder: '请输入模型名称'
  },
  {
    prop: 'platform',
    type: 'select',
    label: '平台类型',
    placeholder: '请选择平台类型',
    options: Object.entries(MODEL_PLATFORM_CONFIG).map(([value, config]) => ({
      label: config.text,
      value
    }))
  },
  {
    prop: 'status',
    type: 'select',
    label: '状态',
    placeholder: '请选择状态',
    options: Object.entries(MODEL_STATUS_CONFIG).map(([value, config]) => ({
      label: config.text,
      value
    }))
  },
  {
    prop: 'health_status',
    type: 'select',
    label: '健康状态',
    placeholder: '请选择健康状态',
    options: Object.entries(MODEL_HEALTH_STATUS_CONFIG).map(([value, config]) => ({
      label: config.text,
      value
    }))
  }
]

// 搜索处理
const handleSearch = () => {
  emit('search', { ...searchFormState.value })
}

// 重置处理
const handleReset = () => {
  searchFormState.value = {
    name: undefined,
    platform: undefined,
    status: undefined,
    health_status: undefined
  }
  emit('reset')
}

// 监听外部filter变化
watch(() => props.filter, (newFilter) => {
  searchFormState.value = { ...newFilter }
}, { deep: true })
</script>
