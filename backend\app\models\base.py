"""
基础数据模型类
包含所有模型的通用字段和方法
"""
from datetime import datetime, timezone, timedelta
from typing import Any, Dict

from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.ext.declarative import declared_attr

from app.database.base import Base


def beijing_now():
    """获取北京时间"""
    beijing_tz = timezone(timedelta(hours=8))
    return datetime.now(beijing_tz)


class BaseModel(Base):
    """
    基础模型类，包含通用字段
    """
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    created_at = Column(DateTime, default=beijing_now, comment="创建时间")
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now, comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建者")
    updated_by = Column(String(50), nullable=True, comment="更新者")

    @declared_attr
    def __tablename__(cls) -> str:
        """
        自动生成表名（类名转换为下划线命名）
        """
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()

    def to_dict(self) -> Dict[str, Any]:
        """
        将模型实例转换为字典
        """
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }

    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """
        从字典更新模型实例
        """
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def __repr__(self) -> str:
        """
        模型的字符串表示
        """
        return f"<{self.__class__.__name__}(id={self.id})>" 