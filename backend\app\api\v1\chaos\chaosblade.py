"""
ChaosBlade管理API路由
"""
from typing import List
from datetime import datetime
from fastapi import APIRouter, Depends, Body, Query, Path

from app.api.deps import get_current_user, get_current_superuser
from app.core.dependencies import get_chaosblade_service, get_environment_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaosblade_service import ChaosBladeService
from app.services.env.env import EnvironmentService
from app.schemas.base import APIResponse
from app.schemas.chaos.chaos_task import (
    ChaosBladeInstallRequest, ChaosBladeStatusResponse
)
from app.schemas.env.env import EnvironmentResponse
from app.utils.logger import setup_logger

logger = setup_logger()

router = APIRouter()


@router.get("/hosts", response_model=APIResponse[List[EnvironmentResponse]], summary="获取Linux主机列表")
async def get_linux_hosts(
    env_service: EnvironmentService = Depends(get_environment_service),
    current_user: User = Depends(get_current_user)
):
    """获取可用于ChaosBlade部署的Linux主机列表"""
    try:
        # 构建搜索参数，只获取SSH类型的环境（Linux主机）
        from app.schemas.common import SearchParams
        search_params = SearchParams(
            current=1,
            size=100,  # 最大限制100台主机
            keyword=None
        )

        # 获取SSH类型的环境列表
        environments, total = await env_service.list_environments(
            search_params,
            env_type="ssh",  # 只获取SSH类型的环境
            status=None  # 不限制状态，显示所有主机
        )

        return response_builder.success(
            data=environments,
            message=f"获取Linux主机列表成功，共{len(environments)}台主机"
        )

    except Exception as e:
        logger.error(f"获取Linux主机列表失败: {str(e)}")
        return response_builder.error(
            message="获取Linux主机列表失败",
            data={"error": str(e)}
        )


@router.post("/install", response_model=APIResponse[dict], summary="安装ChaosBlade")
async def install_chaosblade(
    request: ChaosBladeInstallRequest,
    use_sftp: bool = Query(True, description="是否使用SFTP上传本地包"),
    service: ChaosBladeService = Depends(get_chaosblade_service),
    env_service: EnvironmentService = Depends(get_environment_service),
    current_user: User = Depends(get_current_superuser)
):
    """在指定主机上安装ChaosBlade"""
    results = []
    host_ids = request.host_ids or []

    if not host_ids:
        return response_builder.error(message="请选择要安装的主机")

    for host_id in host_ids:
        try:
            # 从环境管理模块获取真实的主机信息
            environment = await env_service.get_environment(host_id)
            if not environment:
                results.append({
                    "host_id": host_id,
                    "success": False,
                    "message": f"主机ID {host_id} 不存在",
                    "error": True
                })
                continue

            # 构建主机连接信息
            host_info = {
                "host_id": host_id,
                "host": environment.host,
                "port": environment.port or 22,
                "username": environment.config.get("username", "root") if environment.config else "root",
                "password": environment.config.get("password") if environment.config else None,
                "private_key_path": environment.config.get("private_key_path") if environment.config else None
            }

            # 选择安装方式
            if use_sftp:
                result = await service.install_chaosblade_via_sftp(host_info, request.force_reinstall)
            else:
                result = await service.install_chaosblade(host_info, request.force_reinstall)

            results.append({
                "host_id": host_id,
                "host_name": environment.name,
                "host_address": environment.host,
                "success": result["success"],
                "message": result["message"],
                "version": result.get("version"),
                "method": result.get("method", "wget"),
                "skipped": result.get("skipped", False)
            })

        except Exception as e:
            results.append({
                "host_id": host_id,
                "success": False,
                "message": str(e),
                "error": True
            })

    success_count = sum(1 for r in results if r["success"])
    total_count = len(results)

    return response_builder.success(
        data={
            "total_hosts": total_count,
            "success_count": success_count,
            "failed_count": total_count - success_count,
            "results": results,
            "deployment_method": "SFTP" if use_sftp else "wget"
        },
        message=f"ChaosBlade安装完成，成功: {success_count}/{total_count}"
    )


@router.get("/status", response_model=APIResponse[List[ChaosBladeStatusResponse]], summary="检查所有Linux主机的ChaosBlade状态")
async def check_all_chaosblade_status(
    quick_check: bool = Query(True, description="是否快速检查（不实际连接SSH）"),
    service: ChaosBladeService = Depends(get_chaosblade_service),
    env_service: EnvironmentService = Depends(get_environment_service),
    current_user: User = Depends(get_current_user)
):
    """检查所有Linux主机的ChaosBlade安装状态"""
    try:
        # 获取所有SSH类型的环境（Linux主机）
        from app.schemas.common import SearchParams
        search_params = SearchParams(current=1, size=100, keyword=None)
        environments, total = await env_service.list_environments(
            search_params, env_type="ssh", status=None
        )

        results = []
        for env in environments:
            try:
                # 构建主机连接信息
                host_info = {
                    "host_id": env.id,
                    "host": env.host,
                    "port": env.port or 22,
                    "username": env.config.get("username", "root") if env.config else "root",
                    "password": env.config.get("password") if env.config else None,
                    "private_key_path": env.config.get("private_key_path") if env.config else None
                }

                # 检查安装状态
                if quick_check:
                    # 快速检查模式：从环境配置中获取状态或返回默认状态
                    chaosblade_config = env.config.get("chaosblade", {}) if env.config else {}
                    results.append(ChaosBladeStatusResponse(
                        host_id=env.id,
                        host_name=env.name,
                        host_address=env.host,
                        installed=chaosblade_config.get("installed", False),
                        version=chaosblade_config.get("version"),
                        install_path=chaosblade_config.get("install_path"),
                        last_check_time=chaosblade_config.get("last_check_time"),
                        error_message=None
                    ))
                else:
                    # 完整检查模式：实际连接SSH检查
                    status = await service.check_installation(host_info)
                    results.append(ChaosBladeStatusResponse(
                        host_id=env.id,
                        host_name=env.name,
                        host_address=env.host,
                        installed=status["installed"],
                        version=status.get("version"),
                        install_path=status.get("install_path") if status["installed"] else None,
                        last_check_time=status.get("last_check_time"),
                        error_message=status.get("error_message")
                    ))
            except Exception as e:
                results.append(ChaosBladeStatusResponse(
                    host_id=env.id,
                    host_name=env.name,
                    host_address=env.host,
                    installed=False,
                    error_message=str(e)
                ))

        return response_builder.success(
            data=results,
            message=f"ChaosBlade状态检查完成，共检查{len(results)}台主机"
        )

    except Exception as e:
        logger.error(f"检查ChaosBlade状态失败: {str(e)}")
        return response_builder.error(
            message="检查ChaosBlade状态失败",
            data={"error": str(e)}
        )


@router.post("/test-connection/{env_id}", response_model=APIResponse[dict], summary="测试环境SSH连接")
async def test_chaosblade_connection(
    env_id: int = Path(..., description="环境ID"),
    service: ChaosBladeService = Depends(get_chaosblade_service),
    env_service: EnvironmentService = Depends(get_environment_service),
    current_user: User = Depends(get_current_user)
):
    """测试指定环境的SSH连接和ChaosBlade状态"""
    try:
        # 使用环境管理的连接测试功能
        from app.schemas.env.env import ConnectionTestRequest

        # 获取环境信息
        environment = await env_service.get_environment(env_id)
        if not environment:
            return response_builder.error(message=f"环境ID {env_id} 不存在")

        # 构建连接测试请求
        test_request = ConnectionTestRequest(
            type=environment.type,
            config={
                'host': environment.host,
                'port': environment.port,
                **environment.config
            },
            timeout=10
        )

        # 调用环境管理的连接测试
        connection_result = await env_service.test_connection(test_request)

        if connection_result.success:
            # 如果SSH连接成功，再检查ChaosBlade安装状态
            host_info = {
                "host": environment.host,
                "port": environment.port or 22,
                "username": environment.config.get("username", "root") if environment.config else "root",
                "password": environment.config.get("password") if environment.config else None,
                "private_key_path": environment.config.get("private_key_path") if environment.config else None
            }

            status = await service.check_installation(host_info)
            test_result = {
                "connected": True,
                "ssh_status": "连接成功",
                "ssh_duration": connection_result.duration,
                "ssh_details": connection_result.details,
                "chaosblade_installed": status["installed"],
                "chaosblade_version": status.get("version") if status["installed"] else None,
                "chaosblade_path": status.get("install_path") if status["installed"] else None,
                "message": f"SSH连接成功 (耗时: {connection_result.duration:.2f}s)" +
                          (f"，ChaosBlade已安装 (v{status.get('version')})" if status["installed"] else "，ChaosBlade未安装")
            }
        else:
            test_result = {
                "connected": False,
                "ssh_status": "连接失败",
                "ssh_duration": connection_result.duration,
                "message": f"SSH连接失败: {connection_result.message}",
                "error": connection_result.message
            }

        return response_builder.success(data=test_result, message="连接测试完成")

    except Exception as e:
        logger.error(f"连接测试失败: {str(e)}")
        return response_builder.error(
            message="连接测试失败",
            data={"connected": False, "error": str(e)}
        )





@router.get("/version", response_model=APIResponse[dict], summary="获取ChaosBlade版本信息")
async def get_chaosblade_version(
    service: ChaosBladeService = Depends(get_chaosblade_service),
    current_user: User = Depends(get_current_user)
):
    """获取ChaosBlade版本信息"""
    version_info = {
        "supported_version": "1.7.2",
        "download_url": "https://github.com/chaosblade-io/chaosblade/releases",
        "documentation": "https://chaosblade.io/docs/",
        "fault_types": [
            {
                "type": "cpu",
                "description": "CPU故障注入",
                "scenarios": ["load"]
            },
            {
                "type": "memory", 
                "description": "内存故障注入",
                "scenarios": ["load"]
            },
            {
                "type": "network",
                "description": "网络故障注入", 
                "scenarios": ["delay", "loss", "duplicate", "corrupt"]
            },
            {
                "type": "disk",
                "description": "磁盘故障注入",
                "scenarios": ["burn", "fill"]
            },
            {
                "type": "process",
                "description": "进程故障注入",
                "scenarios": ["kill", "stop"]
            }
        ]
    }
    
    return response_builder.success(data=version_info, message="获取版本信息成功")


@router.post("/uninstall", response_model=APIResponse[dict], summary="卸载ChaosBlade")
async def uninstall_chaosblade(
    request: ChaosBladeInstallRequest,
    service: ChaosBladeService = Depends(get_chaosblade_service),
    env_service: EnvironmentService = Depends(get_environment_service),
    current_user: User = Depends(get_current_superuser)
):
    """
    卸载ChaosBlade

    Args:
        request: 卸载请求（复用安装请求结构）
        service: ChaosBlade服务
        env_service: 环境服务
        current_user: 当前用户

    Returns:
        卸载结果
    """
    logger.info(f"开始卸载ChaosBlade，主机IDs: {request.host_ids}")

    results = []
    success_count = 0
    total_hosts = len(request.host_ids) if request.host_ids else 0

    # 如果没有指定主机ID，获取所有Linux主机
    if not request.host_ids:
        environments = await env_service.get_linux_environments()
        host_ids = [env.id for env in environments]
    else:
        host_ids = request.host_ids
        total_hosts = len(host_ids)

    for host_id in host_ids:
        try:
            # 从环境管理模块获取真实的主机信息
            environment = await env_service.get_environment(host_id)
            if not environment:
                results.append({
                    "host_id": host_id,
                    "success": False,
                    "message": f"环境ID {host_id} 不存在",
                    "error": True
                })
                continue

            # 构建主机连接信息
            host_info = {
                "host": environment.host,
                "port": environment.port or 22,
                "username": environment.config.get("username", "root") if environment.config else "root",
                "password": environment.config.get("password") if environment.config else None,
                "private_key_path": environment.config.get("private_key_path") if environment.config else None
            }

            # 执行卸载
            result = await service.uninstall_chaosblade(host_info)

            if result["success"]:
                success_count += 1

            results.append({
                "host_id": host_id,
                "host_name": environment.name,
                "host_address": environment.host,
                "success": result["success"],
                "message": result["message"],
                "output": result.get("output", "")
            })

        except Exception as e:
            results.append({
                "host_id": host_id,
                "success": False,
                "message": str(e),
                "error": True
            })

    return response_builder.success(
        data={
            "results": results,
            "success_count": success_count,
            "total_hosts": total_hosts,
            "summary": f"卸载完成，成功: {success_count}/{total_hosts}"
        },
        message=f"ChaosBlade卸载完成，成功: {success_count}/{total_hosts}"
    )
