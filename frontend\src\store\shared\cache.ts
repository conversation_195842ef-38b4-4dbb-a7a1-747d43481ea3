/**
 * 通用缓存状态管理
 * 提供可复用的缓存逻辑
 */
import { ref, computed } from 'vue'

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiry?: number
}

/**
 * 缓存配置选项
 */
export interface CacheOptions {
  defaultTTL?: number // 默认过期时间（毫秒）
  maxSize?: number    // 最大缓存数量
  autoCleanup?: boolean // 自动清理过期缓存
  cleanupInterval?: number // 清理间隔（毫秒）
}

/**
 * 创建缓存状态管理
 */
export function useCache<T = any>(options: CacheOptions = {}) {
  const {
    defaultTTL = 5 * 60 * 1000, // 5分钟
    maxSize = 100,
    autoCleanup = true,
    cleanupInterval = 60 * 1000 // 1分钟
  } = options

  // 缓存存储
  const cache = ref<Map<string, CacheItem<T>>>(new Map())
  
  // 清理定时器
  let cleanupTimer: NodeJS.Timeout | null = null

  // 计算属性
  const size = computed(() => cache.value.size)
  const keys = computed(() => Array.from(cache.value.keys()))
  const isEmpty = computed(() => cache.value.size === 0)
  const isFull = computed(() => cache.value.size >= maxSize)

  // 启动自动清理
  const startAutoCleanup = () => {
    if (autoCleanup && !cleanupTimer) {
      cleanupTimer = setInterval(() => {
        cleanupExpired()
      }, cleanupInterval)
    }
  }

  // 停止自动清理
  const stopAutoCleanup = () => {
    if (cleanupTimer) {
      clearInterval(cleanupTimer)
      cleanupTimer = null
    }
  }

  // 检查是否过期
  const isExpired = (item: CacheItem<T>): boolean => {
    if (!item.expiry) return false
    return Date.now() > item.expiry
  }

  // 设置缓存
  const set = (key: string, data: T, ttl?: number): void => {
    const now = Date.now()
    const expiry = ttl ? now + ttl : (defaultTTL ? now + defaultTTL : undefined)
    
    // 如果缓存已满，删除最旧的项
    if (isFull.value && !cache.value.has(key)) {
      const oldestKey = keys.value[0]
      if (oldestKey) {
        cache.value.delete(oldestKey)
      }
    }
    
    cache.value.set(key, {
      data,
      timestamp: now,
      expiry
    })
  }

  // 获取缓存
  const get = (key: string): T | null => {
    const item = cache.value.get(key)
    if (!item) return null
    
    if (isExpired(item)) {
      cache.value.delete(key)
      return null
    }
    
    return item.data
  }

  // 检查是否存在
  const has = (key: string): boolean => {
    const item = cache.value.get(key)
    if (!item) return false
    
    if (isExpired(item)) {
      cache.value.delete(key)
      return false
    }
    
    return true
  }

  // 删除缓存
  const remove = (key: string): boolean => {
    return cache.value.delete(key)
  }

  // 清空所有缓存
  const clear = (): void => {
    cache.value.clear()
  }

  // 清理过期缓存
  const cleanupExpired = (): number => {
    let cleanedCount = 0
    const now = Date.now()
    
    for (const [key, item] of cache.value.entries()) {
      if (item.expiry && now > item.expiry) {
        cache.value.delete(key)
        cleanedCount++
      }
    }
    
    return cleanedCount
  }

  // 获取缓存信息
  const getInfo = (key: string) => {
    const item = cache.value.get(key)
    if (!item) return null
    
    return {
      timestamp: item.timestamp,
      expiry: item.expiry,
      age: Date.now() - item.timestamp,
      isExpired: isExpired(item)
    }
  }

  // 获取所有缓存信息
  const getAllInfo = () => {
    const info: Record<string, any> = {}
    for (const [key, item] of cache.value.entries()) {
      info[key] = {
        timestamp: item.timestamp,
        expiry: item.expiry,
        age: Date.now() - item.timestamp,
        isExpired: isExpired(item)
      }
    }
    return info
  }

  // 启动自动清理
  if (autoCleanup) {
    startAutoCleanup()
  }

  return {
    // 状态
    cache: readonly(cache),
    
    // 计算属性
    size,
    keys,
    isEmpty,
    isFull,
    
    // 方法
    set,
    get,
    has,
    remove,
    clear,
    cleanupExpired,
    getInfo,
    getAllInfo,
    startAutoCleanup,
    stopAutoCleanup
  }
}

/**
 * 全局缓存实例
 */
export const globalCache = useCache({
  defaultTTL: 10 * 60 * 1000, // 10分钟
  maxSize: 200,
  autoCleanup: true
})
