"""
请求日志中间件
"""
import time
import json
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.logger import operation_logger


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志中间件
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并记录日志
        
        Args:
            request: HTTP请求
            call_next: 下一个处理器
            
        Returns:
            HTTP响应
        """
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        url = str(request.url)
        headers = dict(request.headers)
        client_ip = request.client.host if request.client else "unknown"
        
        # 记录请求开始
        request_data = {
            "method": method,
            "url": url,
            "client_ip": client_ip,
            "user_agent": headers.get("user-agent", "unknown")
        }
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录成功请求
            await operation_logger.log_operation(
                operation="http_request",
                request_data={
                    **request_data,
                    "status_code": response.status_code,
                    "process_time": f"{process_time:.3f}s"
                },
                result="success"
            )
            
            # 添加处理时间到响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录失败请求
            await operation_logger.log_operation(
                operation="http_request",
                request_data={
                    **request_data,
                    "process_time": f"{process_time:.3f}s"
                },
                result="failed",
                error_msg=str(e)
            )
            
            # 重新抛出异常
            raise 