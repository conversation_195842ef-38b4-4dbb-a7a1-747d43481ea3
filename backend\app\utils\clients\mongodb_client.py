"""
MongoDB连接客户端
支持MongoDB数据库的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional, List

from .base_client import BaseClient, ConnectionResult

try:
    from motor.motor_asyncio import AsyncIOMotorClient
    from pymongo.errors import PyMongoError, ServerSelectionTimeoutError
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False


class MongoDBClient(BaseClient):
    """
    MongoDB连接客户端
    支持MongoDB数据库的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化MongoDB客户端
        
        Args:
            config: MongoDB连接配置
                - host: MongoDB主机地址 (默认: localhost)
                - port: MongoDB端口 (默认: 27017)
                - database: 数据库名称 (可选)
                - username: 用户名 (可选)
                - password: 密码 (可选)
                - auth_source: 认证数据库 (默认: admin)
                - replica_set: 副本集名称 (可选)
                - ssl: 是否使用SSL (默认: False)
                - ssl_cert_reqs: SSL证书要求 (可选)
                - ssl_ca_certs: SSL CA证书文件路径 (可选)
        """
        super().__init__(config)
        
        if not MONGODB_AVAILABLE:
            raise ImportError("Motor库未安装，请运行: pip install motor")
        
        # 设置默认值
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 27017)
        self.database = config.get('database', 'test')
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.auth_source = config.get('auth_source', 'admin')
        self.replica_set = config.get('replica_set', '')
        self.ssl = config.get('ssl', False)
        self.ssl_cert_reqs = config.get('ssl_cert_reqs', '')
        self.ssl_ca_certs = config.get('ssl_ca_certs', '')
        
        self.client = None
        self.db = None

    def _build_connection_uri(self) -> str:
        """
        构建MongoDB连接URI
        
        Returns:
            str: MongoDB连接URI
        """
        # 基础URI
        if self.username and self.password:
            uri = f"mongodb://{self.username}:{self.password}@{self.host}:{self.port}"
        else:
            uri = f"mongodb://{self.host}:{self.port}"
        
        # 添加数据库
        if self.database:
            uri += f"/{self.database}"
        
        # 添加查询参数
        params = []
        
        if self.auth_source and self.username:
            params.append(f"authSource={self.auth_source}")
        
        if self.replica_set:
            params.append(f"replicaSet={self.replica_set}")
        
        if self.ssl:
            params.append("ssl=true")
            if self.ssl_cert_reqs:
                params.append(f"ssl_cert_reqs={self.ssl_cert_reqs}")
            if self.ssl_ca_certs:
                params.append(f"ssl_ca_certs={self.ssl_ca_certs}")
        
        if params:
            uri += "?" + "&".join(params)
        
        return uri

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立MongoDB连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 构建连接URI
            connection_uri = self._build_connection_uri()
            
            # 创建客户端
            self.client = AsyncIOMotorClient(
                connection_uri,
                serverSelectionTimeoutMS=timeout * 1000,
                connectTimeoutMS=timeout * 1000,
                socketTimeoutMS=timeout * 1000
            )
            
            # 测试连接
            await self.client.admin.command('ping')
            
            # 获取数据库
            if self.database:
                self.db = self.client[self.database]
            
            self.is_connected = True
            duration = time.time() - start_time
            
            # 获取服务器信息
            server_info = await self.client.server_info()
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到MongoDB {self.host}:{self.port}",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "database": self.database,
                    "version": server_info.get('version', 'unknown'),
                    "replica_set": self.replica_set or None
                }
            )
            
        except ServerSelectionTimeoutError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MongoDB连接超时: {str(e)}",
                duration=duration,
                details={"error_type": "ServerSelectionTimeoutError"}
            )
        except PyMongoError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MongoDB连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "PyMongoError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MongoDB连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开MongoDB连接"""
        try:
            if self.client:
                self.client.close()
                self.client = None
                self.db = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试MongoDB连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        start_time = time.time()
        
        try:
            # 如果没有建立连接，先建立连接
            if not self.client:
                connect_result = await self.connect(timeout)
                if not connect_result.success:
                    return connect_result
            
            # 执行ping命令测试连接
            await self.client.admin.command('ping')
            
            # 获取数据库统计信息
            stats = {}
            if self.db:
                try:
                    db_stats = await self.db.command('dbStats')
                    stats = {
                        "collections": db_stats.get('collections', 0),
                        "dataSize": db_stats.get('dataSize', 0),
                        "storageSize": db_stats.get('storageSize', 0)
                    }
                except:
                    # 如果没有权限获取统计信息，忽略错误
                    pass
            
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"MongoDB连接测试成功",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "database": self.database,
                    "stats": stats
                }
            )
            
        except PyMongoError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MongoDB连接测试失败: {str(e)}",
                duration=duration,
                details={"error_type": "PyMongoError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MongoDB连接测试异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def get_collections(self) -> List[str]:
        """
        获取集合列表
        
        Returns:
            List[str]: 集合名称列表
        """
        if not self.db:
            raise RuntimeError("MongoDB数据库未连接")
        
        try:
            collections = await self.db.list_collection_names()
            return collections
        except Exception as e:
            raise RuntimeError(f"获取集合列表失败: {str(e)}")

    async def get_databases(self) -> List[str]:
        """
        获取数据库列表
        
        Returns:
            List[str]: 数据库名称列表
        """
        if not self.client:
            raise RuntimeError("MongoDB未连接")
        
        try:
            db_list = await self.client.list_database_names()
            return db_list
        except Exception as e:
            raise RuntimeError(f"获取数据库列表失败: {str(e)}")

    async def execute_command(self, command: Dict[str, Any], database: Optional[str] = None) -> Dict[str, Any]:
        """
        执行MongoDB命令
        
        Args:
            command: MongoDB命令
            database: 数据库名称 (可选，默认使用配置的数据库)
            
        Returns:
            Dict: 命令执行结果
        """
        if not self.client:
            raise RuntimeError("MongoDB未连接")
        
        try:
            db = self.client[database] if database else self.db
            if not db:
                raise RuntimeError("未指定数据库")
            
            result = await db.command(command)
            return {
                "success": True,
                "result": result,
                "command": str(command)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": str(command)
            }
