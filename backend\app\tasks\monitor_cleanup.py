"""
监控数据清理定时任务
"""
import asyncio
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from app.database.session import get_async_session
from app.services.chaos.chaos_monitor_service import ChaosMonitorService
from app.utils.logger import setup_logger

logger = setup_logger()


class MonitorDataCleanupTask:
    """监控数据清理任务"""
    
    def __init__(self):
        self.cleanup_hours = 24  # 清理24小时前的数据
    
    async def run_cleanup(self):
        """执行清理任务"""
        try:
            # 获取数据库会话
            async for db in get_async_session():
                monitor_service = ChaosMonitorService(db)
                
                # 执行清理
                deleted_count = await monitor_service.cleanup_old_data(self.cleanup_hours)
                
                if deleted_count > 0:
                    logger.info(f"监控数据清理完成: 删除了 {deleted_count} 条过期数据")
                else:
                    logger.debug("监控数据清理完成: 没有过期数据需要清理")
                
                break  # 只需要一个会话
                
        except Exception as e:
            logger.error(f"监控数据清理失败: {e}")
    
    async def start_periodic_cleanup(self, interval_hours: int = 1):
        """启动周期性清理任务"""
        logger.info(f"启动监控数据清理任务，清理间隔: {interval_hours}小时，数据保留: {self.cleanup_hours}小时")
        
        while True:
            try:
                await self.run_cleanup()
                
                # 等待下次清理
                await asyncio.sleep(interval_hours * 3600)  # 转换为秒
                
            except Exception as e:
                logger.error(f"清理任务异常: {e}")
                # 出错后等待一段时间再重试
                await asyncio.sleep(300)  # 5分钟后重试


# 全局清理任务实例
cleanup_task = MonitorDataCleanupTask()


async def start_monitor_cleanup_task():
    """启动监控数据清理任务"""
    # 创建后台任务
    task = asyncio.create_task(cleanup_task.start_periodic_cleanup())
    logger.info("监控数据清理任务已启动")
    return task


async def run_manual_cleanup():
    """手动执行一次清理"""
    await cleanup_task.run_cleanup()
