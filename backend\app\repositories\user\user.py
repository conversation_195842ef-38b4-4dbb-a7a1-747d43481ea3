"""
用户仓储类
专注于用户相关的数据访问操作
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.repositories.base import BaseRepository
from app.models.user.user import User
from app.models.user.role import Role
from app.schemas.user.user import UserCreate, UserUpdate
from app.utils.security import get_password_hash, verify_password

class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """
    用户仓储类
    封装用户相关的数据访问逻辑
    """

    def __init__(self, db: AsyncSession):
        """
        初始化用户仓储
        
        Args:
            db: 数据库会话
        """
        super().__init__(User, db)

    async def get_by_username(self, username: str) -> Optional[User]:
        """
        根据用户名获取用户（包含角色信息）
        
        Args:
            username: 用户名
            
        Returns:
            用户实例或None
        """
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.username == username)
        )
        return result.scalar_one_or_none()

    async def get_by_email(self, email: str) -> Optional[User]:
        """
        根据邮箱获取用户（包含角色信息）
        
        Args:
            email: 邮箱
            
        Returns:
            用户实例或None
        """
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.email == email)
        )
        return result.scalar_one_or_none()

    async def get_with_roles(self, user_id: int) -> Optional[User]:
        """
        获取用户信息（包含角色信息）
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户实例或None
        """
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.id == user_id)
        )
        return result.scalar_one_or_none()

    async def search_users(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        keyword: Optional[str] = None,
        status: Optional[str] = None
    ) -> tuple[List[User], int]:
        """
        搜索用户列表（包含角色信息）
        
        Args:
            skip: 跳过的记录数
            limit: 限制的记录数
            keyword: 搜索关键词
            status: 用户状态
            
        Returns:
            (用户列表, 总数)
        """
        # 构建查询，预加载角色关系
        from sqlalchemy import or_, func
        
        query = select(User).options(selectinload(User.roles))
        count_query = select(func.count(User.id))
        
        # 添加搜索条件
        conditions = []
        
        if keyword:
            search_condition = or_(
                User.username.ilike(f"%{keyword}%"),
                User.nickname.ilike(f"%{keyword}%"),
                User.email.ilike(f"%{keyword}%")
            )
            conditions.append(search_condition)
        
        if status:
            conditions.append(User.status == status)
        
        # 应用所有条件
        if conditions:
            from sqlalchemy import and_
            where_condition = and_(*conditions)
            query = query.where(where_condition)
            count_query = count_query.where(where_condition)
        
        # 获取总数
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()
        
        # 分页查询
        query = query.offset(skip).limit(limit).order_by(User.created_at.desc())
        result = await self.db.execute(query)
        users = result.scalars().all()
        
        return users, total

    async def create_user(self, user_data: UserCreate) -> User:
        """
        创建用户（处理密码加密和角色关联）
        
        Args:
            user_data: 用户创建数据
            
        Returns:
            创建的用户实例
        """
        # 准备用户数据
        create_data = user_data.model_dump(exclude={"password", "role_ids"})
        create_data["hashed_password"] = get_password_hash(user_data.password)
        
        # 创建用户实例
        db_user = User(**create_data)
        
        # 处理角色关联
        if user_data.role_ids:
            roles_result = await self.db.execute(
                select(Role).where(Role.id.in_(user_data.role_ids))
            )
            roles = roles_result.scalars().all()
            db_user.roles = roles
        
        # 保存到数据库
        self.db.add(db_user)
        await self.db.commit()
        await self.db.refresh(db_user)
        
        # 预加载角色数据以避免延迟加载问题
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.id == db_user.id)
        )
        user_with_roles = result.scalar_one()
        
        return user_with_roles

    async def update_user(
        self, 
        user: User, 
        user_data: UserUpdate
    ) -> User:
        """
        更新用户（处理角色关联）
        
        Args:
            user: 用户实例
            user_data: 更新数据
            
        Returns:
            更新后的用户实例
        """
        # 更新基础字段
        update_data = user_data.model_dump(exclude={"role_ids"}, exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        # 处理角色关联
        if user_data.role_ids is not None:
            roles_result = await self.db.execute(
                select(Role).where(Role.id.in_(user_data.role_ids))
            )
            roles = roles_result.scalars().all()
            user.roles = roles
        
        # 保存更改
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        return user

    async def authenticate_user(
        self, 
        username: str, 
        password: str
    ) -> Optional[User]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            认证成功的用户实例或None
        """
        user = await self.get_by_username(username)
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        return user

    async def update_password(self, user: User, new_password: str) -> User:
        """
        更新用户密码
        
        Args:
            user: 用户实例
            new_password: 新密码
            
        Returns:
            更新后的用户实例
        """
        user.hashed_password = get_password_hash(new_password)
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        return user

    async def update_last_login(self, user: User) -> User:
        """
        更新最后登录时间
        
        Args:
            user: 用户实例
            
        Returns:
            更新后的用户实例
        """
        from datetime import datetime
        user.last_login_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        return user

    async def is_username_taken(self, username: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查用户名是否已被使用
        
        Args:
            username: 用户名
            exclude_id: 排除的用户ID（用于更新时检查）
            
        Returns:
            是否已被使用
        """
        query = select(User).where(User.username == username)
        if exclude_id:
            query = query.where(User.id != exclude_id)
        
        result = await self.db.execute(query)
        existing_user = result.scalar_one_or_none()
        return existing_user is not None

    async def is_email_taken(self, email: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查邮箱是否已被使用
        
        Args:
            email: 邮箱
            exclude_id: 排除的用户ID（用于更新时检查）
            
        Returns:
            是否已被使用
        """
        query = select(User).where(User.email == email)
        if exclude_id:
            query = query.where(User.id != exclude_id)
        
        result = await self.db.execute(query)
        existing_user = result.scalar_one_or_none()
        return existing_user is not None 