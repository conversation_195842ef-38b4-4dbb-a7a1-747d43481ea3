"""
模型配置管理API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query, Path
from fastapi.responses import StreamingResponse
import json

from app.core.dependencies import get_pagination_params, ModelConfigServiceType
from app.core.responses import response_builder
from app.schemas.base import APIResponse, PaginationResponse
from app.schemas.model.model_config import (
    ModelConfigCreate, ModelConfigUpdate, ModelConfigResponse,
    ModelHealthCheckRequest, ModelHealthCheckResponse,
    ModelCallRequest, ModelCallResponse
)
from app.schemas.common import PaginationParams
from app.models.user.user import User
from app.api.deps import get_current_active_user

router = APIRouter()


@router.post("/", response_model=APIResponse[ModelConfigResponse], summary="创建模型配置")
async def create_model_config(
    model_data: ModelConfigCreate,
    service: ModelConfigServiceType,
    current_user: User = Depends(get_current_active_user)
):
    """创建新的模型配置"""
    model_config = await service.create_model_config(model_data, str(current_user.id))
    return response_builder.success(data=model_config, message="创建模型配置成功")


@router.get("/list", response_model=PaginationResponse[ModelConfigResponse], summary="获取模型配置列表")
async def get_model_config_list(
    service: ModelConfigServiceType,
    name: str = Query(None, description="模型名称搜索关键词"),
    platform: str = Query(None, description="平台类型"),
    status: str = Query(None, description="状态"),
    health_status: str = Query(None, description="健康状态"),
    pagination: PaginationParams = Depends(get_pagination_params)
):
    """获取模型配置列表"""
    from app.schemas.common import SearchParams

    # 构建搜索参数
    search_params = SearchParams(
        current=pagination.current,
        size=pagination.size,
        keyword=name
    )

    models, total = await service.list_model_configs(
        search_params,
        platform=platform,
        status=status,
        health_status=health_status
    )

    return response_builder.paginated(
        records=models,
        total=total,
        current=pagination.current,
        size=pagination.size,
        message="获取模型配置列表成功"
    )


@router.get("/{model_id}", response_model=APIResponse[ModelConfigResponse], summary="获取模型配置详情")
async def get_model_config(
    service: ModelConfigServiceType,
    model_id: int = Path(..., description="模型配置ID")
):
    """获取模型配置详情"""
    model_config = await service.get_model_config(model_id)
    return response_builder.success(data=model_config, message="获取模型配置详情成功")


@router.put("/{model_id}", response_model=APIResponse[ModelConfigResponse], summary="更新模型配置")
async def update_model_config(
    model_data: ModelConfigUpdate,
    service: ModelConfigServiceType,
    model_id: int = Path(..., description="模型配置ID"),
    current_user: User = Depends(get_current_active_user)
):
    """更新模型配置"""
    model_config = await service.update_model_config(model_id, model_data, str(current_user.id))
    return response_builder.success(data=model_config, message="更新模型配置成功")


@router.delete("/{model_id}", response_model=APIResponse[bool], summary="删除模型配置")
async def delete_model_config(
    service: ModelConfigServiceType,
    model_id: int = Path(..., description="模型配置ID")
):
    """删除模型配置"""
    await service.delete_model_config(model_id)
    return response_builder.success(data=True, message="删除模型配置成功")


@router.post("/{model_id}/enable", response_model=APIResponse[ModelConfigResponse], summary="启用模型")
async def enable_model(
    service: ModelConfigServiceType,
    model_id: int = Path(..., description="模型配置ID")
):
    """启用模型"""
    model_config = await service.enable_model(model_id)
    return response_builder.success(data=model_config, message="启用模型成功")


@router.post("/{model_id}/disable", response_model=APIResponse[ModelConfigResponse], summary="停用模型")
async def disable_model(
    service: ModelConfigServiceType,
    model_id: int = Path(..., description="模型配置ID")
):
    """停用模型"""
    model_config = await service.disable_model(model_id)
    return response_builder.success(data=model_config, message="停用模型成功")


@router.get("/available/list", response_model=APIResponse[List[ModelConfigResponse]], summary="获取可用模型列表")
async def get_available_models(
    service: ModelConfigServiceType
):
    """获取所有可用的模型配置（启用且健康）"""
    models = await service.get_available_models()
    return response_builder.success(data=models, message="获取可用模型列表成功")


@router.post("/{model_id}/health-check", response_model=APIResponse[ModelHealthCheckResponse], summary="检查指定模型健康状态")
async def health_check_model(
    service: ModelConfigServiceType,
    model_id: int = Path(..., description="模型配置ID"),
    timeout_seconds: int = Query(None, ge=1, le=60, description="检查超时时间（秒）")
):
    """检查指定模型的健康状态"""
    result = await service.health_check_model(model_id, timeout_seconds)
    return response_builder.success(data=result, message="模型健康检查完成")


@router.post("/health-check/batch", response_model=APIResponse[List[ModelHealthCheckResponse]], summary="批量健康检查")
async def batch_health_check(
    service: ModelConfigServiceType,
    model_ids: List[int] = Query(None, description="模型ID列表，为空时检查所有启用的模型"),
    timeout_seconds: int = Query(None, ge=1, le=60, description="检查超时时间（秒）")
):
    """批量检查模型健康状态"""
    results = await service.batch_health_check(model_ids, timeout_seconds)
    return response_builder.success(data=results, message="批量健康检查完成")


@router.post("/test", response_model=APIResponse[dict], summary="测试模型配置连接")
async def test_model_config(
    model_data: ModelConfigCreate,
    service: ModelConfigServiceType
):
    """测试模型配置连接"""
    result = await service.test_model_connection(model_data)
    return response_builder.success(data=result, message="连接测试完成")


@router.post("/call", response_model=APIResponse[ModelCallResponse], summary="调用模型")
async def call_model(
    request: ModelCallRequest,
    service: ModelConfigServiceType
):
    """统一模型调用接口"""
    result = await service.call_model(request)
    return response_builder.success(data=result, message="模型调用完成")


@router.post("/call/stream", summary="调用模型（流式）")
async def call_model_stream(
    request: ModelCallRequest,
    service: ModelConfigServiceType
):
    """统一模型调用接口（流式）"""

    async def generate():
        try:
            async for chunk in service.call_model_stream(request):
                # 将每个数据块转换为 SSE 格式
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
        except Exception as e:
            # 发送错误信息
            error_chunk = {
                'type': 'error',
                'error_message': str(e)
            }
            yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
        finally:
            # 发送结束信号
            yield "data: [DONE]\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8"
        }
    )


@router.get("/stats/overview", response_model=APIResponse[dict], summary="获取模型统计信息")
async def get_model_stats(
    service: ModelConfigServiceType
):
    """获取模型统计信息"""
    stats = await service.get_platform_stats()
    return response_builder.success(data=stats, message="获取统计信息成功")
