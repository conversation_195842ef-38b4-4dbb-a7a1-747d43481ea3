"""
连接客户端基类
定义统一的连接管理接口
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import time
import asyncio
from datetime import datetime


class ConnectionResult:
    """连接测试结果"""
    
    def __init__(self, success: bool, message: str, duration: float, details: Optional[Dict[str, Any]] = None):
        self.success = success
        self.message = message
        self.duration = duration
        self.details = details or {}
        self.timestamp = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "message": self.message,
            "duration": self.duration,
            "details": self.details,
            "timestamp": self.timestamp
        }


class BaseClient(ABC):
    """
    连接客户端基类
    定义统一的连接管理接口
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化客户端
        
        Args:
            config: 连接配置
        """
        self.config = config
        self.connection = None
        self.is_connected = False

    @abstractmethod
    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        pass

    @abstractmethod
    async def disconnect(self) -> None:
        """断开连接"""
        pass

    @abstractmethod
    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        pass

    async def ping(self, timeout: int = 5) -> ConnectionResult:
        """
        简单的连接检测
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 检测结果
        """
        try:
            start_time = time.time()
            result = await self.test_connection(timeout)
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=result.success,
                message=f"Ping 测试: {result.message}",
                duration=duration,
                details={"ping_result": result.to_dict()}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Ping 测试失败: {str(e)}",
                duration=duration
            )

    def get_connection_info(self) -> Dict[str, Any]:
        """
        获取连接信息
        
        Returns:
            Dict: 连接信息
        """
        return {
            "type": self.__class__.__name__,
            "config": self._mask_sensitive_config(),
            "is_connected": self.is_connected,
            "connection_status": "connected" if self.is_connected else "disconnected"
        }

    def _mask_sensitive_config(self) -> Dict[str, Any]:
        """
        隐藏敏感配置信息
        
        Returns:
            Dict: 脱敏后的配置
        """
        masked_config = self.config.copy()
        sensitive_keys = ['password', 'secret', 'key', 'token', 'auth']
        
        for key in masked_config:
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                masked_config[key] = "***"
        
        return masked_config

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect() 