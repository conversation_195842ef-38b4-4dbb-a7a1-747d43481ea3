@use '@styles/variables.scss' as *;

.login {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(80, 200, 120, 0.1)),
              url('@imgs/login/lf_bg.webp') center center / cover no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(74, 144, 226, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(80, 200, 120, 0.15) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
    pointer-events: none;
  }
}

// 暗色主题适配
.dark .login {
  .login-wrap {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.3),
      0 8px 30px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(0, 0, 0, 0.7);
      box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.4),
        0 12px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
  }

  .el-input__inner {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    color: var(--el-text-color-primary) !important;

    &:focus {
      background: rgba(255, 255, 255, 0.15) !important;
      border-color: #4a90e2 !important;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.15) !important;
      border-color: rgba(74, 144, 226, 0.4) !important;
    }
  }
}

// 背景动画
@keyframes backgroundShift {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.8;
  }
}

// 输入框样式重写
.login {
  .el-input__inner {
    height: 48px !important;
    background: var(--el-bg-color) !important;
    border: 2px solid var(--el-border-color) !important;
    border-radius: 12px !important;
    font-size: 16px !important;
    padding: 0 16px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px var(--el-box-shadow-light);
    color: var(--el-text-color-primary) !important;

    &:focus {
      border-color: #4a90e2 !important;
      background: var(--el-bg-color) !important;
      box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.15), 0 8px 30px var(--el-box-shadow);
      transform: translateY(-1px);
    }

    &:hover {
      background: var(--el-bg-color) !important;
      border-color: rgba(74, 144, 226, 0.3) !important;
      box-shadow: 0 6px 25px var(--el-box-shadow);
    }

    &::placeholder {
      color: var(--el-text-color-placeholder) !important;
      font-weight: 400;
    }
  }

  .el-input--medium .el-input__inner {
    height: var(--el-component-custom-height);
    line-height: var(--el-component-custom-height);
  }

  .login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 420px;
    margin: 0 auto;

    .top-right-wrap {
      position: fixed;
      top: 30px;
      right: 30px;
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .btn {
        display: inline-block;
        padding: 5px;
        margin-left: 15px;
        cursor: pointer;
        user-select: none;
        transition: all 0.3s;

        i {
          font-size: 18px;
        }

        &:hover {
          color: var(--main-color) !important;
        }
      }
    }

    .header {
      display: none;
    }

    .login-wrap {
      position: relative;
      background: var(--el-bg-color-overlay);
      backdrop-filter: blur(30px) saturate(150%);
      border: 1px solid var(--el-border-color-light);
      border-radius: 24px;
      padding: 48px 40px;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 30px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 var(--el-border-color-lighter);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;

      // 渐变边框效果
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        padding: 1px;
        background: linear-gradient(135deg,
          rgba(74, 144, 226, 0.3),
          rgba(80, 200, 120, 0.3),
          rgba(74, 144, 226, 0.3)
        );
        border-radius: 24px;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: xor;
        -webkit-mask-composite: xor;
        pointer-events: none;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow:
          0 30px 80px rgba(0, 0, 0, 0.15),
          0 12px 40px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.9);
      }

      .form {
        width: 100%;

        .brand-section {
          text-align: center;
          margin-bottom: 40px;

          .logo {
            width: 56px;
            height: 56px;
            margin: 0 auto 20px;
            filter: drop-shadow(0 4px 12px rgba(74, 144, 226, 0.3));
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.05);
            }
          }

          .brand-name {
            font-size: 28px;
            font-weight: 700;
            color: var(--el-text-color-primary);
            margin: 0 0 8px 0;
            letter-spacing: -0.5px;
          }

          .brand-desc {
            font-size: 16px;
            color: var(--el-text-color-regular);
            margin: 0 0 32px 0;
            font-weight: 400;
          }
        }

        .login-section {
          .login-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0 0 32px 0;
            text-align: center;
          }
        }

        // 表单项间距
        .el-form-item {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        // 移除未使用的样式

        .forget-password {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 20px 0 32px 0;
          font-size: 14px;

          .el-checkbox {
            .el-checkbox__label {
              color: var(--el-text-color-regular);
              font-weight: 400;
            }

            .el-checkbox__inner {
              border-radius: 4px;
              border-color: var(--el-border-color);
              background-color: var(--el-bg-color);

              &:hover {
                border-color: #4a90e2;
              }
            }

            &.is-checked .el-checkbox__inner {
              background-color: #4a90e2;
              border-color: #4a90e2;
            }
          }

          a {
            color: #4a90e2;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
              color: #50c878;
            }
          }
        }

        .login-btn {
          width: 100%;
          height: 48px !important;
          font-size: 16px !important;
          font-weight: 600 !important;
          border: none !important;
          border-radius: 12px !important;
          background: linear-gradient(135deg, #4a90e2 0%, #50c878 100%) !important;
          color: white !important;
          box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(74, 144, 226, 0.4);
            background: linear-gradient(135deg, #50c878 0%, #4a90e2 100%) !important;

            &::before {
              left: 100%;
            }
          }

          &:active {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
          }

          &:disabled,
          &.is-loading {
            opacity: 0.8;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);

            &::before {
              display: none;
            }
          }
        }

        .back-btn {
          width: 100%;
          height: 40px !important;
        }

        .footer {
          margin-top: 20px;
          font-size: 14px;
          color: var(--art-text-gray-800);

          a {
            color: var(--main-color);
            text-decoration: none;
          }
        }
      }
    }
  }
}

// 平板适配
@media only screen and (max-width: $device-ipad-pro) {
  .login {
    padding: 20px;

    .login-container {
      max-width: 400px;

      .login-wrap {
        padding: 40px 32px;
        border-radius: 20px;

        .brand-section {
          margin-bottom: 32px;

          .logo {
            width: 48px;
            height: 48px;
          }

          .brand-name {
            font-size: 24px;
          }

          .brand-desc {
            font-size: 14px;
          }
        }

        .login-section .login-title {
          font-size: 20px;
        }
      }
    }
  }
}

// 手机端适配
@media only screen and (max-width: $device-phone) {
  .login {
    padding: 16px;
    align-items: flex-start;
    padding-top: 8vh;

    .login-container {
      max-width: 100%;

      .login-wrap {
        padding: 32px 24px;
        border-radius: 16px;
        background: var(--el-bg-color-overlay);
        backdrop-filter: blur(40px);

        .brand-section {
          margin-bottom: 24px;

          .logo {
            width: 40px;
            height: 40px;
            margin-bottom: 16px;
          }

          .brand-name {
            font-size: 20px;
          }

          .brand-desc {
            font-size: 13px;
            margin-bottom: 24px;
          }
        }

        .login-section .login-title {
          font-size: 18px;
          margin-bottom: 24px;
        }

        .el-form-item {
          margin-bottom: 20px;
        }

        .forget-password {
          margin: 16px 0 24px 0;
          font-size: 13px;
        }

        .login-btn {
          height: 44px !important;
          font-size: 15px !important;
        }
      }

      .top-right-wrap {
        top: 20px;
        right: 20px;

        .btn {
          padding: 8px;
          margin-left: 10px;

          i {
            font-size: 16px;
          }
        }
      }
    }
  }
}
