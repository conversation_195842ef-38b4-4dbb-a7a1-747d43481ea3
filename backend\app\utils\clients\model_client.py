"""
简化的统一模型客户端
使用 OpenAI 库提供统一的模型调用接口
"""
import asyncio
import time
from typing import Dict, Any, List, Optional, AsyncGenerator
from openai import AsyncOpenAI
from app.models.model.model_config import ModelConfig
from app.utils.encryption import safe_decrypt_api_key
from app.utils.logger import setup_logger

logger = setup_logger()


class ModelClient:
    """
    统一模型客户端
    使用 OpenAI 库统一调用各种模型平台
    """
    
    def __init__(self, model_config: ModelConfig):
        """初始化客户端"""
        self.model_config = model_config
        self.timeout = model_config.timeout_seconds
        self._client: Optional[AsyncOpenAI] = None
    
    def _get_client(self) -> AsyncOpenAI:
        """获取或创建 AsyncOpenAI 客户端"""
        if self._client is None:
            # 解密 API 密钥
            api_key = safe_decrypt_api_key(self.model_config.api_key_encrypted) if self.model_config.api_key_encrypted else "dummy-key"
            
            self._client = AsyncOpenAI(
                api_key=api_key,
                base_url=self.model_config.api_url.rstrip('/'),
                timeout=self.timeout
            )
        
        return self._client
    
    def _format_messages(self, prompt: str) -> List[Dict[str, str]]:
        """格式化消息"""
        messages = []
        
        # 添加系统提示词（如果有）
        if self.model_config.prompt:
            messages.append({
                "role": "system",
                "content": self.model_config.prompt
            })
        
        # 添加用户消息
        messages.append({
            "role": "user", 
            "content": prompt
        })
        
        return messages
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        start_time = time.time()
        
        try:
            client = self._get_client()
            
            # 使用简单的测试消息
            response = await client.chat.completions.create(
                model=self.model_config.model_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
                timeout=self.timeout
            )
            
            response_time = int((time.time() - start_time) * 1000)
            
            return {
                'is_healthy': True,
                'response_time_ms': response_time,
                'error_message': None,
                'details': f"模型 {self.model_config.model_name} 响应正常"
            }
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            
            logger.warning(f"模型 {self.model_config.name} 健康检查失败: {error_msg}")
            
            return {
                'is_healthy': False,
                'response_time_ms': response_time,
                'error_message': error_msg,
                'details': f"模型 {self.model_config.model_name} 连接失败"
            }
    
    async def call_model(self, prompt: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """调用模型（非流式）"""
        start_time = time.time()
        
        try:
            client = self._get_client()
            messages = self._format_messages(prompt)
            
            # 合并参数
            call_params = {
                'model': self.model_config.model_name,
                'messages': messages,
                'max_tokens': parameters.get('max_tokens', self.model_config.max_tokens),
                'timeout': self.timeout
            }
            
            # 添加其他参数
            if parameters:
                for key, value in parameters.items():
                    if key not in ['max_tokens'] and value is not None:
                        call_params[key] = value
            
            response = await client.chat.completions.create(**call_params)
            
            response_time = int((time.time() - start_time) * 1000)
            
            return {
                'success': True,
                'response': response.choices[0].message.content,
                'response_time_ms': response_time,
                'error_message': None,
                'usage': {
                    'prompt_tokens': response.usage.prompt_tokens if response.usage else 0,
                    'completion_tokens': response.usage.completion_tokens if response.usage else 0,
                    'total_tokens': response.usage.total_tokens if response.usage else 0
                }
            }
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            
            logger.error(f"模型 {self.model_config.name} 调用失败: {error_msg}")
            
            return {
                'success': False,
                'response': None,
                'response_time_ms': response_time,
                'error_message': error_msg,
                'usage': None
            }
    
    async def call_model_stream(self, prompt: str, parameters: Optional[Dict[str, Any]] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """调用模型（流式）"""
        start_time = time.time()
        
        try:
            client = self._get_client()
            messages = self._format_messages(prompt)
            
            # 合并参数
            call_params = {
                'model': self.model_config.model_name,
                'messages': messages,
                'max_tokens': parameters.get('max_tokens', self.model_config.max_tokens),
                'stream': True,
                'timeout': self.timeout
            }
            
            # 添加其他参数
            if parameters:
                for key, value in parameters.items():
                    if key not in ['max_tokens'] and value is not None:
                        call_params[key] = value
            
            stream = await client.chat.completions.create(**call_params)
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield {
                        'type': 'content',
                        'content': chunk.choices[0].delta.content,
                        'finish_reason': chunk.choices[0].finish_reason
                    }
                
                # 处理结束
                if chunk.choices and chunk.choices[0].finish_reason:
                    response_time = int((time.time() - start_time) * 1000)
                    yield {
                        'type': 'done',
                        'response_time_ms': response_time,
                        'finish_reason': chunk.choices[0].finish_reason
                    }
                    break
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            
            logger.error(f"模型 {self.model_config.name} 流式调用失败: {error_msg}")
            
            yield {
                'type': 'error',
                'error_message': error_msg,
                'response_time_ms': response_time
            }
    
    async def close(self):
        """关闭客户端"""
        if self._client:
            await self._client.close()
            self._client = None
