<template>
  <div class="chaos-monitor-page">

    <!-- 监控参数选择 -->
    <div class="monitor-controls">
      <el-card>
        <div class="controls-content">
          <div class="control-group">
            <label>选择环境:</label>
            <el-select
              v-model="queryParams.environmentId"
              placeholder="请选择环境"
              style="width: 200px"
              :loading="environmentsLoading"
              clearable
              filterable
              @change="handleEnvironmentChange"
            >
              <el-option
                v-for="env in environments"
                :key="env.id"
                :label="`${env.name} (${env.host}:${env.port})`"
                :value="env.id"
              />
            </el-select>
          </div>
          
          <div class="control-group">
            <label>时间范围:</label>
            <el-select v-model="queryParams.hours" style="width: 120px">
              <el-option label="1小时" :value="1" />
              <el-option label="6小时" :value="6" />
              <el-option label="24小时" :value="24" />
            </el-select>
          </div>
          
          <div class="control-group">
            <el-button 
              type="primary" 
              @click="loadMonitorData"
              :loading="loading"
              :disabled="!canQuery"
            >
              查询监控数据
            </el-button>
                    <el-button @click="loadMonitorData" :loading="refreshing" icon="Refresh">
          刷新数据
        </el-button>
        <el-button @click="showCleanupDialog" icon="Delete">
          清理数据
        </el-button>
        <el-switch
          v-model="autoRefresh"
          @change="toggleAutoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
          style="margin-left: 10px;"
        />
                    <div class="status-actions">
              <el-button 
                v-if="!isMonitoring" 
                type="primary"           
                @click="startMonitoring"
                :loading="starting"
              >
                开始监控
              </el-button>
              <el-button 
                v-else 
                type="danger" 
                @click="stopMonitoring"
                :loading="stopping"
              >
                停止监控
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 监控图表 -->
    <div v-if="monitorData && monitorData.charts.length > 0" class="monitor-charts">
      <el-row :gutter="20">
        <el-col 
          v-for="chart in monitorData.charts" 
          :key="chart.metric_type"
          :span="12"
          class="chart-col"
        >
          <MonitorChart
            :title="chart.metric_name"
            :data="chart.data"
            :timestamps="chart.timestamps"
            :unit="chart.unit"
            :color="getMetricColor(chart.metric_type)"
            height="300px"
            :loading="loading"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading && queryParams.environmentId && selectedEnvironment" class="empty-state">
      <el-empty description="暂无监控数据">
        <el-button type="primary" @click="startMonitoring">开始监控</el-button>
      </el-empty>
    </div>

    <!-- 数据清理对话框 -->
    <el-dialog v-model="cleanupDialogVisible" title="清理监控数据" width="400px">
      <div class="cleanup-content">
        <p>清理多少小时前的监控数据？</p>
        <el-input-number
          v-model="cleanupHours"
          :min="1"
          :max="168"
          style="width: 100%"
        />
        <p class="cleanup-warning">注意：此操作不可恢复</p>
      </div>
      <template #footer>
        <el-button @click="cleanupDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeCleanup" :loading="cleaning">
          确认清理
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import MonitorChart from '@/components/chaos/MonitorChart.vue'
import { MonitorService, type MonitorDataResponse, type MonitorStatus } from '@/api/monitorApi'
import { EnvironmentService } from '@/api/envApi'
import type { EnvironmentResponse } from '@/types/api/environment'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const starting = ref(false)
const stopping = ref(false)
const cleaning = ref(false)
const cleanupDialogVisible = ref(false)
const cleanupHours = ref(24)
const autoRefresh = ref(false)
const refreshInterval = ref<NodeJS.Timeout | null>(null)

const queryParams = reactive({
  environmentId: 0,
  hours: 1
})

const monitorData = ref<MonitorDataResponse | null>(null)
const monitorStatus = ref<MonitorStatus>({
  environment_id: 0,
  host_id: 0,
  status: 'not_started',
  data_points: 0
})

// 当前选中的环境信息
const selectedEnvironment = ref<EnvironmentResponse | null>(null)

// 环境数据
const environments = ref<EnvironmentResponse[]>([])
const environmentsLoading = ref(false)

// 计算属性
const canQuery = computed(() => {
  return queryParams.environmentId > 0 && selectedEnvironment.value
})

const hostInfo = computed(() => {
  if (!selectedEnvironment.value) return null
  return {
    id: selectedEnvironment.value.id,
    host: selectedEnvironment.value.host,
    port: selectedEnvironment.value.port
  }
})

const isMonitoring = computed(() => {
  return monitorStatus.value.status === 'running'
})

// 方法
const loadEnvironments = async () => {
  environmentsLoading.value = true
  try {
    const data = await EnvironmentService.getAvailableEnvironments()
    environments.value = data
  } catch (error) {
    ElMessage.error('获取环境列表失败')
    console.error(error)
  } finally {
    environmentsLoading.value = false
  }
}

const handleEnvironmentChange = (environmentId: number) => {
  // 找到选中的环境
  selectedEnvironment.value = environments.value.find(env => env.id === environmentId) || null
}

const loadMonitorData = async () => {
  if (!canQuery.value) {
    ElMessage.warning('请选择环境')
    return
  }

  loading.value = true
  try {
    const data = await MonitorService.getMonitorData(
      queryParams.environmentId,
      selectedEnvironment.value!.id, // 使用环境ID作为主机ID
      queryParams.hours
    )

    monitorData.value = data
    // 只在当前状态不是手动设置的running时才更新状态
    if (monitorStatus.value.status !== 'running' || data.monitor_status.status === 'running') {
      monitorStatus.value = data.monitor_status
    }
  } catch (error) {
    ElMessage.error('获取监控数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const startMonitoring = async () => {
  if (!canQuery.value) return

  starting.value = true
  try {
    await MonitorService.startMonitoring({
      environment_id: queryParams.environmentId,
      host_id: selectedEnvironment.value!.id, // 使用环境ID作为主机ID
      config: MonitorService.getDefaultConfig()
    })
    ElMessage.success('监控已启动')
    // 更新监控状态
    monitorStatus.value.status = 'running'
    // 启动自动刷新
    startAutoRefresh()
    // 延迟加载数据，让监控有时间启动
    setTimeout(async () => {
      await loadMonitorData()
    }, 2000)
  } catch (error) {
    ElMessage.error('启动监控失败')
  } finally {
    starting.value = false
  }
}

const stopMonitoring = async () => {
  stopping.value = true
  try {
    await MonitorService.stopMonitoring({
      environment_id: queryParams.environmentId,
      host_id: selectedEnvironment.value!.id // 使用环境ID作为主机ID
    })
    ElMessage.success('监控已停止')
    // 更新监控状态
    monitorStatus.value.status = 'stopped'
    // 停止自动刷新
    stopAutoRefresh()
    await loadMonitorData()
  } catch (error) {
    ElMessage.error('停止监控失败')
  } finally {
    stopping.value = false
  }
}

// 自动刷新相关方法
const startAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
  autoRefresh.value = true
  refreshInterval.value = setInterval(async () => {
    if (canQuery.value && !loading.value) {
      await loadMonitorData()
    }
  }, 30000) // 每30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
  autoRefresh.value = false
}

const toggleAutoRefresh = () => {
  if (autoRefresh.value) {
    stopAutoRefresh()
  } else {
    startAutoRefresh()
  }
}

const refreshData = async () => {
  if (!canQuery.value) return
  
  refreshing.value = true
  try {
    await loadMonitorData()
  } finally {
    refreshing.value = false
  }
}

const showCleanupDialog = () => {
  cleanupDialogVisible.value = true
}

const executeCleanup = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要清理 ${cleanupHours.value} 小时前的监控数据吗？`,
      '确认清理',
      { type: 'warning' }
    )

    cleaning.value = true
    const deletedCount = await MonitorService.cleanupOldData(cleanupHours.value)
    ElMessage.success(`清理完成，删除了 ${deletedCount} 条数据`)
    cleanupDialogVisible.value = false
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清理数据失败')
    }
  } finally {
    cleaning.value = false
  }
}

const formatStatusText = (status: string): string => {
  return MonitorService.formatStatusText(status)
}

const getStatusTagType = (status: string): string => {
  return MonitorService.getStatusTagType(status)
}

const getMetricColor = (metricType: string): string => {
  return MonitorService.getMetricColor(metricType)
}

const formatTime = (time?: string): string => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

// 生命周期
onMounted(async () => {
  // 加载环境列表
  await loadEnvironments()

  // 从路由参数获取初始值
  const environmentId = route.query.environmentId as string

  if (environmentId) {
    queryParams.environmentId = parseInt(environmentId)
    // 设置选中的环境
    handleEnvironmentChange(queryParams.environmentId)
  }

  // 如果有参数，自动加载数据
  if (canQuery.value) {
    loadMonitorData()
  }
})

onUnmounted(() => {
  // 清理定时器
  stopAutoRefresh()
})
</script>

<style scoped lang="scss">
.chaos-monitor-page {

  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 8px 0;
        color: var(--el-text-color-primary);
      }
      
      .page-description {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .monitor-controls {
    margin-bottom: 20px;
    
    .controls-content {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .control-group {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          font-size: 14px;
          color: var(--el-text-color-regular);
          white-space: nowrap;
        }
      }
    }
  }
  
  .monitor-status {
    margin-bottom: 20px;
    
    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .monitor-charts {
    .chart-col {
      margin-bottom: 20px;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 60px 0;
  }
  
  .cleanup-content {
    text-align: center;
    
    .cleanup-warning {
      color: var(--el-color-warning);
      font-size: 12px;
      margin-top: 8px;
    }
  }
}

@media (max-width: 768px) {
  .chaos-monitor-page {
    padding: 16px;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
    
    .monitor-controls .controls-content {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      
      .control-group {
        flex-direction: column;
        align-items: flex-start;
      }
    }
    
    .monitor-charts {
      .chart-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
