# 混沌测试模块

## 概述

混沌测试模块是一个基于ChaosBlade的故障注入测试平台，用于提升系统的可靠性和稳定性。

## 功能模块

### 1. 概览页面 (`dashboard.vue`)
- 统计信息展示
- 最近任务列表
- 热门场景展示
- 快速操作入口

### 2. 任务管理 (`tasks/`)
- **任务列表** (`index.vue`): 任务的查询、筛选、批量操作
- **任务表单** (`form.vue`): 统一的任务创建/编辑表单，支持场景模板选择
- **任务详情** (`detail.vue`): 任务详细信息、执行记录、操作管理

### 3. 场景管理 (`scenarios/`)
- **场景列表** (`index.vue`): 场景模板的管理，支持卡片和表格视图
- 内置场景初始化
- 场景参数配置和验证

### 4. 执行记录 (`executions/`)
- **执行列表** (`index.vue`): 执行历史记录查询和管理
- **执行详情** (`detail.vue`): 详细的执行信息、日志查看、实时监控

### 5. ChaosBlade管理 (`blade/`)
- **工具管理** (`index.vue`): ChaosBlade工具的安装、状态检查、版本管理

## 技术架构

### 组件设计
- 使用系统统一的 `ArtTable`、`ArtTableHeader`、`ArtSearchBar` 等组件
- 集成 `useTable` Hook 进行表格状态管理
- 使用 `ArtStatsCard`、`ArtDataListCard` 等展示组件

### 状态管理
- 基于 Pinia 的响应式状态管理 (`@/stores/chaos`)
- 统一的API接口层 (`@/api/chaosApi`)
- 完整的TypeScript类型定义 (`@/types/chaos`)

### 路由配置
- 支持权限控制的动态路由
- 国际化支持（中英文）
- 面包屑导航和标签页管理

## API接口

### 任务管理
- `GET /api/chaos/tasks/` - 获取任务列表
- `POST /api/chaos/tasks` - 创建任务
- `GET /api/chaos/tasks/{id}` - 获取任务详情
- `PUT /api/chaos/tasks/{id}` - 更新任务
- `DELETE /api/chaos/tasks/{id}` - 删除任务
- `POST /api/chaos/tasks/{id}/execute` - 执行任务
- `POST /api/chaos/tasks/{id}/pause` - 暂停任务
- `POST /api/chaos/tasks/{id}/terminate` - 终止任务

### 场景管理
- `GET /api/chaos/scenarios/` - 获取场景列表
- `POST /api/chaos/scenarios` - 创建场景
- `GET /api/chaos/scenarios/{id}` - 获取场景详情
- `PUT /api/chaos/scenarios/{id}` - 更新场景
- `DELETE /api/chaos/scenarios/{id}` - 删除场景
- `POST /api/chaos/scenarios/initialize` - 初始化内置场景

### 执行记录
- `GET /api/chaos/executions/` - 获取执行记录列表
- `GET /api/chaos/executions/{id}` - 获取执行记录详情
- `POST /api/chaos/executions/{id}/retry` - 重试执行
- `POST /api/chaos/executions/{id}/cancel` - 取消执行
- `POST /api/chaos/executions/{id}/log` - 获取执行日志

### ChaosBlade管理
- `GET /api/chaos/blade/status` - 检查安装状态
- `POST /api/chaos/blade/install` - 安装ChaosBlade
- `POST /api/chaos/blade/test` - 测试连接
- `GET /api/chaos/blade/version` - 获取版本信息

## 故障类型

支持以下故障类型：
- **CPU**: CPU负载、CPU占用
- **内存**: 内存占用、内存泄漏
- **网络**: 网络延迟、丢包、断网
- **磁盘**: 磁盘IO、磁盘占用
- **进程**: 进程杀死、进程停止
- **K8s**: Pod删除、节点故障

## 使用流程

1. **环境准备**: 配置目标环境和主机信息
2. **工具安装**: 在目标主机上安装ChaosBlade工具
3. **场景配置**: 选择或创建故障注入场景
4. **任务创建**: 基于场景创建具体的测试任务
5. **执行监控**: 执行任务并监控执行过程
6. **结果分析**: 查看执行结果和系统响应

## 开发说明

### 本地开发
```bash
# 启动前端开发服务器
npm run dev

# 访问地址
http://localhost:5175
```

### 组件开发规范
- 使用系统统一的组件库
- 遵循TypeScript类型安全
- 使用Composition API
- 支持国际化

### 样式规范
- 使用系统主题变量
- 支持黑色主题
- 响应式设计
- 统一的交互体验

## 注意事项

1. **安全性**: 故障注入具有破坏性，请在测试环境中使用
2. **权限控制**: 确保用户具有相应的操作权限
3. **监控告警**: 建议配置监控告警，及时发现异常
4. **备份恢复**: 执行前确保有完整的备份和恢复方案
