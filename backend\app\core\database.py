"""
数据库配置和连接管理模块
支持 SQLite、PostgreSQL、MySQL 异步数据库连接
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

# 创建异步数据库引擎
async_engine = create_async_engine(
    settings.DATABASE_URL,
    echo=settings.debug,
    future=True
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 为Alembic创建同步引擎（移除异步驱动前缀）
def get_sync_database_url():
    """获取同步数据库URL"""
    url = settings.DATABASE_URL
    # 移除异步驱动前缀，但保持正确的同步驱动
    if "+aiosqlite" in url:
        return url.replace("+aiosqlite", "")
    elif "+asyncpg" in url:
        return url.replace("+asyncpg", "+psycopg2")  # 使用psycopg2作为同步驱动
    elif "+aiomysql" in url:
        return url.replace("+aiomysql", "+pymysql")  # 使用pymysql作为同步驱动
    return url

# 创建同步引擎用于Alembic迁移
sync_engine = create_engine(
    get_sync_database_url(),
    echo=settings.debug
)

# 创建基础模型类
Base = declarative_base()


async def get_db() -> AsyncSession:
    """
    获取数据库会话的依赖注入函数
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db():
    """
    初始化数据库，创建所有表
    """
    async with async_engine.begin() as conn:
        # 导入所有模型以确保它们被注册到Base.metadata
        from app.models import user  # noqa
        from app.models import role  # noqa
        
        # 创建所有表
        await conn.run_sync(Base.metadata.create_all)


async def close_db():
    """
    关闭数据库连接
    """
    await async_engine.dispose() 