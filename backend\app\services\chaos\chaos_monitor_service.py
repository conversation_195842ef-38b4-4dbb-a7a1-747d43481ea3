"""
混沌测试监控服务
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.chaos.chaos_monitor_repository import ChaosMonitorRepository
from app.models.chaos.chaos_monitor_data import ChaosMonitorData
from app.schemas.chaos.chaos_monitor import (
    ChaosMonitorDataCreate, ChaosMonitorDataUpdate, ChaosMonitorDataResponse,
    ChaosMonitorConfig, ChaosMonitorStatusResponse, ChaosMonitorChartData,
    ChaosMonitorDataResponse as MonitorDataResponse, ChaosMonitorSummary
)
from app.utils.clients.ssh_client import SSHClient
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.logger import setup_logger

logger = setup_logger()


class ChaosMonitorService:
    """监控服务"""

    def __init__(self, db: AsyncSession):
        self.repository = ChaosMonitorRepository(db)
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}
        self.collection_interval = 30  # 30秒采集间隔
    
    async def start_monitoring(
        self,
        environment_id: int,
        host_id: int,
        host_info: Dict[str, Any],
        config: Optional[ChaosMonitorConfig] = None
    ) -> ChaosMonitorStatusResponse:
        """启动监控"""
        monitor_key = f"{environment_id}_{host_id}"

        if monitor_key in self.monitoring_tasks:
            raise_validation_error(f"主机 {host_id} 已在监控中")

        # 使用默认配置
        if not config:
            config = ChaosMonitorConfig()

        # 先测试SSH连接
        ssh_client = None
        try:
            ssh_client = SSHClient(host_info)
            connect_result = await ssh_client.connect()
            if not connect_result.success:
                error_msg = f"SSH连接失败: {connect_result.message}"
                logger.error(error_msg)
                raise_validation_error(f"无法连接到目标主机: {error_msg}")
            logger.info(f"SSH连接测试成功: {host_info.get('host')}:{host_info.get('port', 22)}")
            await ssh_client.disconnect()
        except Exception as e:
            error_msg = f"SSH连接异常: {host_info.get('username', 'unknown')}@{host_info.get('host', 'unknown')}:{host_info.get('port', 22)}, 错误: {str(e)}"
            logger.error(error_msg)
            raise_validation_error(f"无法连接到目标主机: {error_msg}")

        # 创建监控任务
        monitor_task = asyncio.create_task(
            self._run_monitoring_loop(environment_id, host_id, host_info, config)
        )

        # 存储任务引用
        self.monitoring_tasks[monitor_key] = monitor_task

        logger.info(f"启动监控: environment_id={environment_id}, host_id={host_id}")

        return ChaosMonitorStatusResponse(
            environment_id=environment_id,
            host_id=host_id,
            status="running",
            start_time=datetime.now(),
            data_points=0,
            last_collection_time=None
        )
    
    async def stop_monitoring(self, environment_id: int, host_id: int) -> bool:
        """停止监控"""
        monitor_key = f"{environment_id}_{host_id}"

        if monitor_key not in self.monitoring_tasks:
            raise_validation_error(f"主机 {host_id} 未在监控中")

        # 取消监控任务
        task = self.monitoring_tasks[monitor_key]
        task.cancel()

        try:
            await task
        except asyncio.CancelledError:
            pass

        # 清理任务引用
        del self.monitoring_tasks[monitor_key]

        logger.info(f"停止监控: environment_id={environment_id}, host_id={host_id}")
        return True
    
    async def get_monitoring_status(self, environment_id: int, host_id: int) -> ChaosMonitorStatusResponse:
        """获取监控状态"""
        monitor_key = f"{environment_id}_{host_id}"

        # 检查是否正在监控
        is_running = monitor_key in self.monitoring_tasks

        # 获取监控数据统计
        status_info = await self.repository.get_environment_monitor_status(environment_id)

        return ChaosMonitorStatusResponse(
            environment_id=environment_id,
            host_id=host_id,
            status="running" if is_running else ("stopped" if status_info['has_data'] else "not_started"),
            start_time=status_info.get('start_time'),
            data_points=status_info.get('total_points', 0),
            last_collection_time=status_info.get('end_time')
        )
    
    async def get_monitor_data(
        self,
        environment_id: int,
        host_id: int,
        hours: int = 1
    ) -> MonitorDataResponse:
        """获取监控数据"""
        # 获取监控状态
        monitor_status = await self.get_monitoring_status(environment_id, host_id)

        # 获取时间范围内的数据
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        metrics_data = await self.repository.get_metrics_by_time_range(
            environment_id, host_id, start_time, end_time
        )

        # 获取数据摘要
        summary = await self.repository.get_metrics_summary(environment_id, host_id, hours)

        # 构建图表数据
        charts = self._build_chart_data(metrics_data)

        # 获取主机信息（这里需要从环境服务获取）
        host_info = {"id": host_id, "name": f"Host-{host_id}"}  # 临时数据

        return MonitorDataResponse(
            environment_id=environment_id,
            host_id=host_id,
            host_info=host_info,
            monitor_status=monitor_status,
            charts=charts,
            summary=summary
        )
    
    async def _run_monitoring_loop(
        self,
        environment_id: int,
        host_id: int,
        host_info: Dict[str, Any],
        config: ChaosMonitorConfig
    ):
        """监控循环"""
        ssh_client = None
        try:
            # 建立SSH连接
            ssh_client = SSHClient(host_info)
            connect_result = await ssh_client.connect()
            if not connect_result.success:
                logger.error(f"监控循环SSH连接失败: {connect_result.message}")
                return

            logger.info(f"开始监控循环: environment_id={environment_id}, host_id={host_id}")

            while True:
                try:
                    # 采集监控数据
                    metrics = await self._collect_system_metrics(ssh_client)

                    if metrics:
                        # 保存到数据库
                        await self._save_metrics_batch(environment_id, host_id, metrics)
                    
                    # 等待下次采集
                    await asyncio.sleep(self.collection_interval)
                    
                except Exception as e:
                    logger.error(f"采集监控数据失败: {e}")
                    await asyncio.sleep(self.collection_interval)  # 出错也要等待
                    
        except asyncio.CancelledError:
            logger.info(f"监控任务被取消: environment_id={environment_id}, host_id={host_id}")
            raise
        except Exception as e:
            logger.error(f"监控任务异常: {e}")
        finally:
            if ssh_client:
                await ssh_client.disconnect()
    
    async def _collect_system_metrics(self, ssh_client: SSHClient) -> Dict[str, float]:
        """收集系统指标"""
        commands = {
            # CPU使用率：使用sar命令或简单的/proc/loadavg方法
            'cpu': "sar -u 1 1 | tail -1 | awk '{printf \"%.2f\", 100-$8}' || cat /proc/loadavg | awk '{printf \"%.2f\", $1*10}'",

            # 内存使用率：计算已使用内存百分比
            'memory': "free | awk 'NR==2{printf \"%.2f\", $3*100/$2 }'",

            # 网络接收流量：获取主要网络接口的接收字节数
            'network_rx': "cat /proc/net/dev | awk '/eth0|ens|enp/{print $2}' | head -1",

            # 磁盘使用率：获取根分区使用百分比
            'disk_usage': "df / | awk 'NR==2{print $5}' | sed 's/%//'"
        }

        metrics = {}
        for metric_type, cmd in commands.items():
            try:
                result = await ssh_client.execute_command(cmd)
                logger.info(f"执行命令 {metric_type}: 成功={result.get('success')}, 输出='{result.get('stdout', '').strip()}', 错误='{result.get('stderr', '').strip()}'")

                if result.get('success') and result['stdout'].strip():
                    raw_value = result['stdout'].strip()
                    value = float(raw_value)

                    # 对大数值进行单位转换，避免超出数据库字段范围
                    if metric_type == 'network_rx':
                        # 将字节转换为MB，保持在合理范围内
                        value = round(value / (1024 * 1024), 2)  # 转换为MB
                    else:
                        value = round(value, 2)

                    # 确保值在数据库字段范围内 (DECIMAL(8,2) 最大值为 999999.99)
                    if value > 999999.99:
                        value = 999999.99
                    elif value < -999999.99:
                        value = -999999.99

                    metrics[metric_type] = value
                    logger.info(f"指标 {metric_type}: 原始值={raw_value}, 处理后值={value}")
                else:
                    # 如果主命令失败，尝试备用方法
                    if metric_type == 'cpu':
                        logger.info("vmstat命令失败，尝试使用top命令获取CPU使用率")
                        backup_cmd = "top -bn1 | grep 'Cpu(s)' | awk '{gsub(/%us,|%sy,|%ni,|%id,|%wa,|%hi,|%si,|%st,/, \" \"); printf \"%.2f\", 100-$8}'"
                        backup_result = await ssh_client.execute_command(backup_cmd)
                        if backup_result.get('success') and backup_result['stdout'].strip():
                            raw_value = backup_result['stdout'].strip()
                            value = float(raw_value)
                            metrics[metric_type] = round(value, 2)
                            logger.info(f"备用CPU命令成功: 原始值={raw_value}, 处理后值={value}")
                        else:
                            logger.warning(f"备用CPU命令也失败: {backup_result}")
                            metrics[metric_type] = 0.0
                    else:
                        logger.warning(f"指标 {metric_type} 命令执行失败或无输出: {result}")
                        metrics[metric_type] = 0.0
            except (ValueError, TypeError) as e:
                logger.warning(f"解析指标 {metric_type} 失败: {e}, 原始输出: {result.get('stdout', '')}")
                metrics[metric_type] = 0.0

        return metrics
    
    async def _save_metrics_batch(
        self,
        environment_id: int,
        host_id: int,
        metrics: Dict[str, float]
    ):
        """批量保存指标数据"""
        if not metrics:
            return

        # 构建批量插入数据
        insert_data = []
        current_time = datetime.now()

        for metric_type, value in metrics.items():
            insert_data.append({
                'environment_id': environment_id,
                'host_id': host_id,
                'metric_type': metric_type,
                'metric_value': value,
                'collected_at': current_time
            })

        # 批量插入
        await self.repository.batch_insert_metrics(insert_data)
    
    def _build_chart_data(self, metrics_data: List[ChaosMonitorData]) -> List[ChaosMonitorChartData]:
        """构建图表数据"""
        # 按指标类型分组
        grouped_data = {}
        for metric in metrics_data:
            if metric.metric_type not in grouped_data:
                grouped_data[metric.metric_type] = []
            grouped_data[metric.metric_type].append(metric)
        
        charts = []
        metric_configs = {
            'cpu': {'name': 'CPU使用率', 'unit': '%'},
            'memory': {'name': '内存使用率', 'unit': '%'},
            'network_rx': {'name': '网络接收', 'unit': 'MB'},
            'disk_usage': {'name': '磁盘使用率', 'unit': '%'}
        }
        
        for metric_type, data_list in grouped_data.items():
            if metric_type not in metric_configs:
                continue
            
            config = metric_configs[metric_type]
            values = [float(item.metric_value) for item in data_list]
            timestamps = [item.collected_at.strftime('%H:%M:%S') for item in data_list]
            
            chart_data = ChaosMonitorChartData(
                metric_type=metric_type,
                metric_name=config['name'],
                unit=config['unit'],
                data=values,
                timestamps=timestamps,
                current_value=values[-1] if values else 0.0,
                avg_value=sum(values) / len(values) if values else 0.0,
                max_value=max(values) if values else 0.0,
                min_value=min(values) if values else 0.0
            )
            charts.append(chart_data)
        
        return charts
    
    async def cleanup_old_data(self, hours: int = 24) -> int:
        """清理旧数据"""
        return await self.repository.cleanup_old_data(hours)
