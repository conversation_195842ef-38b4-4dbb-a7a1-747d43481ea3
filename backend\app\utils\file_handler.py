"""
文件上传处理工具模块
"""
import os
import uuid
import shutil
from pathlib import Path
from typing import Optional, List, Tuple
from fastapi import UploadFile, HTTPException

from app.core.config import settings
from app.core.exceptions import BusinessException, ValidationError


class FileHandler:
    """文件上传处理器"""
    
    # 允许的图片格式
    ALLOWED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.webp', '.gif'}
    
    # 文件大小限制 (5MB)
    MAX_FILE_SIZE = 5 * 1024 * 1024
    
    def __init__(self):
        self.upload_dir = Path("uploads")
        self.avatar_dir = self.upload_dir / "avatars"
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保上传目录存在"""
        self.upload_dir.mkdir(exist_ok=True)
        self.avatar_dir.mkdir(exist_ok=True)
    
    def _validate_file(self, file: UploadFile, allowed_extensions: set) -> None:
        """
        验证上传文件
        
        Args:
            file: 上传的文件
            allowed_extensions: 允许的文件扩展名集合
            
        Raises:
            ValidationError: 文件验证失败
        """
        if not file.filename:
            raise ValidationError("文件名不能为空")
        
        # 检查文件扩展名
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in allowed_extensions:
            raise ValidationError(f"不支持的文件格式。支持的格式: {', '.join(allowed_extensions)}")
        
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise ValidationError("只允许上传图片文件")
    
    def _generate_unique_filename(self, original_filename: str) -> str:
        """
        生成唯一的文件名
        
        Args:
            original_filename: 原始文件名
            
        Returns:
            唯一的文件名
        """
        file_ext = Path(original_filename).suffix.lower()
        unique_id = str(uuid.uuid4())
        return f"{unique_id}{file_ext}"
    
    async def save_avatar(self, file: UploadFile) -> str:
        """
        保存头像文件
        
        Args:
            file: 上传的头像文件
            
        Returns:
            文件访问URL
            
        Raises:
            ValidationError: 文件验证失败
            BusinessException: 文件保存失败
        """
        try:
            # 验证文件
            self._validate_file(file, self.ALLOWED_IMAGE_EXTENSIONS)
            
            # 读取文件内容并检查大小
            contents = await file.read()
            if len(contents) > self.MAX_FILE_SIZE:
                raise ValidationError(f"文件大小不能超过 {self.MAX_FILE_SIZE // (1024*1024)}MB")
            
            # 生成唯一文件名
            filename = self._generate_unique_filename(file.filename)
            file_path = self.avatar_dir / filename
            
            # 保存文件
            with open(file_path, "wb") as buffer:
                buffer.write(contents)
            
            # 生成访问URL
            return f"/uploads/avatars/{filename}"
            
        except ValidationError:
            raise
        except Exception as e:
            raise BusinessException(f"文件保存失败: {str(e)}")
    
    async def delete_file(self, file_url: str) -> bool:
        """
        删除文件
        
        Args:
            file_url: 文件URL
            
        Returns:
            是否删除成功
        """
        try:
            if not file_url or not file_url.startswith('/uploads/'):
                return False
            
            # 提取文件路径
            relative_path = file_url.lstrip('/')
            file_path = Path(relative_path)
            
            if file_path.exists():
                file_path.unlink()
                return True
            return False
            
        except Exception:
            return False
    
    def get_file_info(self, file_url: str) -> Optional[dict]:
        """
        获取文件信息
        
        Args:
            file_url: 文件URL
            
        Returns:
            文件信息字典或None
        """
        try:
            if not file_url or not file_url.startswith('/uploads/'):
                return None
            
            relative_path = file_url.lstrip('/')
            file_path = Path(relative_path)
            
            if file_path.exists():
                stat = file_path.stat()
                return {
                    "filename": file_path.name,
                    "size": stat.st_size,
                    "created_at": stat.st_ctime,
                    "modified_at": stat.st_mtime
                }
            return None
            
        except Exception:
            return None


# 全局文件处理器实例
file_handler = FileHandler() 