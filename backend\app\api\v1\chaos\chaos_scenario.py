"""
混沌测试故障场景API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Path, Query, Body

from app.api.deps import get_current_user
from app.core.dependencies import get_chaos_scenario_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_scenario_service import ChaosScenarioService
from app.schemas.base import APIResponse, PaginationResponse
from app.schemas.chaos.chaos_scenario import (
    ChaosScenarioCreate, ChaosScenarioUpdate, ChaosScenarioResponse, ChaosScenarioListResponse,
    ChaosScenarioSearchParams, ChaosScenarioStatistics, ChaosScenarioValidateRequest,
    ChaosScenarioValidateResponse
)

router = APIRouter()


@router.get("/", response_model=PaginationResponse[ChaosScenarioListResponse], summary="获取场景列表")
async def get_scenarios(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    keyword: str = Query(None, description="搜索关键词"),
    fault_type: str = Query(None, description="故障类型"),
    category: str = Query(None, description="场景分类"),
    is_builtin: bool = Query(None, description="是否内置"),
    is_active: bool = Query(None, description="是否启用"),
    tags: List[str] = Query(None, description="标签列表"),
    order_by: str = Query("usage_count", description="排序字段"),
    desc: bool = Query(True, description="是否降序"),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """获取故障场景列表"""
    search_params = ChaosScenarioSearchParams(
        page=page,
        size=size,
        keyword=keyword,
        fault_type=fault_type,
        category=category,
        is_builtin=is_builtin,
        is_active=is_active,
        tags=tags,
        order_by=order_by,
        desc=desc
    )
    
    result = await service.search_scenarios(search_params)
    return response_builder.paginated(
        records=result.records,
        total=result.total,
        current=result.current,
        size=result.size,
        message="获取场景列表成功"
    )


@router.post("/", response_model=APIResponse[ChaosScenarioResponse], summary="创建场景")
async def create_scenario(
    scenario_data: ChaosScenarioCreate,
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """创建故障场景"""
    scenario = await service.create_scenario(scenario_data, current_user.id)
    return response_builder.success(data=scenario, message="创建场景成功")


@router.get("/{scenario_id}", response_model=APIResponse[ChaosScenarioResponse], summary="获取场景详情")
async def get_scenario(
    scenario_id: int = Path(..., description="场景ID"),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """获取场景详情"""
    scenario = await service.get_scenario_by_id(scenario_id)
    return response_builder.success(data=scenario, message="获取场景详情成功")


@router.put("/{scenario_id}", response_model=APIResponse[ChaosScenarioResponse], summary="更新场景")
async def update_scenario(
    scenario_id: int = Path(..., description="场景ID"),
    scenario_data: ChaosScenarioUpdate = Body(...),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """更新故障场景"""
    scenario = await service.update(scenario_id, scenario_data, str(current_user.id))
    return response_builder.success(data=scenario, message="更新场景成功")


@router.delete("/{scenario_id}", response_model=APIResponse[None], summary="删除场景")
async def delete_scenario(
    scenario_id: int = Path(..., description="场景ID"),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """删除故障场景"""
    await service.delete(scenario_id)
    return response_builder.success(message="删除场景成功")


@router.post("/validate", response_model=APIResponse[ChaosScenarioValidateResponse], summary="验证场景参数")
async def validate_scenario_params(
    request: ChaosScenarioValidateRequest,
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """验证场景参数"""
    result = await service.validate_scenario_params(request)
    return response_builder.success(data=result, message="参数验证完成")


@router.post("/{scenario_id}/use", response_model=APIResponse[None], summary="使用场景")
async def use_scenario(
    scenario_id: int = Path(..., description="场景ID"),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """使用场景（增加使用次数）"""
    await service.increment_usage(scenario_id)
    return response_builder.success(message="场景使用次数已更新")




