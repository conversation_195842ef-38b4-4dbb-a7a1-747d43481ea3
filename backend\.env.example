
# 应用配置
APP_NAME=User Management API
VERSION=1.0.0
DEBUG=true

# 数据库配置
DATABASE_URL=mysql+aiomysql://username:password@localhost:3306/user_management
DATABASE_ECHO=false

# JWT 配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 安全配置
BCRYPT_ROUNDS=12

# CORS 配置
ALLOWED_HOSTS=["*"]
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Redis 配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 邮件配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads/

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
