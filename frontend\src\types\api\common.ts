/**
 * 通用API类型定义
 * 作为所有API类型的"单一真实来源"
 */

// ==================== 基础响应类型 ====================

/**
 * 统一API响应格式
 */
export interface APIResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
}

/**
 * 分页数据结构
 */
export interface PaginationData<T = any> {
  records: T[]
  total: number
  current: number
  size: number
}

/**
 * 分页响应格式
 */
export interface PaginationResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: PaginationData<T>
}

// ==================== 请求参数类型 ====================

/**
 * 基础分页参数
 */
export interface PaginationParams {
  current?: number
  size?: number
}

/**
 * 基础搜索参数
 */
export interface SearchParams extends PaginationParams {
  keyword?: string
}

/**
 * 排序参数
 */
export interface SortParams {
  sortField?: string
  sortOrder?: 'ascending' | 'descending'
}

/**
 * 完整的查询参数
 */
export interface QueryParams extends SearchParams, SortParams {
  [key: string]: any
}

// ==================== 操作相关类型 ====================

/**
 * 批量操作请求
 */
export interface BatchRequest {
  ids: number[]
  action: string
}

/**
 * 批量操作响应
 */
export interface BatchResponse {
  success_count: number
  failed_count: number
  failed_items?: Array<{
    id: number
    error: string
  }>
}

// ==================== 状态相关类型 ====================

/**
 * 通用状态类型
 */
export type CommonStatus = 'active' | 'inactive' | 'pending' | 'disabled'

/**
 * 执行状态类型
 */
export type ExecutionStatus = 'pending' | 'running' | 'success' | 'failed' | 'cancelled' | 'paused'

// ==================== 时间相关类型 ====================

/**
 * 时间范围参数
 */
export interface TimeRange {
  start_time?: string
  end_time?: string
}

/**
 * 基础时间戳字段
 */
export interface BaseTimestamps {
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string
}

// ==================== 文件相关类型 ====================

/**
 * 文件上传响应
 */
export interface FileUploadResponse {
  url: string
  filename: string
  content_type: string
  size: number
}

/**
 * 文件信息
 */
export interface FileInfo {
  name: string
  url: string
  size: number
  type: string
  lastModified?: number
}

// ==================== 统计相关类型 ====================

/**
 * 基础统计数据
 */
export interface BaseStatistics {
  total: number
  active: number
  inactive: number
  [key: string]: number
}

/**
 * 趋势数据点
 */
export interface TrendDataPoint {
  date: string
  value: number
  label?: string
}

/**
 * 趋势数据
 */
export interface TrendData {
  period: string
  data: TrendDataPoint[]
}

// ==================== 导出类型别名 ====================

// 为了向后兼容，导出一些常用的类型别名
export type ApiResponse<T> = APIResponse<T>
export type PageData<T> = PaginationData<T>
export type PageResponse<T> = PaginationResponse<T>
export type PageParams = PaginationParams
