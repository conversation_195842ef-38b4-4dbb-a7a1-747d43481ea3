<template>
  <div class="execution-detail" v-loading="loading">
    <div v-if="execution">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <el-button @click="handleBack" :icon="ArrowLeft">返回</el-button>
          <div class="header-info">
            <h2>执行记录详情</h2>
            <div class="header-meta">
              <el-tag :type="getStatusTagType(execution.status)">
                {{ getStatusLabel(execution.status) }}
              </el-tag>
              <span class="meta-item">执行ID：{{ execution.id }}</span>
              <span class="meta-item">创建时间：{{ formatDateTime(execution.created_at) }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <el-button
            v-if="execution.status === 'failed'"
            type="primary"
            @click="handleRetryExecution"
            :loading="retrying"
          >
            重试执行
          </el-button>
          <el-button
            v-if="execution.is_running"
            type="warning"
            @click="handleCancelExecution"
          >
            取消执行
          </el-button>
          <el-dropdown @command="handleDropdownCommand">
            <el-button>
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="export">导出详情</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除记录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 执行信息卡片 -->
      <div class="content-section">
        <el-row :gutter="20">
          <!-- 基本信息 -->
          <el-col :span="12">
            <el-card title="基本信息" class="info-card">
              <template #header>
                <span>基本信息</span>
              </template>
              <div class="info-list">
                <div class="info-item">
                  <span class="label">执行ID：</span>
                  <span class="value">{{ execution.id }}</span>
                </div>
                <div class="info-item">
                  <span class="label">关联任务：</span>
                  <el-link
                    v-if="execution.task_name"
                    type="primary"
                    @click="handleViewTask"
                  >
                    {{ execution.task_name }}
                  </el-link>
                  <span v-else class="value">Task-{{ execution.task_id }}</span>
                </div>
                <div class="info-item">
                  <span class="label">执行主机：</span>
                  <span class="value">{{ execution.host_name || `Host-${execution.host_id}` }}</span>
                </div>
                <div class="info-item">
                  <span class="label">执行状态：</span>
                  <el-tag :type="getStatusTagType(execution.status)">
                    {{ getStatusLabel(execution.status) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">ChaosBlade UID：</span>
                  <span v-if="execution.chaos_uid" class="value uid-text">{{ execution.chaos_uid }}</span>
                  <span v-else class="text-muted">-</span>
                </div>
                <div class="info-item">
                  <span class="label">ChaosBlade版本：</span>
                  <span class="value">{{ execution.blade_version || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">重试次数：</span>
                  <el-tag v-if="execution.retry_count > 0" size="small" type="warning">
                    {{ execution.retry_count }}
                  </el-tag>
                  <span v-else>0</span>
                </div>
                <div class="info-item">
                  <span class="label">退出码：</span>
                  <el-tag
                    v-if="execution.exit_code !== null && execution.exit_code !== undefined"
                    size="small"
                    :type="execution.exit_code === 0 ? 'success' : 'danger'"
                  >
                    {{ execution.exit_code }}
                  </el-tag>
                  <span v-else class="text-muted">-</span>
                </div>
                <div class="info-item">
                  <span class="label">自动销毁：</span>
                  <el-tag :type="execution.is_auto_destroyed ? 'success' : 'info'" size="small">
                    {{ execution.is_auto_destroyed ? '已销毁' : '未销毁' }}
                  </el-tag>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 时间信息 -->
          <el-col :span="12">
            <el-card title="时间信息" class="info-card">
              <template #header>
                <span>时间信息</span>
              </template>
              <div class="info-list">
                <div class="info-item">
                  <span class="label">创建时间：</span>
                  <span class="value">{{ formatDateTime(execution.created_at) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">开始时间：</span>
                  <span class="value">{{ formatDateTime(execution.start_time) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">结束时间：</span>
                  <span class="value">{{ formatDateTime(execution.end_time) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">执行时长：</span>
                  <span v-if="execution.duration_seconds" class="value">
                    {{ formatDuration(execution.duration_seconds) }}
                  </span>
                  <span v-else class="text-muted">-</span>
                </div>
                <div class="info-item">
                  <span class="label">销毁时间：</span>
                  <span class="value">{{ formatDateTime(execution.destroy_time) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">创建者：</span>
                  <span class="value">{{ execution.created_by || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">更新者：</span>
                  <span class="value">{{ execution.updated_by || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">最后更新：</span>
                  <span class="value">{{ formatDateTime(execution.updated_at) }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 主机信息 -->
        <el-card v-if="execution.host_info" title="主机信息" class="host-card">
          <template #header>
            <span>主机信息</span>
          </template>
          <div class="host-info">
            <pre>{{ JSON.stringify(execution.host_info, null, 2) }}</pre>
          </div>
        </el-card>

        <!-- 故障配置 -->
        <el-card v-if="execution.fault_config" title="故障配置" class="config-card">
          <template #header>
            <span>故障配置</span>
          </template>
          <div class="fault-config">
            <pre>{{ JSON.stringify(execution.fault_config, null, 2) }}</pre>
          </div>
        </el-card>

        <!-- 执行日志 -->
        <el-card title="执行日志" class="log-card">
          <template #header>
            <div class="card-header">
              <span>执行日志</span>
              <el-button size="small" @click="refreshLogs">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <el-tabs v-model="activeLogTab" @tab-change="handleLogTabChange">
            <el-tab-pane label="执行输出" name="output">
              <div class="log-content">
                <el-input
                  v-model="logs.output"
                  type="textarea"
                  :rows="12"
                  readonly
                  placeholder="暂无执行输出"
                />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="错误信息" name="error">
              <div class="log-content">
                <el-input
                  v-model="logs.error"
                  type="textarea"
                  :rows="12"
                  readonly
                  placeholder="暂无错误信息"
                />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="执行命令" name="command">
              <div class="log-content">
                <el-input
                  v-model="logs.command"
                  type="textarea"
                  :rows="12"
                  readonly
                  placeholder="暂无执行命令"
                />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="销毁输出" name="destroy">
              <div class="log-content">
                <el-input
                  v-model="logs.destroy"
                  type="textarea"
                  :rows="12"
                  readonly
                  placeholder="暂无销毁输出"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
          
          <div class="log-actions">
            <el-button @click="handleCopyLog">复制当前日志</el-button>
            <el-button @click="handleDownloadLog">下载日志文件</el-button>
          </div>
        </el-card>

        <!-- 实时监控 -->
        <el-card v-if="execution.is_running" title="实时监控" class="monitor-card">
          <template #header>
            <div class="card-header">
              <span>实时监控</span>
              <el-button size="small" @click="refreshMonitorData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="monitor-content" v-if="monitorData">
            <div class="monitor-progress">
              <div class="progress-info">
                <span class="progress-label">执行进度</span>
                <span class="progress-value">{{ monitorData.progress.toFixed(1) }}%</span>
              </div>
              <el-progress
                :percentage="monitorData.progress"
                :status="getProgressStatus(monitorData.status)"
              />
            </div>
            
            <div class="monitor-details">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <span class="label">当前步骤：</span>
                    <span class="value">{{ monitorData.current_step }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">已执行时长：</span>
                    <span class="value">{{ formatDuration(monitorData.elapsed_seconds) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item" v-if="monitorData.estimated_remaining_seconds">
                    <span class="label">预计剩余：</span>
                    <span class="value">{{ formatDuration(monitorData.estimated_remaining_seconds) }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">最后更新：</span>
                    <span class="value">{{ formatDateTime(monitorData.last_update_time) }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowDown, Refresh } from '@element-plus/icons-vue'
import { useChaosExecutionsStore } from '@/store/business/chaos/executions'
import type { ChaosExecution } from '@/types/api/chaos'

const router = useRouter()
const route = useRoute()
const chaosExecutionsStore = useChaosExecutionsStore()

// 响应式数据
const loading = ref(false)
const retrying = ref(false)
const execution = ref<ChaosExecution | null>(null)
const activeLogTab = ref('output')
const monitorData = ref<any>(null)

// 日志数据
const logs = reactive<Record<string, string>>({
  output: '',
  error: '',
  command: '',
  destroy: ''
})

// 计算属性
const executionId = computed(() => Number(route.params.id))

// 生命周期
onMounted(() => {
  loadExecutionDetail()
  loadAllLogs()
  
  // 如果是运行中状态，加载监控数据
  if (execution.value?.is_running) {
    loadMonitorData()
  }
})

// 方法
const loadExecutionDetail = async () => {
  loading.value = true
  try {
    execution.value = await chaosExecutionsStore.fetchExecution(executionId.value)
  } catch (error) {
    ElMessage.error('加载执行记录详情失败')
    router.push('/chaos/executions')
  } finally {
    loading.value = false
  }
}

const loadAllLogs = async () => {
  const logTypes = ['output', 'error', 'command', 'destroy']
  
  for (const logType of logTypes) {
    try {
      const response = await chaosExecutionsStore.getExecutionLog(executionId.value, logType)
      logs[logType] = response.content || ''
    } catch (error) {
      logs[logType] = ''
    }
  }
}

const loadMonitorData = async () => {
  try {
    monitorData.value = await chaosExecutionsStore.getExecutionMonitor(executionId.value)
  } catch (error) {
    console.error('加载监控数据失败:', error)
  }
}

const refreshLogs = () => {
  loadAllLogs()
}

const refreshMonitorData = () => {
  loadMonitorData()
}

const handleBack = () => {
  router.push('/chaos/executions')
}

const handleViewTask = () => {
  if (execution.value) {
    router.push(`/chaos/tasks/${execution.value.task_id}`)
  }
}

const handleRetryExecution = async () => {
  retrying.value = true
  try {
    await chaosExecutionsStore.retryExecution(executionId.value, {})
    ElMessage.success('重试执行成功')
    await loadExecutionDetail()
    await loadAllLogs()
  } catch (error) {
    ElMessage.error('重试执行失败')
  } finally {
    retrying.value = false
  }
}

const handleCancelExecution = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消执行吗？正在运行的故障注入将被停止。',
      '确认取消',
      { type: 'warning' }
    )
    
    await chaosExecutionsStore.cancelExecution(executionId.value)
    ElMessage.success('执行已取消')
    await loadExecutionDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消执行失败')
    }
  }
}

const handleDropdownCommand = (command: string) => {
  switch (command) {
    case 'export':
      handleExportExecution()
      break
    case 'delete':
      handleDeleteExecution()
      break
  }
}

const handleExportExecution = () => {
  if (!execution.value) return
  
  const exportData = {
    ...execution.value,
    logs: logs
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chaos-execution-${execution.value.id}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('执行记录已导出')
}

const handleDeleteExecution = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除执行记录 ${executionId.value} 吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )
    
    await chaosExecutionsStore.deleteExecution(executionId.value)
    ElMessage.success('执行记录删除成功')
    router.push('/chaos/executions')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除执行记录失败')
    }
  }
}

const handleLogTabChange = (tabName: string | number) => {
  activeLogTab.value = String(tabName)
}

const handleCopyLog = () => {
  const currentLog = logs[activeLogTab.value]
  if (currentLog) {
    navigator.clipboard.writeText(currentLog)
    ElMessage.success('日志已复制到剪贴板')
  } else {
    ElMessage.warning('当前日志为空')
  }
}

const handleDownloadLog = () => {
  const currentLog = logs[activeLogTab.value]
  if (!currentLog) {
    ElMessage.warning('当前日志为空')
    return
  }
  
  const blob = new Blob([currentLog], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `execution-${executionId.value}-${activeLogTab.value}.log`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('日志文件已下载')
}

// 工具方法
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    running: '运行中',
    success: '成功',
    failed: '失败',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' | undefined => {
  const types: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    pending: 'info',
    running: 'primary',
    success: 'success',
    failed: 'danger'
  }
  return types[status]
}

const getProgressStatus = (status: string) => {
  if (status === 'success') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours}时${minutes}分${secs}秒`
}
</script>

<style scoped>
.execution-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.meta-item {
  display: flex;
  align-items: center;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  height: 100%;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.info-item .label {
  width: 120px;
  color: #666;
  flex-shrink: 0;
}

.info-item .value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.text-muted {
  color: #999;
}

.uid-text {
  font-family: monospace;
  font-size: 12px;
}

.host-card,
.config-card,
.log-card,
.monitor-card {
  margin-top: 20px;
}

.host-info,
.fault-config {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 15px;
}

.host-info pre,
.fault-config pre {
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.log-content {
  margin-bottom: 15px;
}

.log-actions {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.monitor-content {
  padding: 10px 0;
}

.monitor-progress {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-label {
  font-weight: 500;
  color: #333;
}

.progress-value {
  font-weight: 600;
  color: #409eff;
}

.monitor-details {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.detail-item .label {
  width: 100px;
  color: #666;
  flex-shrink: 0;
}

.detail-item .value {
  color: #333;
  flex: 1;
}
</style>
