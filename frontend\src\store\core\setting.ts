/**
 * 系统设置核心状态管理
 * 迁移自 src/store/modules/setting.ts
 * 管理应用的菜单、主题、界面显示等各项设置
 */
import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import { MenuThemeType } from '@/types/store'
import AppConfig from '@/config'
import { SystemThemeEnum, MenuThemeEnum, MenuTypeEnum, ContainerWidthEnum } from '@/enums/appEnum'
import { setElementThemeColor } from '@/utils/ui'
import { useCeremony } from '@/composables/useCeremony'

const { defaultMenuWidth, defaultCustomRadius, defaultTabStyle } = AppConfig.systemSetting

export const useSettingStore = defineStore(
  'setting',
  () => {
    // ==================== 菜单相关设置 ====================
    
    /** 菜单类型 */
    const menuType = ref(MenuTypeEnum.LEFT)
    /** 菜单展开宽度 */
    const menuOpenWidth = ref(defaultMenuWidth)
    /** 菜单是否展开 */
    const menuOpen = ref(true)
    /** 双菜单是否显示文本 */
    const dualMenuShowText = ref(false)

    // ==================== 主题相关设置 ====================
    
    /** 系统主题类型 */
    const systemThemeType = ref(SystemThemeEnum.AUTO)
    /** 系统主题模式 */
    const systemThemeMode = ref(SystemThemeEnum.AUTO)
    /** 菜单主题类型 */
    const menuThemeType = ref(MenuThemeEnum.DESIGN)
    /** 系统主题颜色 */
    const systemThemeColor = ref(AppConfig.elementPlusTheme.primary)

    // ==================== 界面显示设置 ====================
    
    /** 是否显示菜单按钮 */
    const showMenuButton = ref(true)
    /** 是否显示快速入口 */
    const showFastEnter = ref(true)
    /** 是否显示刷新按钮 */
    const showRefreshButton = ref(true)
    /** 是否显示面包屑 */
    const showCrumbs = ref(true)
    /** 是否显示工作台标签 */
    const showWorkTab = ref(true)
    /** 是否显示语言切换 */
    const showLanguage = ref(true)
    /** 是否显示全屏按钮 */
    const showFullScreen = ref(true)
    /** 是否显示主题切换 */
    const showThemeSwitch = ref(true)
    /** 是否显示设置按钮 */
    const showSettingButton = ref(true)

    // ==================== 布局设置 ====================
    
    /** 容器宽度类型 */
    const containerWidthType = ref(ContainerWidthEnum.FULL)
    /** 自定义容器宽度 */
    const customContainerWidth = ref(1200)
    /** 自定义圆角大小 */
    const customRadius = ref(defaultCustomRadius)
    /** 标签页样式 */
    const tabStyle = ref(defaultTabStyle)

    // ==================== 动画设置 ====================
    
    /** 是否开启页面切换动画 */
    const enablePageTransition = ref(true)
    /** 页面切换动画类型 */
    const pageTransitionType = ref('fade-slide')
    /** 是否开启菜单动画 */
    const enableMenuTransition = ref(true)

    // ==================== 计算属性 ====================
    
    /** 当前菜单主题配置 */
    const currentMenuTheme = computed((): MenuThemeType => {
      const themeMap = {
        [MenuThemeEnum.DESIGN]: 'design',
        [MenuThemeEnum.LIGHT]: 'light',
        [MenuThemeEnum.DARK]: 'dark'
      }
      return themeMap[menuThemeType.value] as MenuThemeType
    })

    /** 是否为暗色主题 */
    const isDarkTheme = computed(() => {
      return systemThemeMode.value === SystemThemeEnum.DARK
    })

    /** 是否为左侧菜单 */
    const isLeftMenu = computed(() => {
      return menuType.value === MenuTypeEnum.LEFT
    })

    /** 是否为顶部菜单 */
    const isTopMenu = computed(() => {
      return menuType.value === MenuTypeEnum.TOP
    })

    /** 是否为双菜单 */
    const isDualMenu = computed(() => {
      return menuType.value === MenuTypeEnum.DUAL
    })

    // ==================== 方法 ====================
    
    /**
     * 切换菜单展开状态
     */
    const toggleMenuOpen = () => {
      menuOpen.value = !menuOpen.value
    }

    /**
     * 设置菜单类型
     */
    const setMenuType = (type: MenuTypeEnum) => {
      menuType.value = type
    }

    /**
     * 设置系统主题
     */
    const setSystemTheme = async (theme: SystemThemeEnum) => {
      systemThemeType.value = theme
      systemThemeMode.value = theme
      
      // 应用主题
      await nextTick()
      document.documentElement.setAttribute('data-theme', theme)
    }

    /**
     * 设置主题颜色
     */
    const setThemeColor = async (color: string) => {
      systemThemeColor.value = color
      await setElementThemeColor(color)
    }

    /**
     * 设置菜单主题
     */
    const setMenuTheme = (theme: MenuThemeEnum) => {
      menuThemeType.value = theme
    }

    /**
     * 重置所有设置
     */
    const resetSettings = () => {
      // 菜单设置
      menuType.value = MenuTypeEnum.LEFT
      menuOpenWidth.value = defaultMenuWidth
      menuOpen.value = true
      dualMenuShowText.value = false

      // 主题设置
      systemThemeType.value = SystemThemeEnum.AUTO
      systemThemeMode.value = SystemThemeEnum.AUTO
      menuThemeType.value = MenuThemeEnum.DESIGN
      systemThemeColor.value = AppConfig.elementPlusTheme.primary

      // 界面显示设置
      showMenuButton.value = true
      showFastEnter.value = true
      showRefreshButton.value = true
      showCrumbs.value = true
      showWorkTab.value = true
      showLanguage.value = true
      showFullScreen.value = true
      showThemeSwitch.value = true
      showSettingButton.value = true

      // 布局设置
      containerWidthType.value = ContainerWidthEnum.FULL
      customContainerWidth.value = 1200
      customRadius.value = defaultCustomRadius
      tabStyle.value = defaultTabStyle

      // 动画设置
      enablePageTransition.value = true
      pageTransitionType.value = 'fade-slide'
      enableMenuTransition.value = true
    }

    /**
     * 导出设置配置
     */
    const exportSettings = () => {
      return {
        menuType: menuType.value,
        menuOpenWidth: menuOpenWidth.value,
        menuOpen: menuOpen.value,
        dualMenuShowText: dualMenuShowText.value,
        systemThemeType: systemThemeType.value,
        systemThemeMode: systemThemeMode.value,
        menuThemeType: menuThemeType.value,
        systemThemeColor: systemThemeColor.value,
        showMenuButton: showMenuButton.value,
        showFastEnter: showFastEnter.value,
        showRefreshButton: showRefreshButton.value,
        showCrumbs: showCrumbs.value,
        showWorkTab: showWorkTab.value,
        showLanguage: showLanguage.value,
        showFullScreen: showFullScreen.value,
        showThemeSwitch: showThemeSwitch.value,
        showSettingButton: showSettingButton.value,
        containerWidthType: containerWidthType.value,
        customContainerWidth: customContainerWidth.value,
        customRadius: customRadius.value,
        tabStyle: tabStyle.value,
        enablePageTransition: enablePageTransition.value,
        pageTransitionType: pageTransitionType.value,
        enableMenuTransition: enableMenuTransition.value
      }
    }

    /**
     * 导入设置配置
     */
    const importSettings = (settings: any) => {
      Object.keys(settings).forEach(key => {
        if (key in settings) {
          // 这里需要根据具体的ref变量进行赋值
          // 为了简化，这里只是示例
          console.log(`Importing setting: ${key} = ${settings[key]}`)
        }
      })
    }

    return {
      // 菜单相关
      menuType,
      menuOpenWidth,
      menuOpen,
      dualMenuShowText,
      
      // 主题相关
      systemThemeType,
      systemThemeMode,
      menuThemeType,
      systemThemeColor,
      
      // 界面显示
      showMenuButton,
      showFastEnter,
      showRefreshButton,
      showCrumbs,
      showWorkTab,
      showLanguage,
      showFullScreen,
      showThemeSwitch,
      showSettingButton,
      
      // 布局设置
      containerWidthType,
      customContainerWidth,
      customRadius,
      tabStyle,
      
      // 动画设置
      enablePageTransition,
      pageTransitionType,
      enableMenuTransition,
      
      // 计算属性
      currentMenuTheme,
      isDarkTheme,
      isLeftMenu,
      isTopMenu,
      isDualMenu,
      
      // 方法
      toggleMenuOpen,
      setMenuType,
      setSystemTheme,
      setThemeColor,
      setMenuTheme,
      resetSettings,
      exportSettings,
      importSettings
    }
  },
  {
    persist: {
      key: 'app-setting',
      storage: localStorage
    }
  }
)
