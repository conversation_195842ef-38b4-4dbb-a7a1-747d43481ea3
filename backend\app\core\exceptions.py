"""
统一异常处理系统
定义业务异常和异常处理器
"""
from typing import Any, Dict
from fastapi import HTTPException, status


class BusinessException(Exception):
    """
    业务异常基类
    用于业务逻辑层抛出的异常
    """
    
    def __init__(self, message: str, code: int = 400, data: Any = None):
        self.message = message
        self.code = code
        self.data = data
        super().__init__(message)


class ValidationError(BusinessException):
    """数据验证异常"""
    def __init__(self, message: str = "数据验证失败", data: Any = None):
        super().__init__(message, 400, data)


class NotFoundError(BusinessException):
    """资源不存在异常"""
    def __init__(self, message: str = "资源不存在", data: Any = None):
        super().__init__(message, 404, data)


class PermissionError(BusinessException):
    """权限不足异常"""
    def __init__(self, message: str = "权限不足", data: Any = None):
        super().__init__(message, 403, data)


class ConflictError(BusinessException):
    """资源冲突异常"""
    def __init__(self, message: str = "资源冲突", data: Any = None):
        super().__init__(message, 409, data)


class UnauthorizedError(BusinessException):
    """认证失败异常"""
    def __init__(self, message: str = "认证失败", data: Any = None):
        super().__init__(message, 401, data)


class ExceptionHandler:
    """
    异常处理器
    将业务异常转换为HTTP异常
    """
    
    @staticmethod
    def to_http_exception(exc: BusinessException) -> HTTPException:
        """
        将业务异常转换为HTTP异常
        
        Args:
            exc: 业务异常
            
        Returns:
            HTTP异常
        """
        status_map = {
            400: status.HTTP_400_BAD_REQUEST,
            401: status.HTTP_401_UNAUTHORIZED, 
            403: status.HTTP_403_FORBIDDEN,
            404: status.HTTP_404_NOT_FOUND,
            409: status.HTTP_409_CONFLICT,
        }
        
        return HTTPException(
            status_code=status_map.get(exc.code, status.HTTP_400_BAD_REQUEST),
            detail={
                "message": exc.message,
                "code": exc.code,
                "data": exc.data
            }
        )
    
    @staticmethod
    def create_exception(
        exc_type: type,
        message: str,
        data: Any = None
    ) -> BusinessException:
        """
        创建业务异常
        
        Args:
            exc_type: 异常类型
            message: 异常消息
            data: 异常数据
            
        Returns:
            业务异常实例
        """
        return exc_type(message, data)


# 全局异常处理器实例
exception_handler = ExceptionHandler()


# 快捷异常创建函数
def raise_not_found(message: str = "资源不存在", data: Any = None) -> None:
    """抛出资源不存在异常"""
    raise NotFoundError(message, data)


def raise_validation_error(message: str = "数据验证失败", data: Any = None) -> None:
    """抛出数据验证异常"""
    raise ValidationError(message, data)


def raise_permission_error(message: str = "权限不足", data: Any = None) -> None:
    """抛出权限不足异常"""
    raise PermissionError(message, data)


def raise_conflict_error(message: str = "资源冲突", data: Any = None) -> None:
    """抛出资源冲突异常"""
    raise ConflictError(message, data)


def raise_unauthorized_error(message: str = "认证失败", data: Any = None) -> None:
    """抛出认证失败异常"""
    raise UnauthorizedError(message, data) 