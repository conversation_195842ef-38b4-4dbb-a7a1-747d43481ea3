---
type: "always_apply"
---

# DpTestPlatform 后端开发规范

## 项目概述

**项目名称**: DpTestPlatform Backend  
**技术栈**: FastAPI + SQLAlchemy + Pydantic V2 + Alembic  
**架构模式**: 分层架构 + 依赖注入  
**数据库**: Mysql 
**认证方式**: JWT Bearer Token  

## 1. 技术架构规范

### 1.1 核心技术栈

#### Web框架
- **FastAPI 0.104.1**: 现代化异步Web框架，自动API文档生成
- **Uvicorn**: ASGI服务器，支持异步请求处理
- **Pydantic V2**: 数据验证和序列化，类型安全

#### 数据层
- **SQLAlchemy 2.0**: 现代异步ORM，声明式模型定义
- **Alembic**: 数据库迁移管理
- **asyncpg**: PostgreSQL异步驱动
- **aiosqlite**: SQLite异步驱动

#### 认证授权
- **Python-Jose**: JWT令牌处理
- **Passlib**: 密码哈希和验证
- **Python-Multipart**: 文件上传支持

### 1.2 架构设计原则

#### 分层架构模式
```
API层 (api/)          ←→ HTTP请求和响应，参数验证，路由定义
    ↓
业务逻辑层 (services/) ←→ 核心业务逻辑，业务规则实现  
    ↓
数据访问层 (repositories/) ←→ 数据访问抽象，查询逻辑封装
    ↓
数据模型层 (models/)    ←→ 数据库实体定义，关系映射
```

#### 依赖注入
- **认证依赖**: 自动JWT令牌验证和用户身份获取
- **数据库会话**: 自动数据库连接生命周期管理
- **权限检查**: 基于角色的访问控制
- **服务注入**: 自动服务层实例创建和注入

## 2. 项目结构规范

### 2.1 目录结构标准

```
backend/
├── app/                     # 应用核心代码
│   ├── main.py             # 应用主入口，生命周期管理
│   ├── api/                # API路由层
│   │   ├── deps.py         # 依赖注入定义
│   │   ├── router.py       # 路由管理器
│   │   └── v1/             # API版本1
│   │       ├── user/       # 用户相关API
│   │       └── env/        # 环境相关API
│   ├── core/               # 核心功能模块
│   │   ├── config.py       # 配置管理
│   │   ├── database.py     # 数据库连接
│   │   ├── security.py     # 安全工具
│   │   ├── exceptions.py   # 异常定义
│   │   └── responses.py    # 统一响应处理
│   ├── database/           # 数据库模块
│   │   ├── base.py         # 数据库基础配置
│   │   ├── connection.py   # 连接管理
│   │   └── session.py      # 会话管理
│   ├── models/             # 数据模型层
│   │   ├── base.py         # 基础模型类
│   │   ├── user/           # 用户相关模型
│   │   └── env/            # 环境相关模型
│   ├── repositories/       # 数据访问层
│   │   ├── base.py         # 基础仓库类
│   │   ├── user/           # 用户相关仓库
│   │   └── env/            # 环境相关仓库
│   ├── schemas/            # 数据传输对象
│   │   ├── base.py         # 基础Schema和响应格式
│   │   ├── common.py       # 通用Schema
│   │   ├── user/           # 用户相关Schema
│   │   └── env/            # 环境相关Schema
│   ├── services/           # 业务逻辑层
│   │   ├── user/           # 用户相关服务
│   │   └── env/            # 环境相关服务
│   ├── middleware/         # 中间件
│   │   ├── performance.py  # 性能监控中间件
│   │   └── request_logging.py # 请求日志中间件
│   └── utils/              # 工具函数
│       ├── clients/        # 外部客户端
│       ├── converters.py   # 数据转换工具
│       ├── file_handler.py # 文件处理工具
│       ├── logger.py       # 日志配置
│       └── validators.py   # 验证工具
├── alembic/                # 数据库迁移
├── tests/                  # 测试文件
├── uploads/                # 文件上传目录
├── .env                    # 环境变量配置
├── requirements.txt        # Python依赖
└── run.py                  # 生产环境启动脚本
```

### 2.2 文件命名规范

#### 基本规则
- **文件名**: 使用下划线命名法 (snake_case)
- **类名**: 使用驼峰命名法 (PascalCase)
- **函数名**: 使用下划线命名法 (snake_case)
- **常量**: 使用大写字母+下划线 (UPPER_SNAKE_CASE)

#### 具体示例
```python
# 文件名：user_service.py
class UserService:          # 类名：PascalCase
    async def get_user_info(self):  # 方法名：snake_case
        pass

# 常量定义
DEFAULT_PAGE_SIZE = 20      # 常量：UPPER_SNAKE_CASE
```

## 3. 分层架构实现规范

### 3.1 模型层 (Models) 规范

#### 基础模型定义
```python
"""
基础数据模型类
包含所有模型的通用字段和方法
"""
from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.ext.declarative import declared_attr
from app.database.base import Base

class BaseModel(Base):
    """基础模型类，包含通用字段"""
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建者")
    updated_by = Column(String(50), nullable=True, comment="更新者")

    @declared_attr
    def __tablename__(cls) -> str:
        """自动生成表名（类名转换为下划线命名）"""
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
```

#### 模型设计原则
- **继承BaseModel**: 所有业务模型必须继承BaseModel
- **字段注释**: 每个字段必须添加comment说明
- **关系定义**: 使用SQLAlchemy关系映射，明确back_populates
- **索引设计**: 合理设置索引，提高查询性能

### 3.2 数据访问层 (Repository) 规范

#### 基础仓库类
```python
"""基础仓储类，专注于数据访问抽象"""
from typing import Any, Generic, List, Optional, Type, TypeVar, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel
from app.database.base import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)

class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """基础仓储类，提供通用的数据库操作方法"""

    def __init__(self, model: Type[ModelType], db: AsyncSession):
        self.model = model
        self.db = db

    async def get(self, id: Any) -> Optional[ModelType]:
        """根据ID获取单个对象"""
        result = await self.db.execute(
            select(self.model).where(self.model.id == id)
        )
        return result.scalar_one_or_none()

    async def create(self, *, obj_in: CreateSchemaType) -> ModelType:
        """创建新对象"""
        obj_data = obj_in.model_dump()
        db_obj = self.model(**obj_data)
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)
        return db_obj
```

#### 仓库层设计原则
- **职责单一**: 仅负责数据访问，不包含业务逻辑
- **泛型支持**: 使用泛型确保类型安全
- **异步优先**: 所有数据库操作使用异步方法
- **异常透明**: 让数据库异常向上传播，由业务层处理

### 3.3 业务逻辑层 (Service) 规范

#### BaseService 核心架构
**重要**: 所有业务服务必须继承 `BaseService` 基类，充分利用通用 CRUD 操作，避免代码重复。

```python
"""BaseService 使用示例"""
from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.base import BaseService
from app.models.user.user import User
from app.repositories.user.user import UserRepository
from app.schemas.user.user import UserCreate, UserUpdate, UserResponse
from app.core.security import get_password_hash
from app.core.exceptions import raise_validation_error

class UserService(BaseService[User, UserCreate, UserUpdate, UserResponse]):
    """用户业务服务 - 继承BaseService"""

    def __init__(self, db: AsyncSession):
        super().__init__(UserRepository(db))

    # ==================== 钩子方法实现特殊业务逻辑 ====================

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前处理 - 密码哈希和业务校验"""
        # 业务校验：检查用户名重复
        if 'username' in create_dict:
            existing = await self.repository.get_by_username(create_dict['username'])
            if existing:
                raise_validation_error(f"用户名 '{create_dict['username']}' 已存在")

        # 处理密码哈希
        if 'password' in create_dict:
            password = create_dict.pop('password')
            create_dict['hashed_password'] = get_password_hash(password)

        return create_dict

    # ==================== 业务方法使用基类通用方法 ====================

    async def create_user(self, user_data: UserCreate, current_user_id: int) -> UserResponse:
        """创建用户 - 使用基类通用方法"""
        return await self.create(user_data, str(current_user_id))

    async def get_user_by_id(self, user_id: int) -> UserResponse:
        """获取用户 - 使用基类通用方法"""
        return await self.get_by_id(user_id)
```

#### BaseService 钩子方法系统
BaseService 提供了完整的钩子方法系统，子类可以重写这些方法来实现特殊业务逻辑：

```python
# 创建相关钩子
async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
    """创建前数据处理（如：密码哈希、API Key加密）"""

async def _validate_before_create(self, create_data: CreateSchemaType, **kwargs) -> None:
    """创建前业务验证（如：重复性检查）"""

async def _process_after_create(self, obj: ModelType) -> None:
    """创建后处理（如：发送通知、日志记录）"""

# 更新相关钩子
async def _process_before_update(self, obj: ModelType, update_dict: Dict[str, Any]) -> Dict[str, Any]:
    """更新前数据处理"""

# 响应转换钩子
def _convert_to_response(self, obj: ModelType) -> ResponseSchemaType:
    """自定义响应转换（如：包含用户昵称信息）"""
```

#### 服务层设计原则
- **继承BaseService**: 所有业务服务必须继承BaseService基类
- **使用钩子方法**: 通过钩子方法实现特殊业务逻辑，不要重写基类的核心CRUD方法
- **避免代码重复**: 优先使用基类的通用方法，如 create()、get_by_id()、update()、delete()
- **业务逻辑集中**: 复杂业务逻辑在钩子方法中实现
- **异常处理统一**: 使用基类提供的统一异常处理机制

### 3.4 API层 (API) 规范

#### API路由设计
```python
"""用户管理API路由"""
from fastapi import APIRouter, Depends, Path, Query
from app.api.deps import get_current_user, get_current_superuser
from app.core.service_deps import UserServiceType
from app.core.responses import response_builder
from app.schemas.base import APIResponse, PaginationResponse

router = APIRouter()

@router.get("/info", response_model=APIResponse[UserResponse], summary="获取当前用户信息")
async def get_user_info(
    service: UserServiceType,
    current_user: User = Depends(get_current_user)
):
    """获取当前用户信息"""
    user_info = await service.get_current_user_info(current_user)
    return response_builder.success(data=user_info, message="获取用户信息成功")

@router.post("/", response_model=APIResponse[UserResponse], summary="创建用户")
async def create_user(
    user_data: UserCreate,
    service: UserServiceType,
    current_user: User = Depends(get_current_superuser)
):
    """创建用户"""
    new_user = await service.create_user(user_data, current_user.id)
    return response_builder.success(data=new_user, message="创建用户成功")
```

#### API层设计原则
- **路径规范**: 使用RESTful API设计规范
- **响应统一**: 使用response_builder构建统一响应格式
- **依赖注入**: 通过Depends进行认证和服务注入
- **文档完善**: 每个接口必须包含summary和docstring

## 4. 统一响应格式规范

### 4.1 响应模型定义

```python
"""API 统一响应格式"""
from typing import Any, Generic, List, Optional, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')

class APIResponse(BaseModel, Generic[T]):
    """API 统一响应格式"""
    success: bool = Field(default=True, description="是否成功")
    code: int = Field(default=200, description="状态码")
    message: str = Field(default="操作成功", description="响应消息")
    data: Optional[T] = Field(default=None, description="响应数据")

class PaginationResponse(BaseModel, Generic[T]):
    """分页响应模式"""
    success: bool = Field(default=True, description="是否成功")
    code: int = Field(default=200, description="状态码")  
    message: str = Field(default="查询成功", description="响应消息")
    data: PaginationData[T] = Field(description="分页数据")
```

### 4.2 响应构建器

```python
"""统一响应处理系统"""
class ResponseBuilder:
    """响应构建器 - 返回Pydantic模型实例"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功", code: int = 200) -> APIResponse[Any]:
        return APIResponse(success=True, code=code, message=message, data=data)
    
    @staticmethod
    def error(message: str = "操作失败", code: int = 400, data: Any = None) -> APIResponse[Any]:
        return APIResponse(success=False, code=code, message=message, data=data)
    
    @staticmethod
    def paginated(records: List[Any], total: int, current: int, size: int, 
                  message: str = "查询成功") -> PaginationResponse[Any]:
        return PaginationResponse(
            success=True, code=200, message=message,
            data=PaginationData(records=records, total=total, current=current, size=size)
        )
```

## 5. 异常处理规范

### 5.1 业务异常定义

```python
"""业务异常基类"""
class BusinessException(Exception):
    """业务异常基类"""
    def __init__(self, message: str, code: int = 400, data: Any = None):
        self.message = message
        self.code = code
        self.data = data

class ValidationError(BusinessException):
    """数据验证异常"""
    def __init__(self, message: str = "数据验证失败", data: Any = None):
        super().__init__(message, 400, data)

# 便捷的异常抛出函数
def raise_validation_error(message: str, data: Any = None):
    """抛出验证异常"""
    raise ValidationError(message, data)

def raise_not_found(message: str = "资源不存在"):
    """抛出资源不存在异常"""
    raise BusinessException(message, 404)
```

### 5.2 全局异常处理

```python
"""全局异常处理器"""
@app.exception_handler(BusinessException)
async def business_exception_handler(request: Request, exc: BusinessException):
    """业务异常全局处理器"""
    logger.warning(f"业务异常: {exc.message}")
    response = response_builder.error(
        message=exc.message,
        code=exc.code,
        data=exc.data
    )
    return JSONResponse(status_code=exc.code, content=response.model_dump())

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    
    if settings.debug:
        response = response_builder.error(
            message=f"服务器内部错误: {str(exc)}",
            code=500
        )
    else:
        response = response_builder.error(message="服务器内部错误", code=500)
    
    return JSONResponse(status_code=500, content=response.model_dump())
```

## 6. 认证授权规范

### 6.1 JWT认证实现

```python
"""JWT认证和安全工具"""
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """验证令牌并返回载荷"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        return None
```

### 6.2 依赖注入认证

```python
"""认证依赖"""
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

security = HTTPBearer()

async def get_current_user(
    db: AsyncSession = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """获取当前用户依赖"""
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    username = payload.get("sub")
    if username is None:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的令牌")
    
    user = await get_user_by_username(db, username)
    if user is None:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="用户不存在")
    
    return user
```

## 7. 数据库规范

### 7.1 模型设计规范

#### 字段定义规范
```python
class User(BaseModel):
    """用户模型"""
    __tablename__ = "users"
    
    # 基础字段
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, index=True, nullable=False, comment="邮箱")
    password = Column(String(255), nullable=False, comment="密码哈希")
    
    # 可选字段
    nickname = Column(String(50), nullable=True, comment="昵称")
    avatar = Column(String(255), nullable=True, comment="头像URL")
    description = Column(Text, nullable=True, comment="个人描述")
    
    # 状态字段
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    status = Column(String(10), default="1", comment="用户状态：1-正常，2-禁用")
    
    # 关系字段
    roles = relationship("Role", secondary="user_roles", back_populates="users", comment="用户角色")
```

#### 字段设计原则
- **字段长度**: 根据实际需求设置合理的字段长度
- **索引设计**: 查询频繁的字段添加索引
- **默认值**: 为状态字段设置合理的默认值
- **注释完整**: 每个字段都必须有详细的comment
- **关系映射**: 明确定义外键关系和back_populates

### 7.2 数据库迁移规范

#### Alembic配置
```python
# alembic/env.py
from app.models.base import Base
from app.models.user.user import User
from app.models.user.role import Role
# 导入所有模型确保迁移发现

target_metadata = Base.metadata
```

#### 迁移操作流程
```bash
# 1. 生成迁移文件
alembic revision --autogenerate -m "添加用户表"

# 2. 检查生成的迁移文件
# 编辑 alembic/versions/xxx_添加用户表.py

# 3. 执行迁移
alembic upgrade head

# 4. 回滚迁移（如需要）
alembic downgrade -1
```

## 8. 配置管理规范

### 8.1 环境配置

```python
"""核心配置管理"""
from typing import List
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "DpTestPlatform API"
    app_version: str = "1.0.0"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "mysql+aiomysql://user:pass@host:port/db"
    
    # JWT 认证配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS 配置
    cors_origins: List[str] = ["*"]
    cors_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
    cors_headers: List[str] = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"
```

### 8.2 环境变量配置

```bash
# .env 文件示例
# 应用配置
APP_NAME=DpTestPlatform API
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=mysql+aiomysql://root:password@localhost:3306/dp_test

# JWT配置
SECRET_KEY=your-super-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]
```

## 9. 日志管理规范

### 9.1 日志配置

```python
"""日志配置模块"""
import logging
from loguru import logger
import sys

def setup_logger():
    """设置应用日志"""
    
    # 移除默认处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # 添加文件输出
    logger.add(
        "logs/app.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 day",
        retention="30 days",
        compression="zip"
    )
    
    return logger
```

### 9.2 日志使用规范

```python
"""日志使用示例"""
from app.utils.logger import setup_logger

logger = setup_logger()

class UserService:
    async def create_user(self, user_data: UserCreate):
        logger.info(f"开始创建用户: {user_data.username}")
        
        try:
            # 业务逻辑
            user = await self.repository.create(user_data)
            logger.info(f"用户创建成功: {user.id}")
            return user
        except Exception as e:
            logger.error(f"用户创建失败: {str(e)}", exc_info=True)
            raise
```

## 10. 测试规范

### 10.1 测试结构

```
tests/
├── __init__.py
├── conftest.py              # 测试配置和fixtures
├── unit/                    # 单元测试
│   ├── test_models/         # 模型测试
│   ├── test_services/       # 服务层测试
│   └── test_repositories/   # 仓储层测试
├── integration/             # 集成测试
│   └── test_api/            # API集成测试
└── fixtures/                # 测试数据
    └── user_data.py
```

### 10.2 测试用例示例

```python
"""用户API测试"""
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_create_user():
    """测试创建用户接口"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        
        response = await ac.post("/api/user/", json=user_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["username"] == "testuser"

@pytest.mark.asyncio  
async def test_get_user_info():
    """测试获取用户信息接口"""
    # 测试逻辑
    pass
```

## 11. 性能优化规范

### 11.1 数据库优化

#### 连接池配置
```python
"""数据库连接池优化"""
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    pool_size=20,           # 连接池大小
    max_overflow=30,        # 最大溢出连接数
    pool_timeout=30,        # 连接超时时间
    pool_recycle=3600,      # 连接回收时间
    pool_pre_ping=True      # 连接健康检查
)
```

#### 查询优化
```python
"""查询优化示例"""
class UserRepository:
    async def get_users_with_roles(self, skip: int = 0, limit: int = 100):
        """预加载关联数据，避免N+1查询问题"""
        query = (
            select(User)
            .options(selectinload(User.roles))  # 预加载角色
            .offset(skip)
            .limit(limit)
        )
        result = await self.db.execute(query)
        return result.scalars().all()
```

### 11.2 缓存策略

```python
"""缓存装饰器"""
from functools import wraps
import pickle
import redis

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expire_time: int = 300):
    """结果缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__module__}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return pickle.loads(cached_result)
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            redis_client.setex(cache_key, expire_time, pickle.dumps(result))
            return result
        
        return wrapper
    return decorator
```

## 12. 安全规范

### 12.1 输入验证

```python
"""输入验证Schema"""
from pydantic import BaseModel, validator, EmailStr
import re

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    
    @validator('username')
    def validate_username(cls, v):
        if not re.match(r'^[a-zA-Z0-9_]{3,20}$', v):
            raise ValueError('用户名只能包含字母、数字和下划线，长度3-20位')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('密码必须包含字母')
        if not re.search(r'\d', v):
            raise ValueError('密码必须包含数字')
        return v
```

### 12.2 SQL注入防护

```python
"""安全的查询实现"""
class UserRepository:
    async def search_users(self, keyword: str):
        """使用参数化查询防止SQL注入"""
        # ✅ 正确：使用参数化查询
        query = select(User).where(User.username.ilike(f"%{keyword}%"))
        result = await self.db.execute(query)
        return result.scalars().all()
        
        # ❌ 错误：字符串拼接查询
        # query = text(f"SELECT * FROM users WHERE username LIKE '%{keyword}%'")
```

## 13. 部署规范

### 13.1 生产环境配置

```python
"""生产环境启动脚本"""
# run.py
import uvicorn
from app.main import app
from app.core.config import settings

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        workers=4,                  # 工作进程数
        access_log=True,           # 访问日志
        use_colors=True,           # 彩色日志
        loop="uvloop",             # 高性能事件循环
        reload=False               # 生产环境禁用热重载
    )
```

### 13.2 Docker配置

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["python", "run.py"]
```

## 14. 代码质量规范

### 14.1 代码格式化

```python
# pyproject.toml
[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 100
```

### 14.2 类型检查

```python
"""类型注解规范"""
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

# ✅ 正确：完整的类型注解
async def get_user_list(
    skip: int = 0,
    limit: int = 100,
    filters: Optional[Dict[str, Any]] = None
) -> List[User]:
    """获取用户列表"""
    pass

# ✅ 正确：返回类型注解
def format_datetime(dt: datetime) -> str:
    """格式化时间"""
    return dt.strftime("%Y-%m-%d %H:%M:%S")
```

## 15. 开发工作流程

### 15.1 开发环境搭建

```bash
# 1. 克隆项目
git clone <repository-url>
cd DpTestPlatform

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 5. 初始化数据库
alembic upgrade head

# 6. 运行开发服务器
python app/main.py
```

### 15.2 代码提交规范

```bash
# Git提交消息格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      Bug修复
docs:     文档更新
style:    代码格式化
refactor: 代码重构
test:     测试相关
chore:    构建工具或辅助工具的变动

# 示例
feat(user): 添加用户头像上传功能
fix(auth): 修复Token过期时间计算错误
docs(api): 更新用户API文档
```

## 16. BaseService 重构最佳实践

### 16.1 BaseService 架构优势

#### 核心价值
- **消除代码重复**: 减少约60%的重复CRUD代码
- **统一架构模式**: 基类+钩子方法的优雅设计
- **类型安全保障**: 泛型支持确保编译时类型检查
- **一致错误处理**: 统一的异常抛出和处理机制

#### 设计模式
```python
# 标准服务类结构
class BusinessService(BaseService[Model, CreateSchema, UpdateSchema, ResponseSchema]):
    def __init__(self, db: AsyncSession):
        super().__init__(BusinessRepository(db))

    # 重写钩子方法实现特殊业务逻辑
    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        # 特殊处理逻辑（如：加密、验证）
        return create_dict

    # 业务方法使用基类通用方法
    async def create_business(self, data: CreateSchema, current_user: str) -> ResponseSchema:
        return await self.create(data, current_user)
```

### 16.2 钩子方法使用指南

#### 创建流程钩子
```python
async def _validate_before_create(self, create_data: CreateSchemaType, **kwargs) -> None:
    """创建前业务验证 - 用于重复性检查、权限验证等"""

async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
    """创建前数据处理 - 用于密码哈希、API Key加密等"""

async def _process_after_create(self, obj: ModelType) -> None:
    """创建后处理 - 用于发送通知、日志记录等"""
```

#### 更新流程钩子
```python
async def _process_before_update(self, obj: ModelType, update_dict: Dict[str, Any]) -> Dict[str, Any]:
    """更新前数据处理 - 用于字段加密、状态变更等"""
```

#### 响应转换钩子
```python
def _convert_to_response(self, obj: ModelType) -> ResponseSchemaType:
    """自定义响应转换 - 用于包含关联数据、格式化等"""
```

### 16.3 异步操作安全模式

#### 关系数据安全访问
```python
# ❌ 错误：可能触发延迟加载异常
roles = [role.code for role in user.roles]

# ✅ 正确：安全访问模式
try:
    roles = [role.code for role in user.roles] if hasattr(user, 'roles') and user.roles else []
except Exception:
    roles = []  # 优雅降级
```

#### 预加载策略
```python
# 重写基类方法以预加载关系
async def get_by_id(self, user_id: int) -> UserResponse:
    user = await self.repository.get_with_roles(user_id)  # 预加载关系
    if not user:
        raise_not_found(f"用户ID {user_id} 不存在")
    return self._convert_to_response(user)
```

### 16.4 常见问题解决方案

#### 方法调用规范
```python
# ✅ 正确：使用BaseRepository标准方法
user = await self.repository.get(user_id)

# ❌ 错误：使用不存在的方法
user = await self.repository.get_by_id(user_id)  # BaseRepository没有此方法
```

#### 参数传递规范
```python
# ✅ 正确：BaseService.list_with_pagination只接受一个参数
return await self.list_with_pagination(params)

# ❌ 错误：传递多个参数
return await self.list_with_pagination(params, filters)  # 参数错误
```

### 16.5 重构实施步骤

#### 步骤1：分析现有服务
1. 识别重复的CRUD代码
2. 分析特殊业务逻辑
3. 确定钩子方法需求

#### 步骤2：实施重构
1. 继承BaseService基类
2. 实现必要的钩子方法
3. 简化业务方法为基类调用

#### 步骤3：测试验证
1. 单元测试覆盖
2. 集成测试验证
3. 性能测试对比

## 17. 最佳实践总结

### 17.1 代码设计原则

1. **单一职责原则**: 每个类、函数只负责一个功能
2. **依赖倒置原则**: 高层模块不依赖低层模块
3. **接口隔离原则**: 细粒度接口设计
4. **开放封闭原则**: 对扩展开放，对修改封闭

### 17.2 异常处理最佳实践

1. **分层异常**: 不同层次使用不同的异常处理策略
2. **异常链**: 保持异常的完整调用栈
3. **日志记录**: 详细的错误日志记录
4. **用户友好**: 面向用户的错误信息

### 17.3 性能考虑

1. **异步优先**: 优先使用异步编程模式
2. **批量操作**: 避免N+1查询问题
3. **缓存策略**: 合理的缓存设计
4. **资源管理**: 及时释放数据库连接等资源

### 17.4 安全考虑

1. **输入验证**: 所有用户输入必须验证
2. **参数化查询**: 防止SQL注入
3. **认证授权**: 完善的权限控制
4. **敏感数据**: 加密存储敏感信息

---

**文档版本**: v1.1.0
**最后更新**: 2025-07-28
**适用项目**: DpTestPlatform Backend
**重要更新**: 新增 BaseService 重构最佳实践和钩子方法使用指南