<template>
  <ModelChatWindow
    v-model:visible="dialogVisible"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ModelChatWindow from '@/components/core/layouts/model-chat-window/index.vue'

// 组件属性
interface Props {
  visible: boolean
}

// 组件事件
interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})
</script>
