"""
SSH连接客户端
支持SSH服务器的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional

from .base_client import BaseClient, ConnectionResult

try:
    import asyncssh
    SSH_AVAILABLE = True
except ImportError:
    SSH_AVAILABLE = False


class SSHClient(BaseClient):
    """
    SSH连接客户端
    支持SSH服务器的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化SSH客户端
        
        Args:
            config: SSH连接配置
                - host: SSH主机地址
                - port: SSH端口 (默认: 22)
                - username: 用户名
                - password: 密码 (可选)
                - private_key: 私钥文件路径 (可选)
                - passphrase: 私钥密码 (可选)
        """
        super().__init__(config)
        
        if not SSH_AVAILABLE:
            raise ImportError("AsyncSSH库未安装，请运行: pip install asyncssh")
        
        # 验证必要配置
        if 'username' not in config:
            raise ValueError("用户名 (username) 是必需的")
        
        # 设置默认值
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 22)
        self.username = config['username']
        self.password = config.get('password', '')
        self.private_key = config.get('private_key', '')
        self.passphrase = config.get('passphrase', '')
        
        self.ssh_connection = None

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立SSH连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 准备连接参数
            connect_kwargs = {
                'host': self.host,
                'port': self.port,
                'username': self.username,
                'connect_timeout': timeout,
                'keepalive_interval': 30,
                'keepalive_count_max': 3,
                # 禁用主机密钥验证，用于测试连接
                'known_hosts': None
            }
            
            # 添加认证方式
            if self.private_key:
                # 使用私钥认证
                connect_kwargs['client_keys'] = [self.private_key]
                if self.passphrase:
                    connect_kwargs['passphrase'] = self.passphrase
            elif self.password:
                # 使用密码认证
                connect_kwargs['password'] = self.password
            else:
                # 尝试无密码连接（公钥认证）
                pass
            
            # 建立SSH连接
            self.ssh_connection = await asyncssh.connect(**connect_kwargs)
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到SSH服务器 {self.host}:{self.port}",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "username": self.username,
                    "auth_method": "private_key" if self.private_key else "password" if self.password else "public_key"
                }
            )
            
        except asyncssh.PermissionDenied as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"SSH认证失败: {str(e)}",
                duration=duration,
                details={"error_type": "PermissionDenied"}
            )
        except asyncssh.ConnectionLost as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"SSH连接丢失: {str(e)}",
                duration=duration,
                details={"error_type": "ConnectionLost"}
            )
        except asyncssh.HostKeyNotVerifiable as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"SSH主机密钥验证失败: {str(e)}",
                duration=duration,
                details={"error_type": "HostKeyNotVerifiable"}
            )
        except asyncssh.Error as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"SSH连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "SSHError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"SSH连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开SSH连接"""
        try:
            if self.ssh_connection:
                self.ssh_connection.close()
                await self.ssh_connection.wait_closed()
                self.ssh_connection = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试SSH连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        start_time = time.time()
        
        try:
            # 如果没有建立连接，先建立连接
            if not self.ssh_connection:
                connect_result = await self.connect(timeout)
                if not connect_result.success:
                    return connect_result
            
            # 执行多个测试命令
            test_commands = [
                ('whoami', '获取当前用户'),
                ('uname -a', '获取系统信息'),
                ('pwd', '获取当前目录'),
                ('date', '获取系统时间'),
                ('uptime', '获取系统运行时间')
            ]
            
            command_results = {}
            
            for cmd, desc in test_commands:
                try:
                    result = await asyncio.wait_for(
                        self.ssh_connection.run(cmd, check=True),
                        timeout=5
                    )
                    command_results[cmd] = {
                        "success": True,
                        "stdout": result.stdout.strip(),
                        "stderr": result.stderr.strip(),
                        "exit_status": result.exit_status,
                        "description": desc
                    }
                except asyncio.TimeoutError:
                    command_results[cmd] = {
                        "success": False,
                        "error": "命令执行超时",
                        "description": desc
                    }
                except Exception as e:
                    command_results[cmd] = {
                        "success": False,
                        "error": str(e),
                        "description": desc
                    }
            
            duration = time.time() - start_time
            
            # 检查是否至少有一个命令成功执行
            success_count = sum(1 for result in command_results.values() if result.get("success", False))
            
            return ConnectionResult(
                success=success_count > 0,
                message=f"SSH连接测试完成，{success_count}/{len(test_commands)} 个命令执行成功",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "username": self.username,
                    "command_results": command_results,
                    "success_count": success_count,
                    "total_commands": len(test_commands)
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"SSH测试异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def execute_command(self, command: str, timeout: int = 30) -> Dict[str, Any]:
        """
        执行SSH命令
        
        Args:
            command: 要执行的命令
            timeout: 超时时间(秒)
            
        Returns:
            Dict: 命令执行结果
        """
        if not self.ssh_connection:
            raise RuntimeError("SSH未连接")
        
        try:
            result = await asyncio.wait_for(
                self.ssh_connection.run(command, check=False),
                timeout=timeout
            )
            
            return {
                "success": result.exit_status == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "exit_status": result.exit_status,
                "command": command
            }
            
        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": f"命令执行超时 ({timeout}秒)",
                "command": command
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": command
            }

    async def upload_file(self, local_path: str, remote_path: str) -> Dict[str, Any]:
        """
        上传文件到远程服务器
        
        Args:
            local_path: 本地文件路径
            remote_path: 远程文件路径
            
        Returns:
            Dict: 上传结果
        """
        if not self.ssh_connection:
            raise RuntimeError("SSH未连接")
        
        try:
            async with self.ssh_connection.start_sftp_client() as sftp:
                await sftp.put(local_path, remote_path)
            
            return {
                "success": True,
                "message": f"文件上传成功: {local_path} -> {remote_path}",
                "local_path": local_path,
                "remote_path": remote_path
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "local_path": local_path,
                "remote_path": remote_path
            }

    async def download_file(self, remote_path: str, local_path: str) -> Dict[str, Any]:
        """
        从远程服务器下载文件
        
        Args:
            remote_path: 远程文件路径
            local_path: 本地文件路径
            
        Returns:
            Dict: 下载结果
        """
        if not self.ssh_connection:
            raise RuntimeError("SSH未连接")
        
        try:
            async with self.ssh_connection.start_sftp_client() as sftp:
                await sftp.get(remote_path, local_path)
            
            return {
                "success": True,
                "message": f"文件下载成功: {remote_path} -> {local_path}",
                "remote_path": remote_path,
                "local_path": local_path
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "remote_path": remote_path,
                "local_path": local_path
            } 