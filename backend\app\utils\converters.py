"""
数据转换工具
提供字段名转换、数据格式转换等功能
"""
import re
from typing import Any, Dict, List, Type, TypeVar
from datetime import datetime
from pydantic import BaseModel

T = TypeVar('T', bound=BaseModel)


class CaseConverter:
    """
    字段名转换器
    提供snake_case和camelCase之间的转换
    """
    
    @staticmethod
    def snake_to_camel(snake_str: str) -> str:
        """
        将snake_case转换为camelCase
        
        Args:
            snake_str: snake_case字符串
            
        Returns:
            camelCase字符串
        """
        components = snake_str.split('_')
        return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    @staticmethod
    def camel_to_snake(camel_str: str) -> str:
        """
        将camelCase转换为snake_case
        
        Args:
            camel_str: camelCase字符串
            
        Returns:
            snake_case字符串
        """
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_str)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    @staticmethod
    def convert_dict_keys(data: Dict[str, Any], to_camel: bool = True) -> Dict[str, Any]:
        """
        转换字典中的键名
        
        Args:
            data: 原始字典
            to_camel: True转换为camelCase，False转换为snake_case
            
        Returns:
            转换后的字典
        """
        if not isinstance(data, dict):
            return data
        
        converter = CaseConverter.snake_to_camel if to_camel else CaseConverter.camel_to_snake
        result = {}
        
        for key, value in data.items():
            new_key = converter(key)
            if isinstance(value, dict):
                result[new_key] = CaseConverter.convert_dict_keys(value, to_camel)
            elif isinstance(value, list):
                result[new_key] = [
                    CaseConverter.convert_dict_keys(item, to_camel) 
                    if isinstance(item, dict) else item 
                    for item in value
                ]
            else:
                result[new_key] = value
                
        return result


class DateTimeConverter:
    """
    日期时间转换器
    """
    
    @staticmethod
    def datetime_to_string(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """
        将datetime对象转换为字符串
        
        Args:
            dt: datetime对象
            format_str: 格式字符串
            
        Returns:
            格式化的日期时间字符串
        """
        if dt is None:
            return ""
        return dt.strftime(format_str)
    
    @staticmethod
    def string_to_datetime(date_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
        """
        将字符串转换为datetime对象
        
        Args:
            date_str: 日期时间字符串
            format_str: 格式字符串
            
        Returns:
            datetime对象
        """
        return datetime.strptime(date_str, format_str)


class ModelConverter:
    """
    模型转换器
    用于不同模型之间的数据转换
    """
    
    @staticmethod
    def model_to_dict(
        model: BaseModel, 
        include_none: bool = False,
        exclude_fields: List[str] = None,
        to_camel_case: bool = False
    ) -> Dict[str, Any]:
        """
        将Pydantic模型转换为字典
        
        Args:
            model: Pydantic模型实例
            include_none: 是否包含None值
            exclude_fields: 排除的字段列表
            to_camel_case: 是否转换为camelCase
            
        Returns:
            字典数据
        """
        if model is None:
            return {}
        
        # 获取模型数据
        data = model.model_dump(exclude_none=not include_none)
        
        # 排除指定字段
        if exclude_fields:
            for field in exclude_fields:
                data.pop(field, None)
        
        # 转换字段名
        if to_camel_case:
            data = CaseConverter.convert_dict_keys(data, to_camel=True)
        
        return data
    
    @staticmethod
    def models_to_list(
        models: List[BaseModel],
        include_none: bool = False,
        exclude_fields: List[str] = None,
        to_camel_case: bool = False
    ) -> List[Dict[str, Any]]:
        """
        将Pydantic模型列表转换为字典列表
        
        Args:
            models: Pydantic模型列表
            include_none: 是否包含None值
            exclude_fields: 排除的字段列表
            to_camel_case: 是否转换为camelCase
            
        Returns:
            字典列表
        """
        return [
            ModelConverter.model_to_dict(
                model, 
                include_none=include_none,
                exclude_fields=exclude_fields,
                to_camel_case=to_camel_case
            )
            for model in models
        ]
    
    @staticmethod
    def dict_to_model(data: Dict[str, Any], model_class: Type[T]) -> T:
        """
        将字典转换为Pydantic模型
        
        Args:
            data: 字典数据
            model_class: 目标模型类
            
        Returns:
            模型实例
        """
        return model_class(**data)


class ResponseConverter:
    """
    响应数据转换器
    专门用于API响应数据的格式化
    """
    
    @staticmethod
    def format_user_info(user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化用户信息数据
        
        Args:
            user_data: 用户数据
            
        Returns:
            格式化后的用户信息
        """
        formatted_data = CaseConverter.convert_dict_keys(user_data, to_camel=True)
        
        # 特殊字段处理
        if 'created_at' in user_data:
            formatted_data['createTime'] = DateTimeConverter.datetime_to_string(
                user_data['created_at']
            )
        if 'updated_at' in user_data:
            formatted_data['updateTime'] = DateTimeConverter.datetime_to_string(
                user_data['updated_at']
            )
            
        return formatted_data
    
    @staticmethod
    def format_user_list(user_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化用户列表数据
        
        Args:
            user_list: 用户列表
            
        Returns:
            格式化后的用户列表
        """
        return [ResponseConverter.format_user_info(user) for user in user_list]


# 全局转换器实例
case_converter = CaseConverter()
datetime_converter = DateTimeConverter()
model_converter = ModelConverter()
response_converter = ResponseConverter() 