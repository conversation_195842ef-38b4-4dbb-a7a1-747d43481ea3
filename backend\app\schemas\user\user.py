"""
用户相关 Pydantic 模式
"""
from typing import List, Optional
from pydantic import BaseModel, Field, EmailStr

from app.schemas.base import BaseResponseSchema, BaseCreateSchema, BaseUpdateSchema


class UserBase(BaseModel):
    """
    用户基础模式
    """
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(default=None, description="邮箱")
    nickname: Optional[str] = Field(default=None, max_length=50, description="昵称")
    avatar: Optional[str] = Field(default=None, description="头像URL")
    description: Optional[str] = Field(default=None, description="个人简介")
    is_active: bool = Field(default=True, description="是否激活")


class UserCreate(UserBase, BaseCreateSchema):
    """
    用户创建模式
    """
    password: str = Field(..., min_length=6, max_length=50, description="密码")
    is_superuser: bool = Field(default=False, description="是否超级用户")
    role_ids: Optional[List[int]] = Field(default=[], description="角色ID列表")


class UserUpdate(BaseUpdateSchema):
    """
    用户更新模式
    """
    username: Optional[str] = Field(default=None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(default=None, description="邮箱")
    nickname: Optional[str] = Field(default=None, max_length=50, description="昵称")
    avatar: Optional[str] = Field(default=None, description="头像URL")
    description: Optional[str] = Field(default=None, description="个人简介")
    is_active: Optional[bool] = Field(default=None, description="是否激活")
    role_ids: Optional[List[int]] = Field(default=None, description="角色ID列表")


class UserUpdatePassword(BaseModel):
    """
    用户密码更新模式
    """
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, max_length=50, description="新密码")


class UserResponse(UserBase, BaseResponseSchema):
    """
    用户响应模式
    """
    id: int
    is_superuser: bool
    status: str = Field(description="用户状态")
    last_login_at: Optional[str] = Field(default=None, description="最后登录时间")
    roles: List[str] = Field(default=[], description="角色列表")


class UserInfo(BaseModel):
    """
    用户信息模式（用于前端）
    """
    userId: int = Field(alias="id", description="用户ID")
    username: str = Field(alias="username", description="用户名")
    roles: List[str] = Field(default=[], description="角色代码列表")
    buttons: List[str] = Field(default=[], description="按钮权限列表")
    avatar: Optional[str] = Field(default=None, description="头像URL")
    email: Optional[str] = Field(default=None, description="邮箱")


class UserListItem(BaseModel):
    """
    用户列表项模式（用于前端）
    """
    id: int
    avatar: Optional[str] = Field(default=None)
    create_by: Optional[str] = Field(default=None)
    create_at: str
    update_by: Optional[str] = Field(default=None)
    update_at: str
    status: str
    username: str
    nickname: Optional[str] = Field(default=None)
    email: Optional[str] = Field(default=None)
    roles: List[str] = Field(default=[])


class UserListData(BaseModel):
    """
    用户列表数据模式（用于前端）
    """
    records: List[UserListItem]
    current: int
    size: int
    total: int


class UserProfileUpdate(BaseModel):
    """
    用户资料更新模式（仅允许用户自己更新的字段）
    """
    nickname: Optional[str] = Field(default=None, max_length=50, description="昵称")
    email: Optional[EmailStr] = Field(default=None, description="邮箱")
    avatar: Optional[str] = Field(default=None, description="头像URL")
    description: Optional[str] = Field(default=None, description="个人简介")


class UserStatusUpdate(BaseModel):
    """
    用户状态更新模式
    """
    status: str = Field(pattern="^[1-4]$", description="状态: 1-在线, 2-离线, 3-异常, 4-注销")


class UserSimpleResponse(BaseModel):
    """
    用户简单响应模式（用于列表等场景）
    """
    id: int = Field(description="用户ID")
    username: str = Field(description="用户名")
    nickname: Optional[str] = Field(default=None, description="昵称")
    avatar: Optional[str] = Field(default=None, description="头像URL")
    status: str = Field(description="用户状态")


class UserDetailResponse(UserSimpleResponse):
    """
    用户详细响应模式（用于详情页面）
    """
    email: Optional[str] = Field(default=None, description="邮箱")
    description: Optional[str] = Field(default=None, description="个人简介")
    is_active: bool = Field(description="是否激活")
    is_superuser: bool = Field(description="是否超级用户")
    roles: List[str] = Field(default=[], description="角色列表")
    last_login_at: Optional[str] = Field(default=None, description="最后登录时间")
    created_at: Optional[str] = Field(default=None, description="创建时间")
    updated_at: Optional[str] = Field(default=None, description="更新时间") 