"""添加模型配置表

Revision ID: 1a3ebae7013a
Revises: 
Create Date: 2025-07-28 13:46:45.971410

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1a3ebae7013a'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('model_config',
    sa.Column('name', sa.String(length=100), nullable=False, comment='模型名称（唯一标识）'),
    sa.Column('platform', sa.String(length=50), nullable=False, comment='所属平台（openai/claude/local等）'),
    sa.Column('description', sa.Text(), nullable=True, comment='模型描述'),
    sa.Column('api_url', sa.String(length=500), nullable=False, comment='API地址'),
    sa.Column('api_key_encrypted', sa.String(length=255), nullable=False, comment='加密的API Key'),
    sa.Column('timeout_seconds', sa.Integer(), nullable=True, comment='请求超时时间（秒）'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='状态：enabled-启用，disabled-停用'),
    sa.Column('health_status', sa.String(length=20), nullable=True, comment='健康状态：unknown-未知，healthy-健康，unhealthy-不健康'),
    sa.Column('last_health_check', sa.String(length=50), nullable=True, comment='最后健康检查时间'),
    sa.Column('config', sa.JSON(), nullable=True, comment='平台特定配置（JSON格式）'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建者'),
    sa.Column('updated_by', sa.String(length=50), nullable=True, comment='更新者'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_model_config_id'), 'model_config', ['id'], unique=False)
    op.create_index(op.f('ix_model_config_name'), 'model_config', ['name'], unique=True)
    op.create_index(op.f('ix_model_config_platform'), 'model_config', ['platform'], unique=False)
    op.create_table('role',
    sa.Column('name', sa.String(length=50), nullable=False, comment='角色名称'),
    sa.Column('code', sa.String(length=50), nullable=False, comment='角色代码'),
    sa.Column('description', sa.Text(), nullable=True, comment='角色描述'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.Column('permissions', sa.JSON(), nullable=True, comment='权限列表'),
    sa.Column('menu_permissions', sa.JSON(), nullable=True, comment='菜单权限'),
    sa.Column('button_permissions', sa.JSON(), nullable=True, comment='按钮权限'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建者'),
    sa.Column('updated_by', sa.String(length=50), nullable=True, comment='更新者'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_role_code'), 'role', ['code'], unique=True)
    op.create_index(op.f('ix_role_id'), 'role', ['id'], unique=False)
    op.create_index(op.f('ix_role_name'), 'role', ['name'], unique=True)
    op.create_table('user',
    sa.Column('username', sa.String(length=50), nullable=False, comment='用户名'),
    sa.Column('email', sa.String(length=100), nullable=True, comment='邮箱'),
    sa.Column('nickname', sa.String(length=50), nullable=True, comment='昵称'),
    sa.Column('hashed_password', sa.String(length=255), nullable=False, comment='密码哈希'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.Column('is_superuser', sa.Boolean(), nullable=True, comment='是否超级用户'),
    sa.Column('avatar', sa.String(length=500), nullable=True, comment='头像URL'),
    sa.Column('description', sa.Text(), nullable=True, comment='个人简介'),
    sa.Column('status', sa.String(length=10), nullable=True, comment='用户状态: 1-在线, 2-离线, 3-异常, 4-注销'),
    sa.Column('last_login_at', sa.String(length=50), nullable=True, comment='最后登录时间'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建者'),
    sa.Column('updated_by', sa.String(length=50), nullable=True, comment='更新者'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_index(op.f('ix_user_id'), 'user', ['id'], unique=False)
    op.create_index(op.f('ix_user_username'), 'user', ['username'], unique=True)
    op.create_table('user_roles',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['role_id'], ['role.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'role_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_roles')
    op.drop_index(op.f('ix_user_username'), table_name='user')
    op.drop_index(op.f('ix_user_id'), table_name='user')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    op.drop_index(op.f('ix_role_name'), table_name='role')
    op.drop_index(op.f('ix_role_id'), table_name='role')
    op.drop_index(op.f('ix_role_code'), table_name='role')
    op.drop_table('role')
    op.drop_index(op.f('ix_model_config_platform'), table_name='model_config')
    op.drop_index(op.f('ix_model_config_name'), table_name='model_config')
    op.drop_index(op.f('ix_model_config_id'), table_name='model_config')
    op.drop_table('model_config')
    # ### end Alembic commands ###
