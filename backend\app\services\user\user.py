"""
用户服务层
处理用户相关的业务逻辑
"""
from typing import List, Tuple, Optional, Dict, Any, Type
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.user.user import UserRepository
from app.schemas.user.user import UserCreate, UserUpdate, UserResponse, UserProfileUpdate, UserStatusUpdate, UserListItem
from app.schemas.common import SearchParams
from app.models.user.user import User
from app.utils.security import get_password_hash
from app.core.exceptions import raise_validation_error, raise_not_found


class UserService(BaseService[User, UserCreate, UserUpdate, UserResponse]):
    """用户业务服务"""

    def __init__(self, db: AsyncSession):
        repository = UserRepository(db)
        super().__init__(db, repository)
        # 定义有效状态
        self.valid_statuses = {"1", "2"}  # 1-正常, 2-禁用

    @property
    def model_class(self) -> Type[User]:
        """返回用户模型类"""
        return User

    @property
    def response_schema_class(self) -> Type[UserResponse]:
        """返回用户响应Schema类"""
        return UserResponse

    def _process_avatar_url(self, avatar: Optional[str]) -> Optional[str]:
        """处理头像URL，确保返回正确的访问路径"""
        if not avatar:
            return None
        
        # 如果已经是完整URL，直接返回
        if avatar.startswith(('http://', 'https://', '/uploads/')):
            return avatar
            
        # 如果只是文件名，添加/uploads/avatars/前缀
        if '/' not in avatar:
            return f"/uploads/avatars/{avatar}"
            
        # 如果是相对路径，确保以/uploads/开头
        if not avatar.startswith('/uploads/'):
            return f"/uploads/{avatar}"
            
        return avatar

    # ==================== BaseService钩子方法重写 ====================

    async def _validate_before_create(self, create_data: UserCreate, **kwargs) -> None:
        """创建用户前的验证"""
        # 检查用户名是否重复
        existing_user = await self.repository.get_by_username(create_data.username)
        if existing_user:
            raise_validation_error(f"用户名 '{create_data.username}' 已存在")

        # 检查邮箱是否重复
        existing_email = await self.repository.get_by_email(create_data.email)
        if existing_email:
            raise_validation_error(f"邮箱 '{create_data.email}' 已被使用")

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户前的处理"""
        # 加密密码
        if 'password' in create_dict:
            create_dict['password'] = get_password_hash(create_dict['password'])

        # 处理头像URL
        if 'avatar' in create_dict:
            create_dict['avatar'] = self._process_avatar_url(create_dict['avatar'])

        return create_dict

    async def _validate_before_update(self, user: User, update_data: UserUpdate, **kwargs) -> None:
        """更新用户前的验证"""
        # 如果要更新用户名，检查是否重复
        if update_data.username and update_data.username != user.username:
            existing = await self.repository.get_by_username(update_data.username)
            if existing:
                raise_validation_error(f"用户名 '{update_data.username}' 已存在")

        # 如果要更新邮箱，检查是否重复
        if update_data.email and update_data.email != user.email:
            existing = await self.repository.get_by_email(update_data.email)
            if existing:
                raise_validation_error(f"邮箱 '{update_data.email}' 已被使用")

        # 如果要更新状态，检查状态是否有效
        if update_data.status and update_data.status not in self.valid_statuses:
            raise_validation_error(f"无效的用户状态: {update_data.status}")

    async def _process_before_update(self, user: User, update_dict: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户前的处理"""
        # 如果更新密码，进行加密
        if 'password' in update_dict:
            update_dict['password'] = get_password_hash(update_dict['password'])

        # 处理头像URL
        if 'avatar' in update_dict:
            update_dict['avatar'] = self._process_avatar_url(update_dict['avatar'])

        return update_dict

    async def _build_list_filters(self, params: SearchParams) -> Dict[str, Any]:
        """构建用户列表查询过滤条件"""
        filters = {}
        if params.keyword:
            filters['keyword'] = params.keyword
        return filters

    def _convert_to_response(self, user: User) -> UserResponse:
        """将User对象转换为UserResponse - 安全处理角色关系"""
        return self._convert_user_to_response(user)

    def _convert_user_to_response(self, user: User) -> UserResponse:
        """将User对象转换为UserResponse - 统一处理角色转换和头像URL"""
        # 安全获取角色信息，避免延迟加载问题
        try:
            roles = [role.code for role in user.roles] if hasattr(user, 'roles') and user.roles else []
        except Exception:
            # 如果角色关系未加载，返回空列表
            roles = []

        user_dict = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "nickname": user.nickname,
            "avatar": self._process_avatar_url(user.avatar),
            "description": user.description,
            "is_active": user.is_active if user.is_active is not None else True,
            "is_superuser": user.is_superuser if user.is_superuser is not None else False,
            "status": user.status if user.status is not None else "1",
            "last_login_at": user.last_login_at,
            "roles": roles,
            "created_at": user.created_at.strftime("%Y-%m-%d %H:%M:%S") if user.created_at else None,
            "updated_at": user.updated_at.strftime("%Y-%m-%d %H:%M:%S") if user.updated_at else None,
            "created_by": user.created_by,
            "updated_by": user.updated_by
        }
        return UserResponse(**user_dict)

    def _convert_user_to_list_item(self, user: User) -> UserListItem:
        """将User对象转换为UserListItem - 专门用于用户列表显示"""
        # 安全获取角色信息，避免延迟加载问题
        try:
            roles = [role.code for role in user.roles] if hasattr(user, 'roles') and user.roles else []
        except Exception:
            # 如果角色关系未加载，返回空列表
            roles = []

        return UserListItem(
            id=user.id,
            avatar=self._process_avatar_url(user.avatar),
            create_by=user.created_by,
            create_at=user.created_at.strftime("%Y-%m-%d %H:%M:%S") if user.created_at else "",
            update_by=user.updated_by,
            update_at=user.updated_at.strftime("%Y-%m-%d %H:%M:%S") if user.updated_at else "",
            status=user.status if user.status is not None else "1",
            username=user.username,
            nickname=user.nickname,
            email=user.email,
            roles=roles
        )

    async def get_current_user_info(self, current_user: User) -> UserResponse:
        """获取当前用户信息 - 正确处理角色转换"""
        return self._convert_user_to_response(current_user)

    async def get_user_list(
        self, 
        params: SearchParams, 
        status_filter: Optional[str] = None
    ) -> Tuple[List[UserListItem], int]:
        """获取用户列表 - 返回UserListItem格式"""
        
        skip = (params.current - 1) * params.size
        users, total = await self.repository.search_users(
            skip=skip,
            limit=params.size,
            keyword=params.keyword,
            status=status_filter
        )
        
        # 转换为用户列表项格式
        user_list_items = []
        for user in users:
            user_list_items.append(self._convert_user_to_list_item(user))
        
        return user_list_items, total

    async def get_by_id(self, user_id: int) -> UserResponse:
        """根据ID获取用户信息 - 重写基类方法以预加载角色关系"""
        user = await self.repository.get_with_roles(user_id)
        if not user:
            raise_not_found(f"用户ID {user_id} 不存在")
        return self._convert_to_response(user)

    async def get_user_by_id(self, user_id: int) -> UserResponse:
        """根据ID获取用户信息"""
        return await self.get_by_id(user_id)

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前处理 - 密码哈希和业务校验"""
        # 业务校验1: 检查用户名是否重复
        if 'username' in create_dict:
            existing = await self.repository.get_by_username(create_dict['username'])
            if existing:
                raise_validation_error(f"用户名 '{create_dict['username']}' 已存在")

        # 业务校验2: 检查邮箱是否重复
        if 'email' in create_dict:
            existing_email = await self.repository.get_by_email(create_dict['email'])
            if existing_email:
                raise_validation_error(f"邮箱 '{create_dict['email']}' 已被使用")

        # 处理密码哈希
        if 'password' in create_dict:
            password = create_dict.pop('password')
            create_dict['hashed_password'] = get_password_hash(password)

        # 移除role_ids字段，稍后处理角色关联
        create_dict.pop('role_ids', [])

        return create_dict

    async def create_user(self, user_data: UserCreate, current_user_id: int) -> UserResponse:
        """创建用户 - 使用基类通用方法"""
        return await self.create(user_data, str(current_user_id))

    async def update_user_profile(
        self, 
        user_id: int, 
        profile_data: UserProfileUpdate, 
        current_user_id: int
    ) -> UserResponse:
        """更新用户资料"""
        
        # 检查用户是否存在
        user = await self.repository.get_by_id(user_id)
        if not user:
            raise_not_found(f"用户ID {user_id} 不存在")
        
        # 如果要更新邮箱，检查是否重复
        if profile_data.email and profile_data.email != user.email:
            existing_email = await self.repository.get_by_email(profile_data.email)
            if existing_email:
                raise_validation_error(f"邮箱 '{profile_data.email}' 已被使用")
        
        # 更新数据
        update_data = profile_data.model_dump(exclude_unset=True)
        update_data['updated_by'] = str(current_user_id)
        
        updated_user = await self.repository.update(user, update_data)
        return self._convert_user_to_response(updated_user)

    async def update_user(self, user_id: int, user_data: UserUpdate, current_user_id: int) -> UserResponse:
        """更新用户信息"""
        
        # 检查用户是否存在
        user = await self.repository.get_by_id(user_id)
        if not user:
            raise_not_found(f"用户ID {user_id} 不存在")
        
        # 如果要更新用户名，检查是否重复
        if user_data.username and user_data.username != user.username:
            existing = await self.repository.get_by_username(user_data.username)
            if existing:
                raise_validation_error(f"用户名 '{user_data.username}' 已存在")
        
        # 如果要更新邮箱，检查是否重复
        if user_data.email and user_data.email != user.email:
            existing_email = await self.repository.get_by_email(user_data.email)
            if existing_email:
                raise_validation_error(f"邮箱 '{user_data.email}' 已被使用")
        
        # 更新数据
        update_data = user_data.model_dump(exclude_unset=True)
        if user_data.password:
            update_data['password'] = get_password_hash(user_data.password)
        update_data['updated_by'] = str(current_user_id)
        
        updated_user = await self.repository.update(user, update_data)
        return self._convert_user_to_response(updated_user)

    async def update_user_status(
        self, 
        user_id: int, 
        status_data: UserStatusUpdate, 
        current_user_id: int
    ) -> UserResponse:
        """更新用户状态"""
        
        # 检查用户是否存在
        user = await self.repository.get_by_id(user_id)
        if not user:
            raise_not_found(f"用户ID {user_id} 不存在")
        
        # 检查状态值是否有效
        if status_data.status not in self.valid_statuses:
            raise_validation_error(f"无效的状态值: {status_data.status}")
        
        # 防止用户修改自己的状态
        if user_id == current_user_id:
            raise_validation_error("不能修改自己的状态")
        
        # 更新状态
        update_data = {
            'status': status_data.status,
            'updated_by': str(current_user_id)
        }
        
        updated_user = await self.repository.update(user, update_data)
        return self._convert_user_to_response(updated_user)

    async def _validate_before_delete(self, obj: User) -> None:
        """删除前验证 - 防止用户删除自己"""
        # 这个方法会在基类的 delete 方法中被调用
        # 但我们需要当前用户ID，所以这里暂时留空
        pass

    async def delete_user(self, user_id: int, current_user_id: int) -> None:
        """删除用户 - 使用基类通用方法"""
        # 检查用户是否存在
        user = await self.repository.get(user_id)
        if not user:
            raise_not_found(f"用户ID {user_id} 不存在")

        # 防止用户删除自己
        if user_id == current_user_id:
            raise_validation_error("不能删除自己")

        # 使用基类的删除方法
        await self.delete(user_id)