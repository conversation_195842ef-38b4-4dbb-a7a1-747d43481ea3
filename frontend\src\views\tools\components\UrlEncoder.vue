<template>
  <div class="url-encoder">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6f3;</i>
        <h2>URL 编码/解码工具</h2>
      </div>
      <p class="tool-description">
        URL 编码解码、组件分解、参数解析等功能
      </p>
    </div>

    <div class="tool-content">
      <!-- 功能选择 -->
      <div class="function-tabs">
        <div
          v-for="tab in functionTabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeFunction === tab.key }"
          @click="activeFunction = tab.key"
        >
          <i class="iconfont-sys" v-html="tab.icon"></i>
          <span>{{ tab.name }}</span>
        </div>
      </div>

      <!-- URL 编码/解码 -->
      <div v-if="activeFunction === 'encode'" class="encode-section">
        <div class="input-output-panels">
          <!-- 输入面板 -->
          <div class="panel">
            <div class="panel-header">
              <h3 class="panel-title">原始文本</h3>
              <div class="panel-actions">
                <ElButton size="small" @click="clearInput">
                  <i class="iconfont-sys">&#xe622;</i>
                  清空
                </ElButton>
                <ElButton size="small" @click="pasteInput">
                  <i class="iconfont-sys">&#xe623;</i>
                  粘贴
                </ElButton>
              </div>
            </div>
            
            <ElInput
              v-model="inputText"
              type="textarea"
              :rows="8"
              placeholder="请输入需要编码的文本或URL..."
              class="text-area"
              @input="handleEncode"
            />
          </div>

          <!-- 输出面板 -->
          <div class="panel">
            <div class="panel-header">
              <h3 class="panel-title">编码结果</h3>
              <div class="panel-actions">
                <ElButton size="small" @click="copyEncoded" :disabled="!encodedText">
                  <i class="iconfont-sys">&#xe627;</i>
                  复制
                </ElButton>
                <ElButton size="small" @click="swapTexts">
                  <i class="iconfont-sys">&#xe632;</i>
                  交换
                </ElButton>
              </div>
            </div>
            
            <ElInput
              v-model="encodedText"
              type="textarea"
              :rows="8"
              readonly
              placeholder="编码结果将在这里显示..."
              class="text-area result-area"
            />
          </div>
        </div>

        <!-- 编码选项 -->
        <div class="encode-options">
          <div class="option-group">
            <label class="option-label">编码类型：</label>
            <ElRadioGroup v-model="encodeType" @change="handleEncode">
              <ElRadio value="component">encodeURIComponent</ElRadio>
              <ElRadio value="uri">encodeURI</ElRadio>
              <ElRadio value="escape">escape (已废弃)</ElRadio>
            </ElRadioGroup>
          </div>
        </div>
      </div>

      <!-- URL 解析 -->
      <div v-if="activeFunction === 'parse'" class="parse-section">
        <div class="url-input">
          <div class="input-header">
            <h3 class="input-title">URL 输入</h3>
            <div class="input-actions">
              <ElButton size="small" @click="clearUrl">
                <i class="iconfont-sys">&#xe622;</i>
                清空
              </ElButton>
              <ElButton size="small" @click="pasteUrl">
                <i class="iconfont-sys">&#xe623;</i>
                粘贴
              </ElButton>
              <ElButton size="small" @click="loadSampleUrl">
                <i class="iconfont-sys">&#xe629;</i>
                示例
              </ElButton>
            </div>
          </div>
          
          <ElInput
            v-model="urlInput"
            placeholder="请输入完整的URL地址..."
            class="url-input-field"
            @input="handleUrlParse"
          />
        </div>

        <!-- URL 解析结果 -->
        <div v-if="urlParts" class="parse-result">
          <div class="result-header">
            <h3 class="result-title">URL 组件解析</h3>
          </div>
          
          <div class="url-components">
            <div class="component-item" v-for="(value, key) in urlParts" :key="key">
              <label class="component-label">{{ getComponentLabel(key) }}：</label>
              <div class="component-value">
                <code>{{ value || '(空)' }}</code>
                <ElButton 
                  v-if="value" 
                  size="small" 
                  text 
                  @click="copyComponent(value)"
                  class="copy-btn"
                >
                  <i class="iconfont-sys">&#xe627;</i>
                </ElButton>
              </div>
            </div>
          </div>

          <!-- URL 参数解析 -->
          <div v-if="urlParams && Object.keys(urlParams).length > 0" class="params-section">
            <div class="params-header">
              <h4 class="params-title">查询参数</h4>
              <ElButton size="small" @click="copyParams">
                <i class="iconfont-sys">&#xe627;</i>
                复制参数
              </ElButton>
            </div>
            
            <div class="params-table">
              <div class="table-header">
                <div class="header-cell">参数名</div>
                <div class="header-cell">参数值</div>
                <div class="header-cell">操作</div>
              </div>
              <div 
                v-for="(value, key) in urlParams" 
                :key="key" 
                class="table-row"
              >
                <div class="table-cell param-key">{{ key }}</div>
                <div class="table-cell param-value">{{ value }}</div>
                <div class="table-cell param-actions">
                  <ElButton size="small" text @click="copyComponent(`${key}=${value}`)">
                    <i class="iconfont-sys">&#xe627;</i>
                  </ElButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- URL 构建器 -->
      <div v-if="activeFunction === 'build'" class="build-section">
        <div class="builder-form">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">协议</label>
              <ElSelect v-model="urlBuilder.protocol" @change="handleUrlBuild">
                <ElOption label="https://" value="https:" />
                <ElOption label="http://" value="http:" />
                <ElOption label="ftp://" value="ftp:" />
                <ElOption label="file://" value="file:" />
              </ElSelect>
            </div>
            
            <div class="form-item">
              <label class="form-label">主机名</label>
              <ElInput 
                v-model="urlBuilder.hostname" 
                placeholder="example.com"
                @input="handleUrlBuild"
              />
            </div>
            
            <div class="form-item">
              <label class="form-label">端口</label>
              <ElInput 
                v-model="urlBuilder.port" 
                placeholder="80"
                @input="handleUrlBuild"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item full-width">
              <label class="form-label">路径</label>
              <ElInput 
                v-model="urlBuilder.pathname" 
                placeholder="/api/users"
                @input="handleUrlBuild"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item full-width">
              <label class="form-label">查询参数</label>
              <ElInput 
                v-model="urlBuilder.search" 
                placeholder="?name=value&key=data"
                @input="handleUrlBuild"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item full-width">
              <label class="form-label">锚点</label>
              <ElInput 
                v-model="urlBuilder.hash" 
                placeholder="#section"
                @input="handleUrlBuild"
              />
            </div>
          </div>
        </div>

        <!-- 构建结果 -->
        <div class="build-result">
          <div class="result-header">
            <h3 class="result-title">构建结果</h3>
            <ElButton size="small" @click="copyBuiltUrl" :disabled="!builtUrl">
              <i class="iconfont-sys">&#xe627;</i>
              复制URL
            </ElButton>
          </div>
          
          <div class="built-url">
            <code>{{ builtUrl || '请填写URL组件...' }}</code>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'UrlEncoder' })

// 功能选项
const functionTabs = [
  { key: 'encode', name: '编码/解码', icon: '&#xe643;' },
  { key: 'parse', name: 'URL解析', icon: '&#xe644;' },
  { key: 'build', name: 'URL构建', icon: '&#xe645;' }
]

// 响应式数据
const activeFunction = ref('encode')

// 编码/解码
const inputText = ref('')
const encodedText = ref('')
const encodeType = ref('component')

// URL 解析
const urlInput = ref('')
const urlParts = ref<any>(null)
const urlParams = ref<any>(null)

// URL 构建
const urlBuilder = reactive({
  protocol: 'https:',
  hostname: '',
  port: '',
  pathname: '',
  search: '',
  hash: ''
})
const builtUrl = ref('')

// 示例 URL
const sampleUrl = 'https://www.example.com:8080/api/users?name=张三&age=25&active=true#profile'

// 方法
const handleEncode = () => {
  if (!inputText.value.trim()) {
    encodedText.value = ''
    return
  }

  try {
    switch (encodeType.value) {
      case 'component':
        encodedText.value = encodeURIComponent(inputText.value)
        break
      case 'uri':
        encodedText.value = encodeURI(inputText.value)
        break
      case 'escape':
        encodedText.value = escape(inputText.value)
        break
    }
  } catch (error) {
    ElMessage.error('编码失败')
    encodedText.value = ''
  }
}

const handleUrlParse = () => {
  if (!urlInput.value.trim()) {
    urlParts.value = null
    urlParams.value = null
    return
  }

  try {
    const url = new URL(urlInput.value)
    
    urlParts.value = {
      href: url.href,
      protocol: url.protocol,
      hostname: url.hostname,
      port: url.port,
      pathname: url.pathname,
      search: url.search,
      hash: url.hash,
      origin: url.origin
    }

    // 解析查询参数
    const params: any = {}
    url.searchParams.forEach((value, key) => {
      params[key] = value
    })
    urlParams.value = params

  } catch (error) {
    ElMessage.error('URL 格式错误')
    urlParts.value = null
    urlParams.value = null
  }
}

const handleUrlBuild = () => {
  try {
    let url = ''
    
    if (urlBuilder.hostname) {
      url = urlBuilder.protocol + '//' + urlBuilder.hostname
      
      if (urlBuilder.port) {
        url += ':' + urlBuilder.port
      }
      
      if (urlBuilder.pathname) {
        if (!urlBuilder.pathname.startsWith('/')) {
          url += '/'
        }
        url += urlBuilder.pathname
      }
      
      if (urlBuilder.search) {
        if (!urlBuilder.search.startsWith('?')) {
          url += '?'
        }
        url += urlBuilder.search.startsWith('?') ? urlBuilder.search.substring(1) : urlBuilder.search
      }
      
      if (urlBuilder.hash) {
        if (!urlBuilder.hash.startsWith('#')) {
          url += '#'
        }
        url += urlBuilder.hash.startsWith('#') ? urlBuilder.hash.substring(1) : urlBuilder.hash
      }
    }
    
    builtUrl.value = url
  } catch (error) {
    builtUrl.value = ''
  }
}

const getComponentLabel = (key: string | number): string => {
  const keyStr = String(key)
  const labels: { [key: string]: string } = {
    href: '完整URL',
    protocol: '协议',
    hostname: '主机名',
    port: '端口',
    pathname: '路径',
    search: '查询字符串',
    hash: '锚点',
    origin: '源'
  }
  return labels[keyStr] || keyStr
}

const clearInput = () => {
  inputText.value = ''
  encodedText.value = ''
}

const pasteInput = async () => {
  try {
    const text = await navigator.clipboard.readText()
    inputText.value = text
    handleEncode()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const copyEncoded = async () => {
  if (!encodedText.value) return
  
  try {
    await navigator.clipboard.writeText(encodedText.value)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const swapTexts = () => {
  const temp = inputText.value
  inputText.value = encodedText.value
  encodedText.value = temp
  
  // 尝试解码
  if (inputText.value) {
    try {
      switch (encodeType.value) {
        case 'component':
          encodedText.value = decodeURIComponent(inputText.value)
          break
        case 'uri':
          encodedText.value = decodeURI(inputText.value)
          break
        case 'escape':
          encodedText.value = unescape(inputText.value)
          break
      }
    } catch (error) {
      ElMessage.error('解码失败')
    }
  }
  
  ElMessage.success('文本已交换')
}

const clearUrl = () => {
  urlInput.value = ''
  urlParts.value = null
  urlParams.value = null
}

const pasteUrl = async () => {
  try {
    const text = await navigator.clipboard.readText()
    urlInput.value = text
    handleUrlParse()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const loadSampleUrl = () => {
  urlInput.value = sampleUrl
  handleUrlParse()
  ElMessage.success('已加载示例URL')
}

const copyComponent = async (value: string) => {
  try {
    await navigator.clipboard.writeText(value)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const copyParams = async () => {
  if (!urlParams.value) return
  
  const paramsText = Object.entries(urlParams.value)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
  
  try {
    await navigator.clipboard.writeText(paramsText)
    ElMessage.success('复制参数成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const copyBuiltUrl = async () => {
  if (!builtUrl.value) return
  
  try {
    await navigator.clipboard.writeText(builtUrl.value)
    ElMessage.success('复制URL成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}
</script>

<style lang="scss" scoped>
.url-encoder {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .function-tabs {
      display: flex;
      gap: 8px;
      margin-bottom: 24px;
      padding: 4px;
      background: var(--el-fill-color-light);
      border-radius: 8px;

      .tab-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-regular);

        &:hover {
          background: var(--el-fill-color);
        }

        &.active {
          background: var(--el-color-primary);
          color: white;
        }

        i {
          font-size: 16px;
        }
      }
    }

    .encode-section,
    .parse-section,
    .build-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 24px;
    }

    .encode-section {
      .input-output-panels {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;

        .panel {
          .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            .panel-title {
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              margin: 0;
            }

            .panel-actions {
              display: flex;
              gap: 8px;
            }
          }

          .text-area {
            :deep(.el-textarea__inner) {
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 13px;
              line-height: 1.5;
            }

            &.result-area {
              :deep(.el-textarea__inner) {
                background: var(--el-fill-color-lighter);
              }
            }
          }
        }
      }

      .encode-options {
        .option-group {
          display: flex;
          align-items: center;
          gap: 16px;

          .option-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }
      }
    }

    .parse-section {
      .url-input {
        margin-bottom: 24px;

        .input-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .input-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }

          .input-actions {
            display: flex;
            gap: 8px;
          }
        }

        .url-input-field {
          :deep(.el-input__inner) {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
          }
        }
      }

      .parse-result {
        .result-header {
          margin-bottom: 16px;

          .result-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .url-components {
          margin-bottom: 24px;

          .component-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 4px;
            margin-bottom: 8px;

            .component-label {
              width: 100px;
              font-size: 13px;
              font-weight: 500;
              color: var(--el-text-color-regular);
              flex-shrink: 0;
            }

            .component-value {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;

              code {
                flex: 1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 13px;
                color: var(--el-color-primary);
                background: var(--el-fill-color-light);
                padding: 2px 6px;
                border-radius: 3px;
                word-break: break-all;
              }

              .copy-btn {
                flex-shrink: 0;
              }
            }
          }
        }

        .params-section {
          .params-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            .params-title {
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              margin: 0;
            }
          }

          .params-table {
            border: 1px solid var(--el-border-color);
            border-radius: 6px;
            overflow: hidden;

            .table-header {
              display: grid;
              grid-template-columns: 1fr 2fr auto;
              background: var(--el-fill-color-light);

              .header-cell {
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 600;
                color: var(--el-text-color-primary);
                border-right: 1px solid var(--el-border-color-lighter);

                &:last-child {
                  border-right: none;
                }
              }
            }

            .table-row {
              display: grid;
              grid-template-columns: 1fr 2fr auto;
              border-top: 1px solid var(--el-border-color-lighter);

              .table-cell {
                padding: 8px 12px;
                font-size: 13px;
                border-right: 1px solid var(--el-border-color-lighter);

                &:last-child {
                  border-right: none;
                }

                &.param-key {
                  font-family: 'Consolas', 'Monaco', monospace;
                  font-weight: 600;
                  color: var(--el-color-primary);
                }

                &.param-value {
                  font-family: 'Consolas', 'Monaco', monospace;
                  word-break: break-all;
                }

                &.param-actions {
                  display: flex;
                  justify-content: center;
                }
              }
            }
          }
        }
      }
    }

    .build-section {
      .builder-form {
        margin-bottom: 24px;

        .form-row {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;
          margin-bottom: 16px;

          .form-item {
            &.full-width {
              grid-column: 1 / -1;
            }

            .form-label {
              display: block;
              font-size: 13px;
              font-weight: 500;
              color: var(--el-text-color-primary);
              margin-bottom: 6px;
            }
          }
        }
      }

      .build-result {
        .result-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .result-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .built-url {
          padding: 12px 16px;
          background: var(--el-fill-color-light);
          border: 1px solid var(--el-border-color);
          border-radius: 6px;

          code {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            color: var(--el-color-primary);
            word-break: break-all;
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .url-encoder {
    .tool-content {
      .function-tabs {
        .tab-item {
          flex-direction: column;
          gap: 4px;
          padding: 8px 12px;

          span {
            font-size: 12px;
          }
        }
      }

      .encode-section {
        .input-output-panels {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }

      .parse-section {
        .url-components {
          .component-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;

            .component-label {
              width: auto;
            }

            .component-value {
              width: 100%;
            }
          }
        }

        .params-table {
          .table-header,
          .table-row {
            grid-template-columns: 1fr;

            .header-cell,
            .table-cell {
              border-right: none;
              border-bottom: 1px solid var(--el-border-color-lighter);

              &:last-child {
                border-bottom: none;
              }
            }
          }
        }
      }

      .build-section {
        .builder-form {
          .form-row {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>
