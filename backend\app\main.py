"""
FastAPI 应用主入口
"""
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from app.api.router import router_manager
from app.core.config import settings
from app.database import init_db, close_db
from app.middleware.request_logging import RequestLoggingMiddleware
from app.core.responses import response_builder
from app.core.exceptions import BusinessException
from app.utils.logger import setup_logger

# 设置日志
logger = setup_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    """
    # 启动时执行
    logger.info("🚀 应用启动中...")
    await init_db()
    logger.info("✅ 数据库初始化完成")
    yield
    # 关闭时执行
    logger.info("🔄 应用关闭中...")
    await close_db()
    logger.info("✅ 应用已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="DpTestPlatform Backend API - 基于 FastAPI + SQLAlchemy + Pydantic V2",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers
    )

# 添加请求日志中间件
if settings.debug:
    app.add_middleware(RequestLoggingMiddleware)


# 健康检查接口
@app.get("/", summary="根路径健康检查")
async def root():
    """
    根路径健康检查
    """
    return response_builder.success(
        data={
            "message": f"Welcome to {settings.app_name}",
            "version": settings.app_version,
            "status": "healthy",
            "docs_url": "/docs" if settings.debug else None
        },
        msg="服务运行正常"
    )


@app.get("/health", summary="健康检查")
async def health_check():
    """
    健康检查接口
    """
    return response_builder.success(
        data={"status": "healthy", "timestamp": "now"},
        message="服务健康"
    )


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("应用启动中...")

    # 启动监控数据清理任务
    try:
        from app.tasks.monitor_cleanup import start_monitor_cleanup_task
        await start_monitor_cleanup_task()
        logger.info("监控数据清理任务启动成功")
    except Exception as e:
        logger.error(f"启动监控数据清理任务失败: {e}")

    logger.info("应用启动完成")

# 注册所有API路由
app.include_router(router_manager.get_router())

# 静态文件服务
from pathlib import Path

# 获取项目根目录
project_root = Path(__file__).parent.parent
uploads_dir = project_root / "uploads"

# 确保uploads目录存在
uploads_dir.mkdir(exist_ok=True)

app.mount("/uploads", StaticFiles(directory=str(uploads_dir)), name="uploads")

# 业务异常全局处理器
@app.exception_handler(BusinessException)
async def business_exception_handler(request: Request, exc: BusinessException):
    """
    业务异常全局处理器
    """
    logger.warning(f"业务异常: {exc.message}")
    response = response_builder.error(
        message=exc.message,
        code=exc.code,
        data=exc.data
    )
    return JSONResponse(
        status_code=exc.code,
        content=response.model_dump()
    )


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """
    HTTP异常处理器
    """
    response = response_builder.error(
        message=exc.detail,
        code=exc.status_code
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=response.model_dump()
    )


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    全局异常处理器
    """
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    
    if settings.debug:
        # 开发环境返回详细错误信息
        response = response_builder.error(
            message=f"服务器内部错误: {str(exc)}",
            code=500
        )
    else:
        # 生产环境返回通用错误信息
        response = response_builder.error(
            message="服务器内部错误",
            code=500
        )
    
    return JSONResponse(
        status_code=500,
        content=response.model_dump()
    )


if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"🚀 启动 {settings.app_name} v{settings.app_version}")
    logger.info(f"📍 服务地址: http://{settings.host}:{settings.port}")
    logger.info(f"📚 API文档: http://{settings.host}:{settings.port}/docs")
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    ) 