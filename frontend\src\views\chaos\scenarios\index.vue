<template>
  <div class=" art-full-height">
    <!-- 搜索栏 -->
    <ScenarioSearch v-model:filter="searchForm" @reset="handleReset" @search="handleSearch" />

    <ElCard class="art-table-card" shadow="never">

      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton @click="handleCreateScenario" type="primary">创建场景</ElButton>
        </template>
        <template #right>
          <ElButton @click="toggleViewMode" style="margin-left: 5px;">
            {{ viewMode === 'grid' ? '表格视图' : '卡片视图' }}
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        v-if="viewMode === 'table'"
        :loading="loading"
        :data="scenarioList"
        :columns="columns"
        :pagination="paginationState"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <template #actions="{ row }">
          <div class="table-actions">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              查看
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleUseScenario(row)"
            >
              使用
            </el-button>
            <el-button
              v-if="!row.is_builtin"
              type="warning"
              size="small"
              @click="handleEditScenario(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="!row.is_builtin"
              type="danger"
              size="small"
              @click="handleDeleteScenario(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </ArtTable>

      <!-- 卡片视图 -->
      <div v-else class="scenario-cards">
        <div class="cards-grid">
          <div
            v-for="scenario in scenarioList"
            :key="scenario.id"
            class="scenario-card"
            @click="handleViewDetail(scenario)"
          >
            <div class="card-header">
              <div class="card-title">{{ scenario.name }}</div>
              <div class="scenario-badges">
                <el-tag :type="getFaultTypeTagType(scenario.fault_type)" size="small">
                  {{ getFaultTypeLabel(scenario.fault_type) }}
                </el-tag>
                <el-tag v-if="scenario.is_builtin" type="info" size="small">内置</el-tag>
              </div>
            </div>

            <div class="card-content">
              <div class="card-description">{{ scenario.description || '暂无描述' }}</div>
              <div class="card-meta">
                <span class="meta-item">分类: {{ scenario.category || '-' }}</span>
                <span class="meta-item">使用: {{ scenario.usage_count }}次</span>
              </div>
            </div>

            <div class="card-actions">
              <el-button
                type="primary"
                size="small"
                @click.stop="handleUseScenario(scenario)"
              >
                使用
              </el-button>
              <el-button
                v-if="!scenario.is_builtin"
                type="warning"
                size="small"
                @click.stop="handleEditScenario(scenario)"
              >
                编辑
              </el-button>
              <el-button
                v-if="!scenario.is_builtin"
                type="danger"
                size="small"
                @click.stop="handleDeleteScenario(scenario)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

    </ElCard>

    <!-- 场景详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="currentScenario?.name"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentScenario" class="scenario-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">场景名称：</span>
              <span class="value">{{ currentScenario.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">故障类型：</span>
              <el-tag :type="getFaultTypeTagType(currentScenario.fault_type)">
                {{ getFaultTypeLabel(currentScenario.fault_type) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <span class="label">分类：</span>
              <span class="value">{{ currentScenario.category || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">使用次数：</span>
              <span class="value">{{ currentScenario.usage_count }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDateTime(currentScenario.created_at) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建者：</span>
              <span class="value">{{ currentScenario.created_by || '-' }}</span>
            </div>
          </div>
          <div class="detail-item full-width">
            <span class="label">描述：</span>
            <span class="value">{{ currentScenario.description || '-' }}</span>
          </div>
          <div class="detail-item full-width" v-if="currentScenario.tags">
            <span class="label">标签：</span>
            <div class="value">
              <el-tag
                v-for="tag in currentScenario.tags.split(',')"
                :key="tag"
                size="small"
                effect="plain"
                style="margin-right: 8px; margin-bottom: 4px;"
              >
                {{ tag.trim() }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>默认参数</h4>
          <div class="params-content">
            <pre v-if="currentScenario.default_params && Object.keys(currentScenario.default_params).length > 0">{{ JSON.stringify(currentScenario.default_params, null, 2) }}</pre>
            <div v-else class="no-data">暂无默认参数</div>
          </div>
        </div>

        <div class="detail-section">
          <h4>参数结构</h4>
          <div v-if="currentScenario.param_schema && Object.keys(currentScenario.param_schema).length > 0" class="param-schema-content">
            <div v-for="(param, key) in currentScenario.param_schema" :key="key" class="param-item">
              <div class="param-header">
                <span class="param-name">{{ key }}</span>
                <div class="param-badges">
                  <el-tag v-if="param.required" type="danger" size="small">必填</el-tag>
                  <el-tag type="info" size="small">{{ param.type || '未知类型' }}</el-tag>
                </div>
              </div>
              <div class="param-description">{{ param.description || '暂无描述' }}</div>
              <div v-if="param.min !== undefined || param.max !== undefined" class="param-range">
                <span class="range-label">取值范围：</span>
                <span v-if="param.min !== undefined">最小值 {{ param.min }}</span>
                <span v-if="param.min !== undefined && param.max !== undefined"> - </span>
                <span v-if="param.max !== undefined">最大值 {{ param.max }}</span>
              </div>
              <div v-if="param.options && param.options.length > 0" class="param-options">
                <span class="options-label">可选值：</span>
                <el-tag v-for="option in param.options" :key="option" size="small" class="option-tag">{{ option }}</el-tag>
              </div>
            </div>
          </div>
          <div v-else class="no-data">暂无参数结构定义</div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleUseCurrentScenario">
          使用此模板
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElButton, ElCard } from 'element-plus'
import ArtTable from '@/components/core/tables/art-table/index.vue'
import ArtTableHeader from '@/components/core/tables/art-table-header/index.vue'
import ScenarioSearch from './modules/scenario-search.vue'
import { useTable } from '@/composables/useTable'
import { useChaosScenariosStore } from '@/store/business/chaos/scenarios'
import ChaosService from '@/api/chaosApi'
import type { ChaosScenario, ChaosScenarioSearchParams } from '@/types/api/chaos'

defineOptions({ name: 'ChaosScenarios' })

const router = useRouter()
const chaosScenariosStore = useChaosScenariosStore()

// 响应式数据
const currentScenario = ref<ChaosScenario | null>(null)
const detailDialogVisible = ref(false)
const viewMode = ref<'grid' | 'table'>('table')

// 搜索表单
const searchForm = reactive<ChaosScenarioSearchParams>({
  keyword: '',
  fault_type: '',
  category: '',
  is_builtin: undefined,
  is_active: undefined,
  page: 1,
  size: 20,
  order_by: 'usage_count',
  desc: true
})

// 表格配置
const {
  tableData: scenarioList,
  isLoading: loading,
  paginationState,
  columns,
  columnChecks,
  onPageSizeChange,
  onCurrentPageChange,
  loadData: refreshData
} = useTable<ChaosScenario>({
  core: {
    apiFn: async (params) => {
      const result = await ChaosService.getScenarioList({ ...searchForm, ...params })
      return result
    },
    immediate: true,
    apiParams: { page: 1, size: 20 }, // 设置默认分页参数
    columnsFactory: () => [
      { prop: 'category', label: '分类', width: 200 },
      { prop: 'fault_type', label: '故障类型', width: 200 },
      { prop: 'name', label: '场景名称', minWidth: 200 },
      { prop: 'description', label: '描述', minWidth: 200 },
      {
        prop: 'is_builtin',
        label: '类型',
        width: 100,
        formatter: (row: any) => formatBuiltinType(row.is_builtin)
      },
      {
        prop: 'is_active',
        label: '状态',
        width: 100,
        formatter: (row: any) => formatActiveStatus(row.is_active)
      },
      { prop: 'usage_count', label: '使用次数', width: 100 },
      { prop: 'created_at', label: '创建时间', width: 160 },
      { prop: 'actions', label: '操作', width: 200, fixed: 'right' as const, useSlot: true, slotName: 'actions' }
    ]
  }
})



// 方法
const refreshAll = () => {
  refreshData()
}

const handleSearch = () => {
  refreshData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    fault_type: '',
    category: '',
    is_builtin: undefined,
    is_active: undefined,
    page: 1,
    size: 20,
    order_by: 'usage_count',
    desc: true
  })
  refreshData()
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'table' : 'grid'
}



const handleCreateScenario = () => {
  router.push('/chaos/scenarios/create')
}


const handleUseCurrentScenario = () => {
  if (currentScenario.value) {
    router.push(`/chaos/tasks/create?scenario=${currentScenario.value.id}`)
  }
}

// 故障类型标签辅助方法
const getFaultTypeTagType = (faultType: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary'> = {
    'cpu': 'danger',
    'memory': 'warning',
    'network': 'info',
    'disk': 'success',
    'process': 'danger',
    'k8s': 'primary',
    'docker': 'info',
    // 兼容旧的中文类型
    'CPU故障': 'danger',
    '内存故障': 'warning',
    '网络故障': 'info',
    '磁盘故障': 'success',
    '进程故障': 'danger',
    'Kubernetes故障': 'primary',
    'JVM故障': 'warning',
    'Docker故障': 'info'
  }
  return typeMap[faultType] || 'info'
}

const getFaultTypeLabel = (faultType: string) => {
  const labelMap: Record<string, string> = {
    'cpu': 'CPU故障',
    'memory': '内存故障',
    'network': '网络故障',
    'disk': '磁盘故障',
    'process': '进程故障',
    'k8s': 'Kubernetes故障',
    'docker': 'Docker故障'
  }
  return labelMap[faultType] || faultType || '未知类型'
}

// 格式化显示方法
const formatBuiltinType = (isBuiltin: boolean) => {
  return isBuiltin ? '内置' : '自定义'
}

const formatActiveStatus = (isActive: boolean) => {
  return isActive ? '启用' : '禁用'
}







const handleViewDetail = async (scenario: ChaosScenario) => {
  try {
    // 获取完整的场景详情（包含参数信息）
    const fullScenario = await ChaosService.getScenarioDetail(scenario.id)
    currentScenario.value = fullScenario
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取场景详情失败:', error)
    ElMessage.error('获取场景详情失败')
  }
}

const handleUseScenario = (scenario: ChaosScenario) => {
  router.push({
    path: '/chaos/tasks/create',
    query: { scenario: scenario.id }
  })
}

const handleEditScenario = (scenario: ChaosScenario) => {
  if (scenario.is_builtin) {
    ElMessage.warning('内置场景不能编辑')
    return
  }
  // TODO: 实现编辑功能
  ElMessage.info('编辑功能开发中...')
}

const handleDeleteScenario = async (scenario: ChaosScenario) => {
  if (scenario.is_builtin) {
    ElMessage.warning('内置场景不能删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除场景 "${scenario.name}" 吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )

    await chaosScenariosStore.deleteScenario(scenario.id)
    ElMessage.success('场景删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除场景失败')
    }
  }
}



const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleDateString('zh-CN')
}
</script>

<style scoped>



.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.grid-view {
  min-height: 400px;
}

.scenario-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.scenario-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.scenario-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-title {
  flex: 1;
}

.scenario-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.scenario-badges {
  display: flex;
  gap: 6px;
}

.card-actions {
  flex-shrink: 0;
}

/* 卡片视图样式 */
.scenario-cards {
  padding: 20px 0;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.scenario-card {
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 8px;
  padding: 20px;
  background: rgb(var(--art-main-bg-color));
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 200px;
  /* 常驻边框阴影效果 */
  box-shadow: 0 2px 8px rgba(var(--art-border-light), 0.3);
}

.scenario-card:hover {
  border-color: rgb(var(--art-primary));
  box-shadow: 0 6px 16px rgba(var(--art-primary), 0.15);
  transform: translateY(-3px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: rgb(var(--art-text-primary));
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.card-description {
  color: rgb(var(--art-text-secondary));
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: rgb(var(--art-text-gray-400));
}

.meta-item {
  white-space: nowrap;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 详情对话框样式 */
.scenario-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: rgb(var(--art-text-primary));
  font-size: 16px;
  font-weight: 600;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.detail-item .label {
  font-weight: 500;
  color: rgb(var(--art-text-secondary));
  min-width: 80px;
}

.detail-item .value {
  color: rgb(var(--art-text-primary));
  flex: 1;
}

.params-content {
  background: rgb(var(--art-main-bg-color)) !important;
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 6px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.params-content pre {
  margin: 0 !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: rgb(var(--art-text-primary)) !important;
  background: rgb(var(--art-main-bg-color)) !important;
  white-space: pre-wrap;
  word-break: break-all;
  border: none !important;
  outline: none !important;
}

.no-data {
  color: rgb(var(--art-text-gray-400));
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 参数结构样式 */
.param-schema-content {
  background: rgb(var(--art-main-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 6px;
  padding: 0;
  max-height: 400px;
  overflow-y: auto;
}

.param-item {
  padding: 16px;
  border-bottom: 2px solid rgb(var(--art-border-light));
  background: rgb(var(--art-main-bg-color));
  margin: 0;
}

.param-item:last-child {
  border-bottom: none;
}

.param-item:not(:last-child) {
  border-bottom: 2px solid rgb(var(--art-border-light));
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.param-name {
  font-weight: 600;
  color: rgb(var(--art-text-primary));
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.param-badges {
  display: flex;
  gap: 6px;
}

.param-description {
  color: rgb(var(--art-text-secondary));
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.param-range {
  font-size: 12px;
  color: rgb(var(--art-text-gray-400));
  margin-bottom: 6px;
}

.param-options {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.range-label,
.options-label {
  font-weight: 500;
  color: rgb(var(--art-text-secondary));
  margin-right: 6px;
}

.option-tag {
  margin-right: 4px;
  margin-bottom: 2px;
}



.card-content {
  margin-bottom: 16px;
}

.scenario-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.scenario-category {
  color: #999;
  font-size: 12px;
  margin-bottom: 8px;
}

.scenario-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-tags {
  color: #999;
  font-size: 12px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #999;
}

.usage-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.table-view {
  min-height: 400px;
}

.pagination-section {
  padding: 20px 0;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.scenario-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  margin-bottom: 12px;
}

.detail-item .label {
  width: 80px;
  color: #666;
  flex-shrink: 0;
  font-size: 14px;
}

.detail-item .value {
  color: #333;
  flex: 1;
  font-size: 14px;
  word-break: break-all;
}

.params-content {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.params-content pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
