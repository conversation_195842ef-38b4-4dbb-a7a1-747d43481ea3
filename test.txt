一、模块定位​
混沌测试模块是测试平台的核心功能模块之一，旨在基于 chaosBlade 框架，为 linux 主机对象提供全面、灵活且高效的故障注入任务能力。通过模拟各种真实世界中可能出现的故障场景，检验系统在极端条件下的稳定性、可靠性和容错能力，帮助测试人员提前发现系统潜在的问题和漏洞，为系统的优化和改进提供有力依据。​
二、核心功能设计​
（一）故障注入任务管理​
任务创建：提供直观的用户界面，支持测试人员根据测试需求配置故障注入任务的各项参数。包括选择目标 linux 主机、指定故障类型（如 CPU 压力、内存占用、网络延迟、磁盘 IO 异常等）、设置故障注入的强度、持续时间、开始时间等。同时，支持导入预设的故障场景模板，提高任务创建效率。​
任务调度：具备灵活的任务调度机制，可支持立即执行、定时执行和周期性执行等多种方式。测试人员可以根据测试计划，精确安排故障注入任务的执行时间，确保测试的有序进行。​
任务执行与监控：在任务执行过程中，实时监控故障注入的状态和效果。通过与 linux 主机的交互，获取主机的各项性能指标（如 CPU 使用率、内存占用率、网络吞吐量等），并以图表、数据表格等形式直观展示。同时，实时反馈故障注入任务的执行进度、是否成功等信息。​
任务暂停、终止与恢复：支持对正在执行的故障注入任务进行暂停和终止操作。在任务暂停后，可根据需要恢复任务的执行，确保测试过程的灵活性和可控性。​
任务历史记录与查询：自动记录所有故障注入任务的执行信息，包括任务名称、执行时间、目标主机、故障类型、参数配置、执行结果等。提供多条件查询功能，方便测试人员回顾和分析历史任务。​
（二）故障类型支持​
基于 chaosBlade 框架，支持对 linux 主机对象注入多种常见故障类型，具体如下：​
CPU 故障：可模拟 CPU 高负载场景，通过设置 CPU 使用率的百分比（如 80%、90% 等），持续一定时间，检验系统在 CPU 资源紧张情况下的表现。​
内存故障：能够模拟内存泄漏或内存占用过高的情况，设置内存占用的大小（如 2GB、4GB 等）和持续时间，测试系统对内存资源的管理和分配能力。​
网络故障：包括网络延迟、网络丢包、网络带宽限制等。可设置延迟时间（如 100ms、500ms）、丢包率（如 5%、10%）、带宽大小（如 1Mbps、10Mbps）等参数，模拟网络不稳定的环境，评估系统的网络通信能力。​
磁盘故障：支持模拟磁盘 IO 压力过大、磁盘空间满等故障。通过设置磁盘读写速率、占用磁盘空间的大小等，测试系统对磁盘操作的处理能力和容错性。​
进程故障：可以对指定的进程进行故障注入，如杀死进程、暂停进程、重启进程等，检验系统在进程异常情况下的恢复能力和稳定性。
K8s 故障场景类型:节点宕机模拟：通过 ChaosBlade Operator，利用 Kubernetes 的节点驱逐机制，将指定节点上的所有 Pod 驱逐到其他节点，模拟节点突然宕机的场景。
节点资源压力模拟：在节点上注入 CPU 满载、内存溢出、磁盘 IO 高负载等故障。例如，模拟节点 CPU 满载，
Pod 故障：​
Pod 重启 / 崩溃模拟：使用 ChaosBlade Operator，对指定的 Pod 进行频繁重启或模拟崩溃。例如，模拟 Pod 重启：
Pod 资源限制模拟：临时修改 Pod 的资源限制，触发 OOM（Out of Memory）场景或 CPU 资源不足的情况。例如，设置 Pod 内存限制并触发 OOM：
网络故障：​
网络分区模拟：利用 ChaosBlade 的网络故障注入功能，隔离 K8s 集群中指定节点或 Pod 之间的网络通信，模拟网络分区场景。例如，隔离两个 Pod 之间的网络：
网络延迟与丢包模拟：在 K8s 网络中注入延迟或丢包故障，模拟网络不稳定的情况。例如，为指定 Pod 的网络通信添加 100ms 的延迟：

（三）目标主机管理​
使用环境管理接口获取目标主机


三、架构设计​
（一）整体架构​
混沌测试模块采用分层架构设计，主要包括表现层、业务逻辑层、数据访问层和底层交互层。​
表现层：负责与用户进行交互，提供 Web 界面，展示各种功能模块和数据信息，接收用户的操作指令。​
业务逻辑层：是模块的核心部分，实现故障注入任务管理、故障类型处理、目标主机管理、告警通知等核心业务逻辑。​
数据访问层：负责与数据库进行交互，实现对主机信息、任务信息、告警信息等数据的存储、查询、更新和删除等操作。​
底层交互层：通过调用 chaosBlade 框架的 API，与 linux 主机进行通信，执行故障注入命令，获取主机的性能指标和状态信息。​
（二）与 chaosBlade 框架的集成​
模块通过封装 chaosBlade 框架的 API，实现对故障注入功能的调用。在底层交互层，将用户配置的故障注入参数转换为 chaosBlade 框架能够识别的命令格式，发送给目标 linux 主机，执行相应的故障注入操作。同时，接收 chaosBlade 框架返回的执行结果和主机信息，传递给业务逻辑层进行处理和展示。


四、界面设计
（一）故障注入任务管理​
任务创建：提供直观的用户界面，支持测试人员根据测试需求配置故障注入任务的各项参数。包括选择目标 linux 主机、指定故障类型（如 CPU 压力、内存占用、网络延迟、磁盘 IO 异常等）、设置故障注入的强度、持续时间、开始时间等。同时，支持导入预设的故障场景模板，提高任务创建效率。​
任务调度：具备灵活的任务调度机制，可支持立即执行、定时执行和周期性执行等多种方式。测试人员可以根据测试计划，精确安排故障注入任务的执行时间，确保测试的有序进行。​
任务执行与监控：在任务执行过程中，实时监控故障注入的状态和效果。通过与 linux 主机的交互，获取主机的各项性能指标（如 CPU 使用率、内存占用率、网络吞吐量等），并以图表、数据表格等形式直观展示。同时，实时反馈故障注入任务的执行进度、是否成功等信息。​
任务暂停、终止与恢复：支持对正在执行的故障注入任务进行暂停和终止操作。在任务暂停后，可根据需要恢复任务的执行，确保测试过程的灵活性和可控性。​
任务历史记录与查询：自动记录所有故障注入任务的执行信息，包括任务名称、执行时间、目标主机、故障类型、参数配置、执行结果等。提供多条件查询功能，方便测试人员回顾和分析历史任务。​
（二）故障类型模板
基于 chaosBlade 框架，支持对 linux 主机对象注入多种常见故障类型，具体如下：​
CPU 故障：可模拟 CPU 高负载场景，通过设置 CPU 使用率的百分比（如 80%、90% 等），持续一定时间，检验系统在 CPU 资源紧张情况下的表现。​
内存故障：能够模拟内存泄漏或内存占用过高的情况，设置内存占用的大小（如 2GB、4GB 等）和持续时间，测试系统对内存资源的管理和分配能力。​
网络故障：包括网络延迟、网络丢包、网络带宽限制等。可设置延迟时间（如 100ms、500ms）、丢包率（如 5%、10%）、带宽大小（如 1Mbps、10Mbps）等参数，模拟网络不稳定的环境，评估系统的网络通信能力。​
磁盘故障：支持模拟磁盘 IO 压力过大、磁盘空间满等故障。通过设置磁盘读写速率、占用磁盘空间的大小等，测试系统对磁盘操作的处理能力和容错性。​
进程故障：可以对指定的进程进行故障注入，如杀死进程、暂停进程、重启进程等，检验系统在进程异常情况下的恢复能力和稳定性。


五、页面布局设计​
（一）整体布局结构​
一级菜单-故障测试；2级菜单包括任务管理、任务监控、故障模板、
（二）核心功能页面布局​
故障任务管理列表页​
顶部设置 “创建任务” 按钮和 “批量操作” 下拉菜单（包含批量删除、批量执行等），右侧放置搜索框和筛选条件区域（可按任务状态、故障类型、时间范围等筛选）。​
中间区域为任务列表表格，采用响应式设计，表头包含 “任务名称”“目标主机 / 分组”“故障类型”“状态”“创建时间”“操作” 等字段。操作列提供 “编辑”“执行”“暂停”“终止”“查看详情” 等按钮，按钮样式根据任务状态动态变化（如运行中任务显示 “暂停”“终止”，已结束任务显示 “查看详情”）。​
表格下方为分页控件，支持页码快速跳转和每页显示条数设置。​
提供探针部署按钮，点击可为任务管理绑定的环境通过远程安装 chaosBlade，chaosblade包在指定目录下，

创建故障注入任务页​
采用分步表单布局，通过进度条显示当前步骤（如 “基本信息”→“故障配置”→“执行计划”→“确认提交”），降低用户填写复杂度。​
第一步：基本信息：左侧显示表单说明文字，右侧为输入区域，包含 “任务名称”（必填，文本框）、“任务描述”（选填，文本域）、“目标主机 / 分组”（下拉选择框，支持多选，可搜索）。​
第二步：故障配置：根据选择的目标主机类型，加载对应的故障类型选项卡（如 “CPU 故障”“内存故障” 等）。每个选项卡内放置该故障类型的参数设置项，例如 “CPU 故障” 选项卡包含 “CPU 使用率”（滑块 + 百分比输入框）、“持续时间”（时间选择器）。参数旁添加 “？” 图标，鼠标悬停显示参数说明 tooltip。​
第三步：执行计划：提供 “立即执行”“定时执行”“周期性执行” 单选按钮组，选择不同选项显示对应配置项（如定时执行显示日期时间选择器，周期性执行显示周期类型、间隔、开始 / 结束时间等）。​
第四步：确认提交：以卡片形式汇总前序步骤配置的所有信息，高亮显示关键参数（如故障类型、目标主机、执行时间），底部放置 “上一步”“提交” 按钮，提交按钮为蓝色突出显示。​

任务监控详情页​
采用左右分栏布局，左侧为任务基本信息和操作区，右侧为监控图表区。​
左侧上半部分显示任务基本信息（名称、状态、执行时间等），下半部分为操作按钮（如 “暂停”“终止”“下载日志”）。​
右侧分为多个监控面板，通过选项卡切换不同指标（如 “CPU 监控”“内存监控”“网络监控”），每个面板内放置时间序列图表（如折线图），横轴为时间，纵轴为指标值，支持鼠标悬停查看具体数值，图表下方显示数据统计信息（如最大值、平均值）。