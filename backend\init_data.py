"""
数据初始化脚本
创建默认用户和角色数据
"""
import asyncio
from sqlalchemy import select, insert
from app.database import AsyncSessionLocal
from app.repositories.user import UserRepository
from app.models.role import Role
from app.models.user import user_roles
from app.schemas.user import UserCreate


async def init_default_data():
    """
    初始化默认数据
    """
    async with AsyncSessionLocal() as db:
        try:
            # 初始化用户仓储
            user_repo = UserRepository(db)
            # 创建默认角色
            print("📋 创建默认角色...")
            
            # 检查管理员角色是否已存在
            result = await db.execute(select(Role).where(Role.code == "R_ADMIN"))
            existing_admin_role = result.scalar_one_or_none()
            
            if not existing_admin_role:
                admin_role = Role(
                    name="管理员",
                    code="R_ADMIN",
                    description="系统管理员角色",
                    permissions=["*"],
                    menu_permissions=["*"],
                    button_permissions=["*"]
                )
                db.add(admin_role)
                print("   ✅ 管理员角色创建成功")
            else:
                print("   ℹ️  管理员角色已存在")
            
            # 检查普通用户角色是否已存在
            result = await db.execute(select(Role).where(Role.code == "R_USER"))
            existing_user_role = result.scalar_one_or_none()
            
            if not existing_user_role:
                user_role = Role(
                    name="普通用户",
                    code="R_USER",
                    description="普通用户角色",
                    permissions=["user:read"],
                    menu_permissions=["dashboard", "user"],
                    button_permissions=["user:view"]
                )
                db.add(user_role)
                print("   ✅ 普通用户角色创建成功")
            else:
                print("   ℹ️  普通用户角色已存在")
            
            # 提交角色创建
            await db.commit()
            
            # 重新获取角色以获得ID
            result = await db.execute(select(Role).where(Role.code == "R_ADMIN"))
            admin_role = result.scalar_one()
            
            result = await db.execute(select(Role).where(Role.code == "R_USER"))
            user_role = result.scalar_one()
            
            print("👤 创建默认用户...")
            
            # 创建默认管理员用户
            admin_user_data = UserCreate(
                username="admin",
                password="admin123",
                email="<EMAIL>",
                nickname="系统管理员",
                is_superuser=True
            )
            
            # 检查管理员用户是否已存在
            existing_admin_user = await user_repo.get_by_username(username="admin")
            if not existing_admin_user:
                # 创建用户
                admin_user = await user_repo.create_user(admin_user_data)
                
                # 关联用户和角色 - 使用关联表直接插入
                await db.execute(
                    insert(user_roles).values(
                        user_id=admin_user.id,
                        role_id=admin_role.id
                    )
                )
                await db.commit()
                
                print("   ✅ 默认管理员用户创建成功")
                print("      用户名: admin")
                print("      密码: admin123")
                print("      角色: 管理员")
            else:
                print("   ℹ️  默认管理员用户已存在")
            
            # 创建默认测试用户
            test_user_data = UserCreate(
                username="testuser",
                password="test123",
                email="<EMAIL>",
                nickname="测试用户"
            )
            
            existing_test_user = await user_repo.get_by_username(username="testuser")
            if not existing_test_user:
                # 创建用户
                test_user = await user_repo.create_user(test_user_data)
                
                # 关联用户和角色 - 使用关联表直接插入
                await db.execute(
                    insert(user_roles).values(
                        user_id=test_user.id,
                        role_id=user_role.id
                    )
                )
                await db.commit()
                
                print("   ✅ 默认测试用户创建成功")
                print("      用户名: testuser")
                print("      密码: test123")
                print("      角色: 普通用户")
            else:
                print("   ℹ️  默认测试用户已存在")
                
        except Exception as e:
            print(f"❌ 初始化数据失败: {e}")
            import traceback
            traceback.print_exc()
            await db.rollback()


if __name__ == "__main__":
    print("🚀 开始初始化默认数据...")
    asyncio.run(init_default_data())
    print("✨ 数据初始化完成！") 