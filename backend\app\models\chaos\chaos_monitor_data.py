"""
混沌测试监控数据模型
"""
from datetime import datetime
from sqlalchemy import Column, BigInteger, Integer, String, DECIMAL, TIMESTAMP, Index, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class ChaosMonitorData(BaseModel):
    """监控数据模型"""
    __tablename__ = "chaos_monitor_data"
    
    id = Column(BigInteger, primary_key=True, index=True, comment="主键ID")
    environment_id = Column(Integer, ForeignKey("environment.id"), nullable=False, comment="环境ID")
    host_id = Column(Integer, nullable=False, comment="主机ID")
    metric_type = Column(String(20), nullable=False, comment="指标类型: cpu, memory, network, disk")
    metric_value = Column(DECIMAL(8, 2), nullable=False, comment="指标值")
    collected_at = Column(TIMESTAMP, nullable=False, default=datetime.utcnow, comment="采集时间")

    # 关联关系
    environment = relationship("Environment", back_populates="monitor_data")
    
    # 索引定义
    __table_args__ = (
        Index('idx_environment_host_time', 'environment_id', 'host_id', 'collected_at'),
        Index('idx_cleanup_time', 'collected_at'),
        {'comment': '监控数据表'}
    )
    
    def __repr__(self):
        return f"<ChaosMonitorData(id={self.id}, environment_id={self.environment_id}, metric_type='{self.metric_type}', value={self.metric_value})>"
    
    @property
    def formatted_value(self) -> str:
        """格式化的指标值"""
        if self.metric_type in ['cpu', 'memory', 'disk_usage']:
            return f"{self.metric_value:.1f}%"
        elif self.metric_type in ['network_rx', 'network_tx']:
            return f"{self.metric_value:.1f}KB"
        else:
            return f"{self.metric_value:.2f}"
    
    @property
    def metric_display_name(self) -> str:
        """指标显示名称"""
        metric_names = {
            'cpu': 'CPU使用率',
            'memory': '内存使用率', 
            'network_rx': '网络接收',
            'network_tx': '网络发送',
            'disk_usage': '磁盘使用率',
            'disk_io_read': '磁盘读取',
            'disk_io_write': '磁盘写入'
        }
        return metric_names.get(self.metric_type, self.metric_type)
