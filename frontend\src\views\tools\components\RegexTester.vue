<template>
  <div class="regex-tester">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe631;</i>
        <h2>正则表达式测试工具</h2>
      </div>
      <p class="tool-description">
        测试正则表达式匹配效果，支持实时预览和常用模式
      </p>
    </div>

    <div class="tool-content">
      <!-- 正则表达式输入 -->
      <div class="regex-section">
        <div class="section-header">
          <h3 class="section-title">正则表达式</h3>
          <div class="section-actions">
            <ElButton size="small" @click="showPatternLibrary = true">
              <i class="iconfont-sys">&#xe637;</i>
              常用模式
            </ElButton>
          </div>
        </div>
        
        <div class="regex-input-wrapper">
          <span class="regex-delimiter">/</span>
          <ElInput
            v-model="regexPattern"
            placeholder="请输入正则表达式..."
            class="regex-input"
            @input="handleRegexChange"
          />
          <span class="regex-delimiter">/</span>
          <ElInput
            v-model="regexFlags"
            placeholder="flags"
            class="flags-input"
            @input="handleRegexChange"
          />
        </div>
        
        <!-- 正则选项 -->
        <div class="regex-options">
          <ElCheckbox v-model="flags.global" @change="updateFlags">
            全局匹配 (g)
          </ElCheckbox>
          <ElCheckbox v-model="flags.ignoreCase" @change="updateFlags">
            忽略大小写 (i)
          </ElCheckbox>
          <ElCheckbox v-model="flags.multiline" @change="updateFlags">
            多行模式 (m)
          </ElCheckbox>
          <ElCheckbox v-model="flags.dotAll" @change="updateFlags">
            单行模式 (s)
          </ElCheckbox>
        </div>
        
        <!-- 错误提示 -->
        <div v-if="regexError" class="error-message">
          <i class="iconfont-sys">&#xe62a;</i>
          {{ regexError }}
        </div>
      </div>

      <!-- 测试文本输入 -->
      <div class="text-section">
        <div class="section-header">
          <h3 class="section-title">测试文本</h3>
          <div class="section-actions">
            <ElButton size="small" @click="clearText">
              <i class="iconfont-sys">&#xe622;</i>
              清空
            </ElButton>
            <ElButton size="small" @click="pasteText">
              <i class="iconfont-sys">&#xe623;</i>
              粘贴
            </ElButton>
            <ElButton size="small" @click="loadSampleText">
              <i class="iconfont-sys">&#xe629;</i>
              示例
            </ElButton>
          </div>
        </div>
        
        <ElInput
          v-model="testText"
          type="textarea"
          :rows="8"
          placeholder="请输入要测试的文本..."
          class="text-textarea"
          @input="handleTextChange"
        />
      </div>

      <!-- 匹配结果 -->
      <div v-if="matchResult" class="result-section">
        <div class="section-header">
          <h3 class="section-title">匹配结果</h3>
          <div class="section-actions">
            <ElButton size="small" @click="copyMatches">
              <i class="iconfont-sys">&#xe627;</i>
              复制匹配项
            </ElButton>
          </div>
        </div>
        
        <!-- 匹配统计 -->
        <div class="match-stats">
          <div class="stat-item">
            <label>匹配数量：</label>
            <span class="stat-value">{{ matchResult.matches.length }}</span>
          </div>
          <div class="stat-item">
            <label>是否匹配：</label>
            <span class="stat-value" :class="{ success: matchResult.isMatch, error: !matchResult.isMatch }">
              {{ matchResult.isMatch ? '是' : '否' }}
            </span>
          </div>
        </div>
        
        <!-- 高亮显示的文本 -->
        <div class="highlighted-text">
          <h4 class="highlight-title">高亮显示</h4>
          <div class="highlight-content" v-html="matchResult.highlightedText"></div>
        </div>
        
        <!-- 匹配详情 -->
        <div v-if="matchResult.matches.length > 0" class="match-details">
          <h4 class="details-title">匹配详情</h4>
          <div class="match-list">
            <div
              v-for="(match, index) in matchResult.matches"
              :key="index"
              class="match-item"
            >
              <div class="match-header">
                <span class="match-index">匹配 {{ index + 1 }}</span>
                <span class="match-position">位置: {{ match.index }} - {{ match.index + match.value.length - 1 }}</span>
              </div>
              <div class="match-content">
                <div class="match-value">
                  <label>匹配值：</label>
                  <code>{{ match.value }}</code>
                </div>
                <div v-if="match.groups && match.groups.length > 0" class="match-groups">
                  <label>捕获组：</label>
                  <div class="group-list">
                    <div
                      v-for="(group, groupIndex) in match.groups"
                      :key="groupIndex"
                      class="group-item"
                    >
                      <span class="group-index">${{ groupIndex + 1 }}:</span>
                      <code>{{ group || '(未匹配)' }}</code>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 常用模式库对话框 -->
    <ElDialog
      v-model="showPatternLibrary"
      title="常用正则表达式模式"
      width="600px"
      class="pattern-dialog"
    >
      <div class="pattern-library">
        <div
          v-for="category in patternLibrary"
          :key="category.name"
          class="pattern-category"
        >
          <h4 class="category-title">{{ category.name }}</h4>
          <div class="pattern-list">
            <div
              v-for="pattern in category.patterns"
              :key="pattern.name"
              class="pattern-item"
              @click="selectPattern(pattern)"
            >
              <div class="pattern-name">{{ pattern.name }}</div>
              <div class="pattern-regex">{{ pattern.regex }}</div>
              <div class="pattern-desc">{{ pattern.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'RegexTester' })

// 响应式数据
const regexPattern = ref('')
const regexFlags = ref('')
const testText = ref('')
const regexError = ref('')
const matchResult = ref<any>(null)
const showPatternLibrary = ref(false)

// 正则选项
const flags = ref({
  global: false,
  ignoreCase: false,
  multiline: false,
  dotAll: false
})

// 常用模式库
const patternLibrary = [
  {
    name: '基础验证',
    patterns: [
      { name: '邮箱地址', regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$', description: '验证邮箱地址格式' },
      { name: '手机号码', regex: '^1[3-9]\\d{9}$', description: '验证中国大陆手机号' },
      { name: 'IP地址', regex: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$', description: '验证IPv4地址' },
      { name: 'URL链接', regex: '^https?:\\/\\/[\\w\\-]+(\\.[\\w\\-]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?$', description: '验证HTTP/HTTPS链接' }
    ]
  },
  {
    name: '数字格式',
    patterns: [
      { name: '整数', regex: '^-?\\d+$', description: '匹配正负整数' },
      { name: '小数', regex: '^-?\\d+(\\.\\d+)?$', description: '匹配正负小数' },
      { name: '身份证号', regex: '^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$', description: '验证18位身份证号' },
      { name: '银行卡号', regex: '^[1-9]\\d{12,19}$', description: '验证银行卡号格式' }
    ]
  },
  {
    name: '文本处理',
    patterns: [
      { name: '中文字符', regex: '[\\u4e00-\\u9fa5]', description: '匹配中文字符' },
      { name: '英文单词', regex: '\\b[a-zA-Z]+\\b', description: '匹配英文单词' },
      { name: 'HTML标签', regex: '<[^>]+>', description: '匹配HTML标签' },
      { name: '空白行', regex: '^\\s*$', description: '匹配空白行' }
    ]
  }
]

// 示例文本
const sampleText = `用户信息：
姓名：张三
邮箱：<EMAIL>
手机：13812345678
网站：https://www.example.com
IP地址：***********
身份证：110101199001011234

其他测试文本：
Hello World! 你好世界！
<div class="container">内容</div>
数字：123, -456, 3.14, -2.5

空白行测试：

结束。`

// 方法
const handleRegexChange = () => {
  regexError.value = ''
  testRegex()
}

const handleTextChange = () => {
  testRegex()
}

const updateFlags = () => {
  const flagsArray = []
  if (flags.value.global) flagsArray.push('g')
  if (flags.value.ignoreCase) flagsArray.push('i')
  if (flags.value.multiline) flagsArray.push('m')
  if (flags.value.dotAll) flagsArray.push('s')
  
  regexFlags.value = flagsArray.join('')
  testRegex()
}

const testRegex = () => {
  if (!regexPattern.value.trim() || !testText.value.trim()) {
    matchResult.value = null
    return
  }

  try {
    const regex = new RegExp(regexPattern.value, regexFlags.value)
    const matches = []
    let match
    let highlightedText = testText.value
    let offset = 0

    if (flags.value.global) {
      while ((match = regex.exec(testText.value)) !== null) {
        matches.push({
          value: match[0],
          index: match.index,
          groups: match.slice(1)
        })
        
        if (!flags.value.global) break
      }
    } else {
      match = regex.exec(testText.value)
      if (match) {
        matches.push({
          value: match[0],
          index: match.index,
          groups: match.slice(1)
        })
      }
    }

    // 生成高亮文本
    if (matches.length > 0) {
      const sortedMatches = [...matches].sort((a, b) => b.index - a.index)
      
      for (const match of sortedMatches) {
        const before = highlightedText.substring(0, match.index + offset)
        const highlighted = `<mark class="regex-match">${escapeHtml(match.value)}</mark>`
        const after = highlightedText.substring(match.index + match.value.length + offset)
        
        highlightedText = before + highlighted + after
        offset += highlighted.length - match.value.length
      }
    }

    matchResult.value = {
      matches,
      isMatch: matches.length > 0,
      highlightedText: highlightedText.replace(/\n/g, '<br>')
    }

    regexError.value = ''
  } catch (error) {
    regexError.value = error instanceof Error ? error.message : '正则表达式语法错误'
    matchResult.value = null
  }
}

const escapeHtml = (text: string): string => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

const clearText = () => {
  testText.value = ''
  matchResult.value = null
}

const pasteText = async () => {
  try {
    const text = await navigator.clipboard.readText()
    testText.value = text
    testRegex()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const loadSampleText = () => {
  testText.value = sampleText
  testRegex()
  ElMessage.success('已加载示例文本')
}

const copyMatches = async () => {
  if (!matchResult.value || matchResult.value.matches.length === 0) return
  
  const text = matchResult.value.matches.map((match: any, index: number) => 
    `匹配 ${index + 1}: ${match.value}`
  ).join('\n')
  
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const selectPattern = (pattern: any) => {
  regexPattern.value = pattern.regex
  showPatternLibrary.value = false
  testRegex()
  ElMessage.success(`已选择模式：${pattern.name}`)
}

// 监听 flags 变化，同步到 regexFlags
watch(regexFlags, (newFlags) => {
  flags.value.global = newFlags.includes('g')
  flags.value.ignoreCase = newFlags.includes('i')
  flags.value.multiline = newFlags.includes('m')
  flags.value.dotAll = newFlags.includes('s')
  testRegex()
})
</script>

<style lang="scss" scoped>
.regex-tester {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .regex-section,
    .text-section,
    .result-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }

        .section-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .regex-section {
      .regex-input-wrapper {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 16px;

        .regex-delimiter {
          font-family: 'Consolas', 'Monaco', monospace;
          font-size: 18px;
          font-weight: bold;
          color: var(--el-color-primary);
        }

        .regex-input {
          flex: 1;
          
          :deep(.el-input__inner) {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
          }
        }

        .flags-input {
          width: 80px;
          
          :deep(.el-input__inner) {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            text-align: center;
          }
        }
      }

      .regex-options {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
        margin-bottom: 16px;
      }

      .error-message {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: var(--el-color-error-light-9);
        color: var(--el-color-error);
        border-radius: 4px;
        font-size: 13px;

        i {
          font-size: 14px;
        }
      }
    }

    .text-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }

    .match-stats {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;
      padding: 12px 16px;
      background: var(--el-fill-color-light);
      border-radius: 6px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;

        label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        .stat-value {
          font-size: 16px;
          font-weight: 600;

          &.success {
            color: var(--el-color-success);
          }

          &.error {
            color: var(--el-color-error);
          }
        }
      }
    }

    .highlighted-text {
      margin-bottom: 20px;

      .highlight-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }

      .highlight-content {
        padding: 12px;
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color);
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 13px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-all;

        :deep(.regex-match) {
          background: var(--el-color-warning-light-7);
          color: var(--el-color-warning-dark-2);
          padding: 2px 4px;
          border-radius: 3px;
          font-weight: 600;
        }
      }
    }

    .match-details {
      .details-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 12px 0;
      }

      .match-list {
        .match-item {
          border: 1px solid var(--el-border-color);
          border-radius: 6px;
          margin-bottom: 12px;
          overflow: hidden;

          &:last-child {
            margin-bottom: 0;
          }

          .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: var(--el-fill-color-light);
            font-size: 13px;

            .match-index {
              font-weight: 600;
              color: var(--el-color-primary);
            }

            .match-position {
              color: var(--el-text-color-regular);
            }
          }

          .match-content {
            padding: 12px;

            .match-value,
            .match-groups {
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              label {
                display: inline-block;
                font-size: 13px;
                font-weight: 500;
                color: var(--el-text-color-regular);
                margin-right: 8px;
                min-width: 60px;
              }

              code {
                background: var(--el-fill-color-light);
                padding: 2px 6px;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
              }
            }

            .group-list {
              margin-top: 8px;

              .group-item {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 4px;

                .group-index {
                  font-size: 12px;
                  color: var(--el-color-primary);
                  font-weight: 600;
                  min-width: 30px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 模式库对话框样式
.pattern-dialog {
  .pattern-library {
    max-height: 500px;
    overflow-y: auto;

    .pattern-category {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 12px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--el-border-color-light);
      }

      .pattern-list {
        .pattern-item {
          padding: 12px;
          border: 1px solid var(--el-border-color-light);
          border-radius: 6px;
          margin-bottom: 8px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--el-color-primary);
            background: var(--el-color-primary-light-9);
          }

          .pattern-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }

          .pattern-regex {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            color: var(--el-color-primary);
            background: var(--el-fill-color-light);
            padding: 4px 8px;
            border-radius: 3px;
            margin-bottom: 4px;
            word-break: break-all;
          }

          .pattern-desc {
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .regex-tester {
    .tool-content {
      .regex-section {
        .regex-options {
          flex-direction: column;
          gap: 8px;
        }
      }

      .match-stats {
        flex-direction: column;
        gap: 8px;
      }

      .match-details {
        .match-list {
          .match-item {
            .match-header {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
