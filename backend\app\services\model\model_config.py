"""
模型配置业务服务层
处理模型管理相关的业务逻辑
"""
from typing import List, Tuple, Optional, Dict, Any, Type
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.services.base import BaseService
from app.repositories.model.model_config import ModelConfigRepository
from app.schemas.model.model_config import (
    ModelConfigCreate, ModelConfigUpdate, ModelConfigResponse, ModelHealthCheckResponse,
    ModelCallRequest, ModelCallResponse
)
from app.schemas.common import SearchParams
from app.models.model.model_config import ModelConfig
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.encryption import encrypt_api_key
from backend.app.utils.clients.model_client import ModelClient
from app.utils.logger import setup_logger


logger = setup_logger()


class ModelConfigService(BaseService[ModelConfig, ModelConfigCreate, ModelConfigUpdate, ModelConfigResponse]):
    """模型配置业务服务"""

    def __init__(self, db: AsyncSession):
        repository = ModelConfigRepository(db)
        super().__init__(db, repository)

    @property
    def model_class(self) -> Type[ModelConfig]:
        """返回模型类"""
        return ModelConfig

    @property
    def response_schema_class(self) -> Type[ModelConfigResponse]:
        """返回响应Schema类"""
        return ModelConfigResponse

    # ==================== BaseService钩子方法重写 ====================

    async def _validate_before_create(self, create_data: ModelConfigCreate, **kwargs) -> None:
        """创建模型配置前的验证"""
        # 检查名称是否重复
        existing = await self.repository.get_by_name(create_data.name)
        if existing:
            raise_validation_error(f"模型名称 '{create_data.name}' 已存在")

    async def _validate_before_update(self, model: ModelConfig, update_data: ModelConfigUpdate, **kwargs) -> None:
        """更新模型配置前的验证"""
        # 如果要更新名称，检查是否重复
        if update_data.name and update_data.name != model.name:
            existing = await self.repository.get_by_name(update_data.name)
            if existing:
                raise_validation_error(f"模型名称 '{update_data.name}' 已存在")

    async def _build_list_filters(self, params: SearchParams) -> Dict[str, Any]:
        """构建模型列表查询过滤条件"""
        filters = {}
        if params.keyword:
            filters['keyword'] = params.keyword
        return filters

    # ==================== 重写基类钩子方法实现特殊业务逻辑 ====================

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前处理 - API Key加密"""
        # 加密API Key
        if 'api_key' in create_dict:
            api_key = create_dict.pop('api_key')
            create_dict['api_key_encrypted'] = encrypt_api_key(api_key)

        # 设置默认状态
        create_dict.update({
            'status': 'enabled',
            'health_status': 'unknown'
        })

        return create_dict

    # ==================== 模型配置特定的业务方法 ====================

    async def create_model_config(self,model_data: ModelConfigCreate,current_user: str) -> ModelConfigResponse:
        """创建模型配置 - 使用基类通用方法"""
        return await self.create(model_data, current_user)

    async def test_model_connection(
        self,
        model_data: ModelConfigCreate
    ) -> dict:
        """测试模型配置连接"""
        try:
            # 创建临时模型配置对象用于测试
            temp_config = ModelConfig(
                name=model_data.name,
                platform=model_data.platform,
                description=model_data.description,
                api_url=model_data.api_url,
                api_key_encrypted=model_data.api_key,  # 测试时直接使用原始API Key
                timeout_seconds=model_data.timeout_seconds,
                config=model_data.config,
                status='enabled',
                health_status='unknown'
            )

            # 创建客户端并测试连接
            client = self.client_factory.create_client(temp_config)

            # 执行健康检查
            test_result = await client.health_check()

            return {
                "success": test_result.get('is_healthy', False),
                "message": "连接测试成功" if test_result.get('is_healthy') else "连接测试失败",
                "details": test_result
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}",
                "details": None
            }

    async def _process_before_update(self, obj: ModelConfig, update_dict: Dict[str, Any]) -> Dict[str, Any]:
        """更新前处理 - API Key加密"""
        # 如果更新API Key，进行加密
        if 'api_key' in update_dict:
            api_key = update_dict.pop('api_key')
            update_dict['api_key_encrypted'] = encrypt_api_key(api_key)

        return update_dict

    async def update_model_config(
        self,
        model_id: int,
        model_data: ModelConfigUpdate,
        current_user: str
    ) -> ModelConfigResponse:
        """更新模型配置 - 使用基类通用方法"""
        return await self.update(model_id, model_data, current_user)

    async def list_model_configs(
        self,
        params: SearchParams,
        platform: Optional[str] = None,
        status: Optional[str] = None,
        health_status: Optional[str] = None
    ) -> Tuple[List[ModelConfigResponse], int]:
        """获取模型配置列表"""
        # 暂时使用原有的仓储方法，因为需要处理额外的过滤参数
        models, total = await self.repository.search_models(
            params, platform=platform, status=status, health_status=health_status
        )

        responses = [self._convert_to_response(model) for model in models]
        return responses, total

    async def get_model_config(self, model_id: int) -> ModelConfigResponse:
        """获取模型配置详情 - 使用基类通用方法"""
        return await self.get_by_id(model_id)

    async def delete_model_config(self, model_id: int) -> None:
        """删除模型配置 - 使用基类通用方法"""
        await self.delete(model_id)

    async def enable_model(self, model_id: int) -> ModelConfigResponse:
        """启用模型"""
        model = await self.repository.get(model_id)
        if not model:
            raise_not_found(f"模型配置不存在: {model_id}")

        model.enable()
        updated_model = await self.repository.update(db_obj=model, obj_in={})
        return self._convert_to_response(updated_model)

    async def disable_model(self, model_id: int) -> ModelConfigResponse:
        """停用模型"""
        model = await self.repository.get(model_id)
        if not model:
            raise_not_found(f"模型配置不存在: {model_id}")

        model.disable()
        updated_model = await self.repository.update(db_obj=model, obj_in={})
        return self._convert_to_response(updated_model)

    async def get_available_models(self) -> List[ModelConfigResponse]:
        """获取所有可用的模型配置"""
        models = await self.repository.get_available_models()
        return [self._convert_to_response(model) for model in models]

    async def get_platform_stats(self) -> Dict[str, Any]:
        """获取平台统计信息"""
        stats = await self.repository.get_platform_stats()
        
        # 计算总体统计
        total_models = sum(stat['total'] for stat in stats)
        total_enabled = sum(stat['enabled'] for stat in stats)
        total_healthy = sum(stat['healthy'] for stat in stats)
        
        return {
            'total_models': total_models,
            'total_enabled': total_enabled,
            'total_healthy': total_healthy,
            'platform_stats': stats
        }

    def _convert_to_response(self, model: ModelConfig) -> ModelConfigResponse:
        """转换模型为响应格式"""
        model_dict = {
            'id': model.id,
            'name': model.name,
            'platform': model.platform,
            'description': model.description,
            'api_url': model.api_url,
            'api_key_encrypted': model.api_key_encrypted,  # 将在Response中自动脱敏
            'timeout_seconds': model.timeout_seconds,
            'status': model.status,
            'health_status': model.health_status,
            'last_health_check': model.last_health_check,
            'model_name': model.model_name,
            'max_tokens': model.max_tokens,
            'prompt': model.prompt,
            'created_at': model.created_at,
            'updated_at': model.updated_at,
            'created_by': model.created_by,
            'updated_by': model.updated_by
        }
        
        return ModelConfigResponse(**model_dict)

    # ==================== 健康检查相关方法 ====================

    async def health_check_model(
        self,
        model_id: int,
        timeout_seconds: Optional[int] = None
    ) -> ModelHealthCheckResponse:
        """单个模型健康检查"""
        model = await self.repository.get(model_id)
        if not model:
            raise_not_found(f"模型配置不存在: {model_id}")

        # 创建客户端并执行健康检查
        client = ModelClient(model)

        check_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            if timeout_seconds:
                # 使用自定义超时
                original_timeout = client.timeout
                client.timeout = timeout_seconds

            result = await client.health_check()

            if timeout_seconds:
                # 恢复原始超时
                client.timeout = original_timeout

            # 更新模型健康状态
            health_status = 'healthy' if result['is_healthy'] else 'unhealthy'
            await self.repository.update_health_status(model_id, health_status, check_time)

            return ModelHealthCheckResponse(
                model_id=model_id,
                model_name=model.name,
                is_healthy=result['is_healthy'],
                response_time_ms=result.get('response_time_ms'),
                error_message=result.get('error_message'),
                check_time=check_time
            )

        except Exception as e:
            # 更新为不健康状态
            await self.repository.update_health_status(model_id, 'unhealthy', check_time)

            return ModelHealthCheckResponse(
                model_id=model_id,
                model_name=model.name,
                is_healthy=False,
                response_time_ms=None,
                error_message=str(e),
                check_time=check_time
            )

    async def batch_health_check(
        self,
        model_ids: Optional[List[int]] = None,
        timeout_seconds: Optional[int] = None
    ) -> List[ModelHealthCheckResponse]:
        """批量健康检查"""
        if model_ids:
            # 检查指定的模型
            models = []
            for model_id in model_ids:
                model = await self.repository.get(model_id)
                if model:
                    models.append(model)
        else:
            # 检查所有启用的模型
            models = await self.repository.get_enabled_models()

        results = []
        for model in models:
            try:
                result = await self.health_check_model(model.id, timeout_seconds)
                results.append(result)
            except Exception as e:
                logger.error(f"健康检查失败 - 模型ID: {model.id}, 错误: {str(e)}")
                results.append(ModelHealthCheckResponse(
                    model_id=model.id,
                    model_name=model.name,
                    is_healthy=False,
                    response_time_ms=None,
                    error_message=str(e),
                    check_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ))

        return results

    # ==================== 模型调用相关方法 ====================

    async def call_model(
        self,
        request: ModelCallRequest
    ) -> ModelCallResponse:
        """调用模型"""
        # 根据模型名称获取配置
        model = await self.repository.get_by_name(request.model_name)
        if not model:
            raise_not_found(f"模型不存在: {request.model_name}")

        if not model.is_enabled:
            raise_validation_error(f"模型已停用: {request.model_name}")

        # 创建客户端并调用模型
        client = ModelClient(model)

        try:
            result = await client.call_model(request.prompt, request.parameters)

            return ModelCallResponse(
                model_name=request.model_name,
                response=result.get('response'),
                response_time_ms=result.get('response_time_ms', 0),
                success=result.get('success', False),
                error_message=result.get('error_message')
            )

        except Exception as e:
            logger.error(f"模型调用失败 - 模型: {request.model_name}, 错误: {str(e)}")

            return ModelCallResponse(
                model_name=request.model_name,
                response=None,
                response_time_ms=0,
                success=False,
                error_message=str(e)
            )

        finally:
            await client.close()

    async def call_model_stream(
        self,
        request: ModelCallRequest
    ):
        """调用模型（流式）"""
        # 根据模型名称获取配置
        model = await self.repository.get_by_name(request.model_name)
        if not model:
            raise_not_found(f"模型不存在: {request.model_name}")

        if not model.is_enabled:
            raise_validation_error(f"模型已停用: {request.model_name}")

        # 创建客户端并调用模型
        client = ModelClient(model)

        try:
            async for chunk in client.call_model_stream(request.prompt, request.parameters):
                yield chunk

        finally:
            await client.close()
