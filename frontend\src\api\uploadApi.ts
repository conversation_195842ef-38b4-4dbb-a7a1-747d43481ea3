/**
 * 文件上传相关API
 */
import request from '@/utils/http'

// 上传响应数据类型
export interface UploadResponse {
  url: string
  filename: string
  content_type: string
  size: number
}

// 文件信息类型
export interface FileInfo {
  filename: string
  size: number
  created_at: number
  modified_at: number
}

/**
 * 文件上传服务类
 */
export class UploadService {
  /**
   * 上传头像
   * @param file 头像文件
   * @returns 上传结果
   */
  static async uploadAvatar(file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await request.post<Api.Common.BaseResponse>({
      url: '/api/upload/avatar',
      data: formData
      // 不要手动设置 Content-Type，让浏览器自动处理 multipart/form-data 的边界
    })
    return response
  }

  /**
   * 获取文件信息
   * @param fileUrl 文件URL
   * @returns 文件信息
   */
  static async getFileInfo(fileUrl: string): Promise<Api.Common.BaseResponse> {
    const response = await request.get<Api.Common.BaseResponse>({
      url: '/api/upload/info',
      params: { file_url: fileUrl }
    })
    return response
  }

  /**
   * 删除文件
   * @param fileUrl 文件URL
   * @returns 删除结果
   */
  static async deleteFile(fileUrl: string): Promise<Api.Common.BaseResponse> {
    const response = await request.del<Api.Common.BaseResponse>({
      url: '/api/upload/delete',
      params: { file_url: fileUrl }
    })
    return response
  }
} 