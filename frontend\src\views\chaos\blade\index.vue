<template>
  <div class="chaosblade-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>ChaosBlade部署管理</h2>
        <p class="header-desc">管理ChaosBlade工具的安装、配置和状态监控</p>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-area">
      <el-card>
        <div class="action-buttons">
          <el-button type="primary" @click="handleCheckStatus(false)" :loading="checking">
            <el-icon><Refresh /></el-icon>
            {{ checking ? '检查中...' : '检查状态' }}
          </el-button>
          <el-button type="success" @click="handleCheckVersion">
            <el-icon><InfoFilled /></el-icon>
            版本信息
          </el-button>
          <el-button type="warning" @click="handleInstallBlade" :disabled="selectedHosts.length === 0">
            <el-icon><Download /></el-icon>
            批量安装 ({{ selectedHosts.length }})
          </el-button>
        </div>

        <!-- 操作提示信息 -->
        <div class="operation-tips">
          <el-alert
            type="info"
            :closable="false"
          >
            <template #default>
              <div class="tips-content">
                <div class="tip-section">
                  <h4><el-icon><InfoFilled /></el-icon> 安装信息确认</h4>
                  <ul>
                    <li><strong>安装说明</strong>：通过SFTP上传安装包，ChaosBlade将安装到用户主目录 <code>~/chaosblade/</code></li>
                    <li><strong>版本信息</strong>：当前安装版本：ChaosBlade v1.7.4 支持Linux x86_64 (amd64) 架构</li>
                    <li><strong>目录权限</strong>：用户需要对主目录 <code>~</code> 具有读写权限</li>
                  </ul>
                </div>
                <div class="tip-section">
                  <h4><el-icon><InfoFilled /></el-icon> 操作注意事项</h4>
                  <ul>
                    <li><strong>状态检查</strong>：首次使用请先点击"检查状态"获取主机列表</li>
                    <li><strong>批量操作</strong>：可以选择多个主机进行批量安装</li>
                    <li><strong>重装卸载</strong>：卸载时会自动停止所有正在运行的故障注入；重装会覆盖现有安装，请谨慎操作</li>
                  </ul>
                </div>
              </div>
            </template>
          </el-alert>
        </div>

      </el-card>
    </div>


    <!-- 空状态提示 -->
    <div v-if="statusList.length === 0" class="empty-state">
      <el-card>
        <el-empty description="暂无主机状态数据">
          <template #image>
            <el-icon size="60" color="#909399"><InfoFilled /></el-icon>
          </template>
          <template #description>
            <p>请点击上方"检查状态"按钮获取主机ChaosBlade安装状态</p>
          </template>
          <el-button type="primary" @click="handleCheckStatus(false)" :loading="checking">
            <el-icon><Refresh /></el-icon>
            {{ checking ? '检查中...' : '立即检查' }}
          </el-button>
        </el-empty>
      </el-card>
    </div>

    <!-- 主机状态列表 -->
    <div class="hosts-section" v-if="statusList.length > 0">
      <el-card class="art-table-card" shadow="never">
        <!-- 表格 -->
        <ArtTable
          :loading="checking"
          :data="statusList"
          :columns="columns"
          row-key="host_id"
          @selection-change="handleSelectionChange"
        >
          <!-- 安装状态插槽 -->
          <template #installed="{ row }">
            <el-tag :type="row.installed ? 'success' : 'danger'">
              {{ row.installed ? '已安装' : '未安装' }}
            </el-tag>
          </template>

          <!-- 版本插槽 -->
          <template #version="{ row }">
            <span v-if="row.version">{{ row.version }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>

          <!-- 安装路径插槽 -->
          <template #install_path="{ row }">
            <span v-if="row.install_path" class="font-mono text-sm">{{ row.install_path }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>

          <!-- 最后检查时间插槽 -->
          <template #last_check_time="{ row }">
            {{ formatDateTime(row.last_check_time) }}
          </template>

          <!-- 错误信息插槽 -->
          <template #error_message="{ row }">
            <span v-if="row.error_message" class="text-red-500 text-sm">{{ row.error_message }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>

          <!-- 操作插槽 -->
          <template #operation="{ row }">
            <div class="flex gap-1">
              <el-button
                v-if="!row.installed"
                size="small"
                type="primary"
                @click="handleInstallSingle(row)"
              >
                安装
              </el-button>
              <el-button
                v-if="row.installed"
                size="small"
                type="success"
                @click="handleTestConnection(row)"
              >
                测试
              </el-button>
              <el-button
                v-if="row.installed"
                size="small"
                type="warning"
                @click="handleReinstall(row)"
              >
                重装
              </el-button>
              <el-button
                v-if="row.installed"
                size="small"
                type="danger"
                @click="handleUninstallSingle(row)"
              >
                卸载
              </el-button>
              <el-dropdown @command="(command) => handleDropdownCommand(command, row)">
                <el-button size="small" type="info">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="test">执行测试</el-dropdown-item>
                    <el-dropdown-item command="logs">查看日志</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </ArtTable>
      </el-card>
    </div>

    <!-- 版本信息对话框 -->
    <el-dialog
      v-model="versionDialogVisible"
      title="ChaosBlade 版本信息"
      width="700px"
      destroy-on-close
    >
      <div class="version-dialog" v-if="versionInfo">
        <div class="version-section">
          <h4>支持版本</h4>
          <div class="version-item">
            <span class="label">推荐版本：</span>
            <span class="value">{{ versionInfo.supported_version }}</span>
          </div>
          <div class="version-item">
            <span class="label">下载地址：</span>
            <el-link :href="versionInfo.download_url" target="_blank" type="primary">
              {{ versionInfo.download_url }}
            </el-link>
          </div>
          <div class="version-item">
            <span class="label">文档地址：</span>
            <el-link :href="versionInfo.documentation" target="_blank" type="primary">
              {{ versionInfo.documentation }}
            </el-link>
          </div>
        </div>

        <div class="fault-types-section">
          <h4>支持的故障类型</h4>
          <div class="fault-types-grid">
            <div
              v-for="faultType in versionInfo.fault_types"
              :key="faultType.type"
              class="fault-type-card"
            >
              <div class="fault-type-header">
                <el-tag :type="getFaultTypeTagType(faultType.type)">
                  {{ faultType.type.toUpperCase() }}
                </el-tag>
                <span class="fault-type-name">{{ faultType.description }}</span>
              </div>
              <div class="fault-scenarios">
                <el-tag
                  v-for="scenario in faultType.scenarios"
                  :key="scenario"
                  size="small"
                  effect="plain"
                >
                  {{ scenario }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="versionDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 安装对话框 -->
    <el-dialog
      v-model="installDialogVisible"
      title="安装 ChaosBlade"
      width="500px"
      destroy-on-close
    >
      <div class="install-dialog">
        <el-form :model="installForm" label-width="120px">

          <el-form-item label="目标主机">
            <el-select
              v-model="installForm.host_ids"
              multiple
              placeholder="选择主机（空则安装到所有主机）"
              style="width: 100%"
            >
              <el-option
                v-for="host in availableHosts"
                :key="host.host_id"
                :label="host.host_name"
                :value="host.host_id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="强制重装">
            <el-switch v-model="installForm.force_reinstall" />
            <div class="form-tip">启用后将覆盖已安装的版本</div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="installDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmInstall" :loading="installing">
          开始安装
        </el-button>
      </template>
    </el-dialog>

    <!-- 测试结果对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="连接测试结果"
      width="600px"
      destroy-on-close
    >
      <div class="test-dialog" v-if="testResult">
        <div class="test-result">
          <div class="result-status">
            <el-tag :type="testResult.connected ? 'success' : 'danger'" size="large">
              {{ testResult.connected ? '连接成功' : '连接失败' }}
            </el-tag>
          </div>
          <div class="result-details">
            <div class="detail-item" v-if="testResult.version">
              <span class="label">版本：</span>
              <span class="value">{{ testResult.version }}</span>
            </div>
            <div class="detail-item">
              <span class="label">消息：</span>
              <span class="value">{{ testResult.message }}</span>
            </div>
            <div class="detail-item" v-if="testResult.error">
              <span class="label">错误：</span>
              <span class="value error-text">{{ testResult.error }}</span>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="testDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  InfoFilled, Download, Refresh, ArrowDown
} from '@element-plus/icons-vue'
import { useChaosStore } from '@/store/business/chaos/index'
import ChaosService from '@/api/chaosApi'
import ArtTable from '@/components/core/tables/art-table/index.vue'
import { useTableColumns } from '@/composables/useTableColumns'
import { safeExecute } from '@/utils/errorHandler'
import type { ChaosBladeStatusResponse, ChaosBladeInstallRequest } from '@/types/api/chaos'
import type { ColumnOption } from '@/types/component'

const chaosStore = useChaosStore()

// 响应式数据
const checking = ref(false)
const installing = ref(false)
const statusList = ref<ChaosBladeStatusResponse[]>([])
const selectedHosts = ref<ChaosBladeStatusResponse[]>([])

// 表格列配置
const columnsFactory = (): ColumnOption[] => [
  { type: 'selection', width: 55 },
  { prop: 'host_name', label: '主机名称', width: 150 },
  { prop: 'host_address', label: '主机地址', width: 140 },
  { prop: 'host_id', label: '主机ID', width: 100 },
  {
    prop: 'installed',
    label: '安装状态',
    width: 120,
    useSlot: true
  },
  {
    prop: 'version',
    label: '版本',
    width: 120,
    useSlot: true
  },
  {
    prop: 'install_path',
    label: '安装路径',
    minWidth: 200,
    useSlot: true
  },
  {
    prop: 'last_check_time',
    label: '最后检查',
    width: 160,
    useSlot: true
  },
  {
    prop: 'error_message',
    label: '错误信息',
    minWidth: 200,
    useSlot: true
  },
  {
    prop: 'operation',
    label: '操作',
    width: 280,
    fixed: 'right',
    useSlot: true
  }
]

// 使用表格列管理
const { columns } = useTableColumns(columnsFactory)

// 对话框状态
const versionDialogVisible = ref(false)
const installDialogVisible = ref(false)
const testDialogVisible = ref(false)
const versionInfo = ref<any>(null)
const testResult = ref<any>(null)

// 安装表单
const installForm = reactive<ChaosBladeInstallRequest>({
  env_id: 0,
  host_ids: [],
  force_reinstall: false
})

// 计算属性

const availableHosts = computed(() => {
  return statusList.value.map(status => ({
    host_id: status.host_id,
    host_name: status.host_name || `Host-${status.host_id}`
  }))
})

// 生命周期
onMounted(() => {
  // 移除自动检查，用户需要手动点击检查按钮
})

// 方法


const handleCheckStatus = async (quickCheck: boolean = true) => {
  checking.value = true

  const { data, success } = await safeExecute(
    () => ChaosService.checkAllChaosBladeStatus(quickCheck),
    {
      showMessage: false,  // 不自动显示错误消息，我们手动处理
      customMessage: '检查状态失败'
    }
  )

  if (success && data) {
    statusList.value = data || []
    const checkType = quickCheck ? '快速' : '完整'
    ElMessage.success(`${checkType}检查完成，共检查${data.length}台主机`)
  } else {
    statusList.value = []
    ElMessage.error('检查状态失败')
  }

  checking.value = false
}



const handleCheckVersion = async () => {
  const { data, success } = await safeExecute(
    () => chaosStore.getChaosBladeVersion(),
    { showMessage: false }
  )

  if (success && data) {
    versionInfo.value = data
    versionDialogVisible.value = true
  } else {
    ElMessage.error('获取版本信息失败')
  }
}

// 卸载单个主机的ChaosBlade
const handleUninstallSingle = async (host: ChaosBladeStatusResponse) => {
  try {
    await ElMessageBox.confirm(
      `确定要卸载主机 ${host.host_name} 上的ChaosBlade吗？`,
      '确认卸载',
      { type: 'warning' }
    )

    const uninstallData: ChaosBladeInstallRequest = {
      env_id: 0,
      host_ids: [host.host_id],
      force_reinstall: false
    }

    const { data, success } = await safeExecute(
      () => chaosStore.uninstallChaosBlade(uninstallData),
      { showMessage: false }
    )

    if (success && data) {
      if (data.success_count > 0) {
        ElMessage.success('卸载成功')
        await handleCheckStatus()
      } else {
        const failedResult = data.results?.[0]
        const errorMessage = failedResult?.message || '未知错误'
        ElMessage.error(`卸载失败: ${errorMessage}`)
      }
    } else {
      ElMessage.error('卸载失败')
    }
  } catch (error) {
    // 用户取消操作，不显示错误
  }
}

const handleInstallBlade = () => {
  if (selectedHosts.value.length === 0) {
    ElMessage.warning('请先选择要安装的主机')
    return
  }

  installForm.env_id = 0  // 不再需要环境ID
  installForm.host_ids = selectedHosts.value.map(h => h.host_id)
  installForm.force_reinstall = false
  installDialogVisible.value = true
}

const confirmInstall = async () => {
  installing.value = true

  const { data, success } = await safeExecute(
    () => chaosStore.installChaosBlade(installForm),
    { showMessage: false }  // 不自动显示错误消息
  )

  if (success && data) {
    // 检查实际安装结果
    if (data.success_count > 0) {
      ElMessage.success(`安装完成，成功: ${data.success_count}/${data.total_hosts}`)
      installDialogVisible.value = false
      await handleCheckStatus()
    } else {
      // 虽然接口调用成功，但实际安装失败
      const failedResults = data.results?.filter((r: any) => !r.success) || []
      const errorMessages = failedResults.map((r: any) => r.message).join('; ')
      ElMessage.error(`安装失败: ${errorMessages || '未知错误'}`)
    }
  } else {
    ElMessage.error('安装失败')
  }

  installing.value = false
}

const handleInstallSingle = async (host: ChaosBladeStatusResponse) => {
  const installData: ChaosBladeInstallRequest = {
    env_id: 0,  // 不再需要环境ID
    host_ids: [host.host_id],
    force_reinstall: false
  }

  const { data, success } = await safeExecute(
    () => chaosStore.installChaosBlade(installData),
    { showMessage: false }
  )

  if (success && data) {
    // 检查实际安装结果
    if (data.success_count > 0) {
      ElMessage.success('安装成功')
      await handleCheckStatus()
    } else {
      const failedResult = data.results?.[0]
      const errorMessage = failedResult?.message || '未知错误'
      ElMessage.error(`安装失败: ${errorMessage}`)
    }
  } else {
    ElMessage.error('安装失败')
  }
}

const handleReinstall = async (host: ChaosBladeStatusResponse) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新安装主机 ${host.host_name} 上的ChaosBlade吗？`,
      '确认重新安装',
      { type: 'warning' }
    )

    const installData: ChaosBladeInstallRequest = {
      env_id: 0,  // 不再需要环境ID
      host_ids: [host.host_id],
      force_reinstall: true
    }

    const { data, success } = await safeExecute(
      () => chaosStore.installChaosBlade(installData),
      { showMessage: false }
    )

    if (success && data) {
      // 检查实际安装结果
      if (data.success_count > 0) {
        ElMessage.success('重新安装成功')
        await handleCheckStatus()
      } else {
        const failedResult = data.results?.[0]
        const errorMessage = failedResult?.message || '未知错误'
        ElMessage.error(`重新安装失败: ${errorMessage}`)
      }
    } else {
      ElMessage.error('重新安装失败')
    }
  } catch (error) {
    // 用户取消操作，不显示错误
  }
}

const handleTestConnection = async (host: ChaosBladeStatusResponse) => {
  // 使用环境ID调用环境管理的连接测试接口
  const { data, success } = await safeExecute(
    () => ChaosService.testChaosBladeConnection(host.host_id),
    { showMessage: false }
  )

  if (success && data) {
    testResult.value = data
    testDialogVisible.value = true
  } else {
    ElMessage.error('测试连接失败')
  }
}


const handleSelectionChange = (selection: ChaosBladeStatusResponse[]) => {
  selectedHosts.value = selection
}

const handleDropdownCommand = (command: string, host: ChaosBladeStatusResponse) => {
  switch (command) {
    case 'test':
      handleExecuteTest(host)
      break
    case 'logs':
      handleViewLogs(host)
      break
  }
}

const handleExecuteTest = (_host: ChaosBladeStatusResponse) => {
  ElMessage.info('执行测试功能开发中...')
}

const handleViewLogs = (_host: ChaosBladeStatusResponse) => {
  ElMessage.info('查看日志功能开发中...')
}

// 工具方法
const getFaultTypeTagType = (type: string) => {
  const types: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary'> = {
    cpu: 'danger',
    memory: 'warning',
    network: 'info',
    disk: 'success',
    process: 'primary',
    k8s: 'info'
  }
  return types[type] || 'info'
}

const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.chaosblade-management {
  padding: 10px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-area {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.empty-state {
  margin-bottom: 20px;
}

.empty-state .el-empty {
  padding: 40px 20px;
}

.operation-tips {
  margin-top: 20px;
}

.tips-content {
  line-height: 1.6;
}

.tip-section {
  margin-bottom: 20px;
}

.tip-section:last-child {
  margin-bottom: 0;
}

.tip-section h4 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tip-section ul {
  margin: 0;
  padding-left: 20px;
}

.tip-section li {
  margin-bottom: 6px;
  color: #606266;
  font-size: 13px;
}

.tip-section li:last-child {
  margin-bottom: 0;
}

.tip-section strong {
  color: #303133;
  font-weight: 600;
}

.tip-section code {
  background: #f1f2f3;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e6a23c;
}



.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}


.path-text {
  font-family: monospace;
  font-size: 12px;
}

.error-text {
  color: #f56c6c;
}

.version-dialog {
  padding: 10px 0;
}

.version-section {
  margin-bottom: 30px;
}

.version-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.version-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.version-item .label {
  width: 100px;
  color: #666;
  flex-shrink: 0;
}

.version-item .value {
  color: #333;
  flex: 1;
}

.fault-types-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.fault-types-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.fault-type-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
}

.fault-type-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.fault-type-name {
  font-weight: 500;
  color: #333;
}

.fault-scenarios {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.install-dialog {
  padding: 10px 0;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.test-dialog {
  padding: 10px 0;
}

.test-result {
  text-align: center;
}

.result-status {
  margin-bottom: 20px;
}

.result-details {
  text-align: left;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.detail-item .label {
  width: 80px;
  color: #666;
  flex-shrink: 0;
}

.detail-item .value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* ArtTable 相关样式 */
.art-table-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.art-table-card :deep(.el-card__body) {
  padding: 0;
}

/* 表格内容样式 */
.text-gray-400 {
  color: #9ca3af;
}

.text-red-500 {
  color: #ef4444;
}

.font-mono {
  font-family: 'Courier New', monospace;
}

.text-sm {
  font-size: 0.875rem;
}

.flex {
  display: flex;
}

.gap-1 {
  gap: 0.25rem;
}





</style>
