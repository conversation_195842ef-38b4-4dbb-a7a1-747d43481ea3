<template>
  <div class="model-chat-window">
    <ElDialog
      v-model="dialogVisible"
      title=""
      width="1000px"
      align-center
      :show-close="false"
      @opened="handleDialogOpened"
      @closed="handleDialogClosed"
    >
      <div class="header">
        <div class="header-left">
          <span class="name">AI 模型对话</span>
          <div class="status">
            <div class="dot" :class="{ online: selectedModel, offline: !selectedModel }"></div>
            <span class="status-text">{{ selectedModel ? `已连接: ${selectedModel}` : '未选择模型' }}</span>
          </div>
        </div>
        <div class="header-center">
          <ElSelect
            v-model="selectedModel"
            placeholder="请选择模型"
            style="width: 250px"
            :loading="modelsLoading"
            filterable
            @change="handleModelChange"
          >
            <ElOption
              v-for="model in availableModels"
              :key="model.id"
              :label="`${model.name} (${model.platform})`"
              :value="model.name"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ model.name }}</span>
                <ElTag :type="getPlatformTagType(model.platform)" size="small">
                  {{ getPlatformDisplayName(model.platform) }}
                </ElTag>
              </div>
            </ElOption>
          </ElSelect>
        </div>
        <div class="header-right">
          <ElButton @click="clearMessages" size="small" type="warning" plain>
            清空对话
          </ElButton>
          <ElIcon class="icon-close" :size="20" @click="closeChat">
            <Close />
          </ElIcon>
        </div>
      </div>

      <div class="chat-container">
        <!-- 聊天消息区域 -->
        <div class="chat-messages" ref="messageContainer">
          <div v-if="messages.length === 0" class="empty-chat">
            <ElEmpty description="开始与 AI 对话吧！" />
          </div>
          <template v-for="(message, index) in messages" :key="index">
            <div :class="['message-item', message.role === 'user' ? 'message-right' : 'message-left']">
              <ElAvatar
                :size="32"
                :src="message.role === 'user' ? userAvatar : aiAvatarSrc"
                class="message-avatar"
              />
              <div class="message-content">
                <div class="message-info">
                  <span class="sender-name">{{ message.role === 'user' ? '我' : selectedModel || 'AI' }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-text" v-html="formatMessageContent(message.content)"></div>
                <div v-if="message.role === 'assistant'" class="message-actions">
                  <ElButton size="small" text @click="copyMessage(message.content)">
                    <ElIcon><CopyDocument /></ElIcon>
                    复制
                  </ElButton>
                  <ElButton size="small" text @click="regenerateMessage(index)">
                    <ElIcon><Refresh /></ElIcon>
                    重新生成
                  </ElButton>
                </div>
              </div>
            </div>
          </template>
          
          <!-- 正在输入指示器 -->
          <div v-if="isStreaming" class="message-item message-left typing">
            <ElAvatar :size="32" :src="aiAvatarSrc" class="message-avatar" />
            <div class="message-content">
              <div class="message-info">
                <span class="sender-name">{{ selectedModel || 'AI' }}</span>
                <span class="message-time">{{ formatTime(new Date()) }}</span>
              </div>
              <div class="message-text streaming-text">
                {{ streamingContent }}
                <span class="cursor">|</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天输入区域 -->
        <div class="chat-input">
          <ElInput
            v-model="inputMessage"
            type="textarea"
            :rows="3"
            placeholder="输入您的消息..."
            :disabled="!selectedModel || isStreaming"
            @keydown.ctrl.enter="sendMessage"
            resize="none"
            maxlength="2000"
            show-word-limit
          />
          <div class="chat-input-actions">
            <div class="left">
              <span class="tip">按 Ctrl + Enter 快速发送</span>
            </div>
            <ElButton
              type="primary"
              @click="sendMessage"
              :disabled="!inputMessage.trim() || !selectedModel || isStreaming"
              :loading="isStreaming"
            >
              <ElIcon><Promotion /></ElIcon>
              发送
            </ElButton>
          </div>
        </div>
      </div>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Close, CopyDocument, Refresh, Promotion } from '@element-plus/icons-vue'
import { useModelStore } from '@/store/business/model/index'
import { MODEL_PLATFORM_CONFIG } from '@/types/api/model'

import meAvatar from '@/assets/img/avatar/avatar5.webp'
import aiAvatar from '@/assets/img/avatar/avatar10.webp'

defineOptions({ name: 'ModelChatWindow' })

// 消息接口
interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

// 组件属性
interface Props {
  visible: boolean
}

// 组件事件
interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const modelStore = useModelStore()

// 常量定义
const SCROLL_DELAY = 100

// 组件状态
const modelsLoading = ref(false)
const isStreaming = ref(false)
const streamingContent = ref('')

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 可用模型列表
const availableModels = computed(() => modelStore.availableModels)

// 聊天相关状态
const selectedModel = ref('')
const inputMessage = ref('')
const messages = ref<ChatMessage[]>([])
const messageContainer = ref<HTMLElement | null>(null)

// 头像
const userAvatar = meAvatar
const aiAvatarSrc = aiAvatar

// 获取平台标签类型
const getPlatformTagType = (platform: string) => {
  return MODEL_PLATFORM_CONFIG[platform as keyof typeof MODEL_PLATFORM_CONFIG]?.type || 'default'
}

// 获取平台显示名称
const getPlatformDisplayName = (platform: string) => {
  return MODEL_PLATFORM_CONFIG[platform as keyof typeof MODEL_PLATFORM_CONFIG]?.text || platform
}

// 工具函数
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const scrollToBottom = (): void => {
  nextTick(() => {
    setTimeout(() => {
      if (messageContainer.value) {
        messageContainer.value.scrollTop = messageContainer.value.scrollHeight
      }
    }, SCROLL_DELAY)
  })
}

// 格式化消息内容（支持 Markdown 等）
const formatMessageContent = (content: string): string => {
  // 简单的换行处理
  return content.replace(/\n/g, '<br>')
}

// 复制消息
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('消息已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 重新生成消息
const regenerateMessage = async (index: number) => {
  if (index === 0 || messages.value[index - 1].role !== 'user') {
    ElMessage.warning('无法重新生成此消息')
    return
  }
  
  // 删除当前AI消息及之后的所有消息
  messages.value = messages.value.slice(0, index)
  
  // 重新发送上一条用户消息
  const lastUserMessage = messages.value[index - 1].content
  await callModel(lastUserMessage)
}

// 模型选择变化处理
const handleModelChange = (modelName: string) => {
}

// 清空消息
const clearMessages = () => {
  messages.value = []
  streamingContent.value = ''
}

// 发送消息
const sendMessage = async () => {
  const text = inputMessage.value.trim()
  if (!text || !selectedModel.value || isStreaming.value) return

  // 添加用户消息
  const userMessage: ChatMessage = {
    role: 'user',
    content: text,
    timestamp: new Date()
  }
  
  messages.value.push(userMessage)
  inputMessage.value = ''
  scrollToBottom()

  // 调用模型
  await callModel(text)
}

// 调用模型（流式）
const callModel = async (prompt: string) => {
  if (!selectedModel.value) return

  try {
    isStreaming.value = true
    streamingContent.value = ''
    scrollToBottom()

    // 实现真实的 SSE 流式调用
    await callModelStream(prompt)

  } catch (error) {
    ElMessage.error('模型调用失败，请重试')

    // 添加错误消息
    const errorMessage: ChatMessage = {
      role: 'assistant',
      content: `抱歉，模型调用失败：${error instanceof Error ? error.message : '未知错误'}`,
      timestamp: new Date()
    }
    messages.value.push(errorMessage)
  } finally {
    isStreaming.value = false
    streamingContent.value = ''
  }
}

// 真实的流式调用实现
const callModelStream = async (prompt: string) => {
  const requestData = {
    model_name: selectedModel.value,
    prompt: prompt,
    parameters: {}
  }

  // 创建 SSE 连接
  const response = await fetch('http://localhost:8000/api/model/call/stream', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestData)
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const reader = response.body?.getReader()
  if (!reader) {
    throw new Error('无法获取响应流')
  }

  const decoder = new TextDecoder()
  let buffer = ''

  try {
    while (true) {
      const { done, value } = await reader.read()

      if (done) break

      // 解码数据
      buffer += decoder.decode(value, { stream: true })

      // 处理 SSE 数据
      const lines = buffer.split('\n')
      buffer = lines.pop() || '' // 保留不完整的行

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6))

            if (data.type === 'content' && data.content) {
              streamingContent.value += data.content
              scrollToBottom()
            } else if (data.type === 'done' || data.finish_reason) {
              // 流式响应结束
              break
            } else if (data.type === 'error') {
              throw new Error(data.message || '模型调用出错')
            }
          } catch (parseError) {
            console.warn('解析 SSE 数据失败:', parseError)
          }
        }
      }
    }
  } finally {
    reader.releaseLock()
  }

  // 添加完整的AI消息
  if (streamingContent.value) {
    const aiMessage: ChatMessage = {
      role: 'assistant',
      content: streamingContent.value,
      timestamp: new Date()
    }

    messages.value.push(aiMessage)
    scrollToBottom()
  }
}

// 加载可用模型
const loadAvailableModels = async () => {
  try {
    modelsLoading.value = true
    await modelStore.fetchAvailableModels()
  } catch (error) {
  } finally {
    modelsLoading.value = false
  }
}

// 对话框打开处理
const handleDialogOpened = () => {
  loadAvailableModels()
  scrollToBottom()
}

// 对话框关闭处理
const handleDialogClosed = () => {
  // 可以选择是否清空对话
}

// 聊天窗口控制方法
const openChat = (): void => {
  dialogVisible.value = true
}

const closeChat = (): void => {
  dialogVisible.value = false
}

// 暴露方法给父组件
defineExpose({
  openChat,
  closeChat
})

// 生命周期
onMounted(() => {
  scrollToBottom()
})
</script>

<style lang="scss" scoped>
@use './style';
</style>
