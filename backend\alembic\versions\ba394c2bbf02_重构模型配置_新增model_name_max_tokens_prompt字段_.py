"""重构模型配置：新增model_name、max_tokens、prompt字段，删除config字段

Revision ID: ba394c2bbf02
Revises: 1a3ebae7013a
Create Date: 2025-07-28 19:43:29.715993

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ba394c2bbf02'
down_revision: Union[str, None] = '1a3ebae7013a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # 先添加可空的字段
    op.add_column('model_config', sa.Column('model_name', sa.String(length=100), nullable=True, comment='实际模型名称'))
    op.add_column('model_config', sa.Column('max_tokens', sa.Integer(), nullable=True, comment='最大令牌数'))
    op.add_column('model_config', sa.Column('prompt', sa.Text(), nullable=True, comment='系统提示词'))

    # 更新现有数据的默认值
    op.execute("UPDATE model_config SET model_name = 'deepseek-chat' WHERE platform = 'deepseek' AND model_name IS NULL")
    op.execute("UPDATE model_config SET model_name = 'qwen-turbo' WHERE platform = 'qwen' AND model_name IS NULL")
    op.execute("UPDATE model_config SET model_name = 'local-model' WHERE platform = 'local' AND model_name IS NULL")
    op.execute("UPDATE model_config SET model_name = 'custom-model' WHERE model_name IS NULL")
    op.execute("UPDATE model_config SET max_tokens = 2048 WHERE max_tokens IS NULL")

    # 将 model_name 设为非空
    op.alter_column('model_config', 'model_name',
                   existing_type=sa.String(length=100),
                   nullable=False)

    op.alter_column('model_config', 'platform',
               existing_type=mysql.VARCHAR(length=50),
               comment='所属平台（local/deepseek/qwen等）',
               existing_comment='所属平台（openai/claude/local等）',
               existing_nullable=False)
    op.alter_column('model_config', 'api_key_encrypted',
               existing_type=mysql.VARCHAR(length=255),
               nullable=True,
               comment='加密的API Key（本地模型可为空）',
               existing_comment='加密的API Key')
    op.drop_column('model_config', 'config')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('model_config', sa.Column('config', mysql.JSON(), nullable=True, comment='平台特定配置（JSON格式）'))
    op.alter_column('model_config', 'api_key_encrypted',
               existing_type=mysql.VARCHAR(length=255),
               nullable=False,
               comment='加密的API Key',
               existing_comment='加密的API Key（本地模型可为空）')
    op.alter_column('model_config', 'platform',
               existing_type=mysql.VARCHAR(length=50),
               comment='所属平台（openai/claude/local等）',
               existing_comment='所属平台（local/deepseek/qwen等）',
               existing_nullable=False)
    op.drop_column('model_config', 'prompt')
    op.drop_column('model_config', 'max_tokens')
    op.drop_column('model_config', 'model_name')
    # ### end Alembic commands ###
