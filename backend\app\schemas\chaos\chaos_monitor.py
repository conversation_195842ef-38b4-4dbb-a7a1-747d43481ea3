"""
混沌测试监控数据Schema
"""
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, validator


class ChaosMonitorDataBase(BaseModel):
    """监控数据基础Schema"""
    environment_id: int = Field(..., description="环境ID")
    host_id: int = Field(..., description="主机ID")
    metric_type: str = Field(..., description="指标类型")
    metric_value: float = Field(..., description="指标值")
    collected_at: datetime = Field(..., description="采集时间")


class ChaosMonitorDataCreate(ChaosMonitorDataBase):
    """创建监控数据Schema"""
    
    @validator('metric_type')
    def validate_metric_type(cls, v):
        allowed_types = ['cpu', 'memory', 'network_rx', 'network_tx', 'disk_usage', 'disk_io_read', 'disk_io_write']
        if v not in allowed_types:
            raise ValueError(f'指标类型必须是以下之一: {", ".join(allowed_types)}')
        return v
    
    @validator('metric_value')
    def validate_metric_value(cls, v):
        if v < 0:
            raise ValueError('指标值不能为负数')
        return v


class ChaosMonitorDataUpdate(BaseModel):
    """更新监控数据Schema"""
    metric_value: Optional[float] = Field(None, description="指标值")


class ChaosMonitorDataResponse(ChaosMonitorDataBase):
    """监控数据响应Schema"""
    id: int = Field(..., description="数据ID")
    
    class Config:
        from_attributes = True


class ChaosMonitorConfig(BaseModel):
    """监控配置Schema"""
    enable_monitoring: bool = Field(True, description="是否启用监控")
    monitor_duration: str = Field("auto", description="监控时长模式: auto/custom")
    custom_monitor_duration: Optional[int] = Field(None, description="自定义监控时长(秒)")
    collection_interval: int = Field(30, description="采集间隔(秒)")
    metrics: List[str] = Field(
        default=['cpu', 'memory', 'network_rx', 'disk_usage'],
        description="监控指标列表"
    )
    
    @validator('collection_interval')
    def validate_interval(cls, v):
        if v < 10 or v > 300:
            raise ValueError('采集间隔必须在10-300秒之间')
        return v
    
    @validator('custom_monitor_duration')
    def validate_duration(cls, v, values):
        if values.get('monitor_duration') == 'custom' and (v is None or v < 60):
            raise ValueError('自定义监控时长不能少于60秒')
        return v


class ChaosMonitorStartRequest(BaseModel):
    """启动监控请求Schema"""
    environment_id: int = Field(..., description="环境ID")
    host_id: int = Field(..., description="主机ID")
    config: Optional[ChaosMonitorConfig] = Field(None, description="监控配置")


class ChaosMonitorStopRequest(BaseModel):
    """停止监控请求Schema"""
    environment_id: int = Field(..., description="环境ID")
    host_id: int = Field(..., description="主机ID")


class ChaosMonitorStatusResponse(BaseModel):
    """监控状态响应Schema"""
    environment_id: int = Field(..., description="环境ID")
    host_id: int = Field(..., description="主机ID")
    status: str = Field(..., description="监控状态: not_started/running/stopped/error")
    start_time: Optional[datetime] = Field(None, description="监控开始时间")
    data_points: int = Field(0, description="数据点数量")
    last_collection_time: Optional[datetime] = Field(None, description="最后采集时间")


class ChaosMonitorDataQueryRequest(BaseModel):
    """监控数据查询请求Schema"""
    environment_id: int = Field(..., description="环境ID")
    host_id: int = Field(..., description="主机ID")
    hours: int = Field(1, description="查询最近几小时的数据")
    metric_types: Optional[List[str]] = Field(None, description="指标类型过滤")
    
    @validator('hours')
    def validate_hours(cls, v):
        if v < 1 or v > 24:
            raise ValueError('查询时间范围必须在1-24小时之间')
        return v


class ChaosMonitorChartData(BaseModel):
    """监控图表数据Schema"""
    metric_type: str = Field(..., description="指标类型")
    metric_name: str = Field(..., description="指标显示名称")
    unit: str = Field(..., description="单位")
    data: List[float] = Field(..., description="数据值列表")
    timestamps: List[str] = Field(..., description="时间戳列表")
    current_value: float = Field(..., description="当前值")
    avg_value: float = Field(..., description="平均值")
    max_value: float = Field(..., description="最大值")
    min_value: float = Field(..., description="最小值")


class ChaosMonitorDataResponse(BaseModel):
    """监控数据响应Schema"""
    environment_id: int = Field(..., description="环境ID")
    host_id: int = Field(..., description="主机ID")
    host_info: Dict[str, Any] = Field(..., description="主机信息")
    monitor_status: ChaosMonitorStatusResponse = Field(..., description="监控状态")
    charts: List[ChaosMonitorChartData] = Field(..., description="图表数据")
    summary: Dict[str, Any] = Field(..., description="数据摘要")


class ChaosMonitorSummary(BaseModel):
    """监控数据摘要Schema"""
    total_data_points: int = Field(..., description="总数据点数")
    monitoring_duration: int = Field(..., description="监控时长(分钟)")
    host_count: int = Field(..., description="监控主机数量")
    metrics_summary: Dict[str, Dict[str, float]] = Field(..., description="指标摘要统计")
    start_time: Optional[datetime] = Field(None, description="监控开始时间")
    end_time: Optional[datetime] = Field(None, description="监控结束时间")
