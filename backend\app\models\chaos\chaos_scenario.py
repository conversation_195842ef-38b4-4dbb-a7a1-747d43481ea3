"""
混沌测试故障场景模板数据模型
"""
from typing import Dict, Any, List, Optional

from sqlalchemy import Column, String, Text, JSON, Boolean, Integer
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class ChaosScenario(BaseModel):
    """
    故障场景模板模型
    定义可重用的故障注入场景配置
    """
    __tablename__ = "chaos_scenarios"

    # 基本信息
    name = Column(String(100), nullable=False, index=True, comment="场景名称")
    fault_type = Column(String(50), nullable=False, index=True, comment="故障类型：cpu/memory/network/disk/process/k8s")
    description = Column(Text, nullable=True, comment="场景描述")
    
    # 模板配置
    default_params = Column(JSON, nullable=False, comment="默认参数配置")
    param_schema = Column(JSON, nullable=False, comment="参数结构定义和验证规则")
    
    # 模板属性
    is_builtin = Column(Boolean, default=False, comment="是否内置模板")
    is_active = Column(Boolean, default=True, comment="是否启用")
    category = Column(String(50), nullable=True, comment="场景分类")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    # 标签和分类
    tags = Column(String(500), nullable=True, comment="场景标签，逗号分隔")

    def __repr__(self) -> str:
        return f"<ChaosScenario(id={self.id}, name={self.name}, fault_type={self.fault_type})>"

    @property
    def tag_list(self) -> List[str]:
        """获取标签列表"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(",") if tag.strip()]

    def add_tag(self, tag: str) -> None:
        """添加标签"""
        current_tags = self.tag_list
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags = ",".join(current_tags)

    def remove_tag(self, tag: str) -> None:
        """移除标签"""
        current_tags = self.tag_list
        if tag in current_tags:
            current_tags.remove(tag)
            self.tags = ",".join(current_tags)

    def get_default_param(self, key: str, default=None) -> Any:
        """获取默认参数值"""
        if not self.default_params:
            return default
        return self.default_params.get(key, default)

    def set_default_param(self, key: str, value: Any) -> None:
        """设置默认参数值"""
        if not self.default_params:
            self.default_params = {}
        self.default_params[key] = value

    def get_param_schema(self, key: str) -> Optional[Dict[str, Any]]:
        """获取参数结构定义"""
        if not self.param_schema:
            return None
        return self.param_schema.get(key)

    def validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数是否符合结构定义"""
        errors = {}
        
        if not self.param_schema:
            return errors
            
        for param_name, schema in self.param_schema.items():
            if schema.get("required", False) and param_name not in params:
                errors[param_name] = "参数必填"
                continue
                
            if param_name in params:
                value = params[param_name]
                param_type = schema.get("type")
                
                # 类型验证
                if param_type == "integer" and not isinstance(value, int):
                    errors[param_name] = "参数类型必须为整数"
                elif param_type == "float" and not isinstance(value, (int, float)):
                    errors[param_name] = "参数类型必须为数字"
                elif param_type == "string" and not isinstance(value, str):
                    errors[param_name] = "参数类型必须为字符串"
                
                # 范围验证
                if "min" in schema and value < schema["min"]:
                    errors[param_name] = f"参数值不能小于{schema['min']}"
                if "max" in schema and value > schema["max"]:
                    errors[param_name] = f"参数值不能大于{schema['max']}"
                    
                # 选项验证
                if "options" in schema and value not in schema["options"]:
                    errors[param_name] = f"参数值必须为{schema['options']}中的一个"
        
        return errors

    def merge_with_defaults(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """将用户参数与默认参数合并"""
        merged_params = self.default_params.copy() if self.default_params else {}
        merged_params.update(params)
        return merged_params

    def increment_usage(self) -> None:
        """增加使用次数"""
        self.usage_count = (self.usage_count or 0) + 1

    def get_scenario_info(self) -> Dict[str, Any]:
        """获取场景信息摘要"""
        return {
            "id": self.id,
            "name": self.name,
            "fault_type": self.fault_type,
            "description": self.description,
            "category": self.category,
            "is_builtin": self.is_builtin,
            "is_active": self.is_active,
            "usage_count": self.usage_count,
            "tags": self.tag_list,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
