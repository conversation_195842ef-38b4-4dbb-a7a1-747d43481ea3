.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 0 4px;

  .header-left {
    flex: 1;
    
    .name {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .status {
      display: flex;
      gap: 6px;
      align-items: center;
      margin-top: 6px;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.online {
          background-color: var(--el-color-success);
        }

        &.offline {
          background-color: var(--el-color-danger);
        }
      }

      .status-text {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .header-center {
    flex: 0 0 auto;
    margin: 0 20px;
  }

  .header-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    gap: 12px;

    .icon-close {
      cursor: pointer;
      color: var(--el-text-color-secondary);
      
      &:hover {
        color: var(--el-text-color-primary);
      }
    }
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 600px;

  .chat-messages {
    flex: 1;
    padding: 20px 16px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    background-color: var(--el-bg-color-page);

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-lighter);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-fill-color-dark);
      border-radius: 3px;
      
      &:hover {
        background: var(--el-fill-color-darker);
      }
    }

    .empty-chat {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(--el-text-color-secondary);
    }

    .message-item {
      display: flex;
      flex-direction: row;
      gap: 12px;
      align-items: flex-start;
      width: 100%;
      margin-bottom: 24px;

      &.message-left {
        justify-content: flex-start;

        .message-content {
          align-items: flex-start;

          .message-info {
            flex-direction: row;
          }

          .message-text {
            background-color: var(--el-fill-color-light);
            border: 1px solid var(--el-border-color-lighter);
          }
        }
      }

      &.message-right {
        flex-direction: row-reverse;

        .message-content {
          align-items: flex-end;

          .message-info {
            flex-direction: row-reverse;
          }

          .message-text {
            background-color: var(--el-color-primary-light-9);
            border: 1px solid var(--el-color-primary-light-7);
            color: var(--el-color-primary);
          }
        }
      }

      &.typing {
        .message-text {
          background-color: var(--el-fill-color-light);
          border: 1px solid var(--el-border-color-lighter);
        }
      }

      .message-avatar {
        flex-shrink: 0;
        border: 2px solid var(--el-border-color-lighter);
      }

      .message-content {
        display: flex;
        flex-direction: column;
        max-width: 75%;
        min-width: 120px;

        .message-info {
          display: flex;
          gap: 8px;
          margin-bottom: 6px;
          font-size: 12px;

          .message-time {
            color: var(--el-text-color-secondary);
          }

          .sender-name {
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }

        .message-text {
          padding: 12px 16px;
          line-height: 1.5;
          border-radius: 12px;
          font-size: 14px;
          word-wrap: break-word;
          white-space: pre-wrap;

          &.streaming-text {
            .cursor {
              animation: blink 1s infinite;
              color: var(--el-color-primary);
              font-weight: bold;
            }
          }
        }

        .message-actions {
          display: flex;
          gap: 8px;
          margin-top: 8px;
          opacity: 0;
          transition: opacity 0.2s;
        }
      }

      &:hover .message-actions {
        opacity: 1;
      }
    }
  }

  .chat-input {
    margin-top: 16px;

    .chat-input-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;

      .left {
        .tip {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
}

// 打字机光标动画
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// 暗色主题适配
.dark {
  .chat-container {
    .chat-messages {
      background-color: var(--el-bg-color);
      border-color: var(--el-border-color);

      .message-item {
        &.message-left {
          .message-text {
            background-color: var(--el-fill-color-dark);
            border-color: var(--el-border-color);
            color: var(--el-text-color-primary);
          }
        }

        &.message-right {
          .message-text {
            background-color: var(--el-color-primary-dark-2);
            border-color: var(--el-color-primary);
            color: var(--el-color-white);
          }
        }

        &.typing {
          .message-text {
            background-color: var(--el-fill-color-dark);
            border-color: var(--el-border-color);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 12px;
    
    .header-center {
      margin: 0;
      width: 100%;
    }
    
    .header-right {
      width: 100%;
      justify-content: space-between;
    }
  }

  .chat-container {
    height: 500px;
    
    .chat-messages {
      .message-item {
        .message-content {
          max-width: 85%;
        }
      }
    }
  }
}
