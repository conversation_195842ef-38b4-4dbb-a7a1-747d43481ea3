"""
用户API集成测试
验证重构后的用户API功能正确性
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.models.user import User
from app.core.security import create_access_token


class TestUserAPI:
    """用户API测试类"""
    
    @pytest.fixture
    async def client(self):
        """测试客户端"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac
    
    @pytest.fixture
    async def admin_headers(self, db: AsyncSession):
        """管理员认证头"""
        # 创建测试管理员用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password="fake_hash",
            is_superuser=True,
            is_active=True
        )
        db.add(admin_user)
        await db.commit()
        
        # 生成访问令牌
        token = create_access_token(data={"sub": admin_user.username})
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.fixture
    async def user_headers(self, db: AsyncSession):
        """普通用户认证头"""
        # 创建测试用户
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="fake_hash",
            is_superuser=False,
            is_active=True
        )
        db.add(user)
        await db.commit()
        
        # 生成访问令牌
        token = create_access_token(data={"sub": user.username})
        return {"Authorization": f"Bearer {token}"}

    async def test_get_user_info(self, client: AsyncClient, user_headers):
        """测试获取当前用户信息"""
        response = await client.get("/api/v1/users/info", headers=user_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "userId" in data["data"]
        assert "username" in data["data"]

    async def test_get_user_list(self, client: AsyncClient, admin_headers):
        """测试获取用户列表"""
        response = await client.get(
            "/api/v1/users/list", 
            headers=admin_headers,
            params={"current": 1, "size": 10}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "records" in data["data"]
        assert "total" in data["data"]

    async def test_get_user_list_with_keyword(self, client: AsyncClient, admin_headers):
        """测试带关键词的用户列表搜索"""
        response = await client.get(
            "/api/v1/users/list", 
            headers=admin_headers,
            params={"current": 1, "size": 10, "keyword": "admin"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    async def test_create_user(self, client: AsyncClient, admin_headers):
        """测试创建用户"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "password123",
            "nickname": "New User",
            "is_superuser": False
        }
        
        response = await client.post(
            "/api/v1/users/",
            headers=admin_headers,
            json=user_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["username"] == "newuser"

    async def test_update_profile(self, client: AsyncClient, user_headers):
        """测试更新用户资料"""
        update_data = {
            "nickname": "Updated Nickname",
            "email": "<EMAIL>"
        }
        
        response = await client.put(
            "/api/v1/users/profile",
            headers=user_headers,
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    async def test_update_user_status(self, client: AsyncClient, admin_headers, db: AsyncSession):
        """测试更新用户状态"""
        # 创建测试用户
        test_user = User(
            username="statususer",
            email="<EMAIL>",
            hashed_password="fake_hash",
            is_active=True
        )
        db.add(test_user)
        await db.commit()
        
        status_data = {"status": "2"}  # 离线状态
        
        response = await client.put(
            f"/api/v1/users/{test_user.id}/status",
            headers=admin_headers,
            json=status_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    async def test_get_user_by_id(self, client: AsyncClient, admin_headers, db: AsyncSession):
        """测试根据ID获取用户"""
        # 创建测试用户
        test_user = User(
            username="getuser",
            email="<EMAIL>",
            hashed_password="fake_hash",
            is_active=True
        )
        db.add(test_user)
        await db.commit()
        
        response = await client.get(
            f"/api/v1/users/{test_user.id}",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == test_user.id

    async def test_permission_denied_for_normal_user(self, client: AsyncClient, user_headers):
        """测试普通用户访问管理员接口被拒绝"""
        response = await client.get("/api/v1/users/list", headers=user_headers)
        assert response.status_code == 403

    async def test_unauthenticated_access_denied(self, client: AsyncClient):
        """测试未认证访问被拒绝"""
        response = await client.get("/api/v1/users/info")
        assert response.status_code == 401

    async def test_invalid_user_creation_data(self, client: AsyncClient, admin_headers):
        """测试无效的用户创建数据"""
        invalid_data = {
            "username": "a",  # 太短
            "email": "invalid-email",  # 无效邮箱
            "password": "123"  # 密码太短
        }
        
        response = await client.post(
            "/api/v1/users/",
            headers=admin_headers,
            json=invalid_data
        )
        
        assert response.status_code == 422  # 验证错误

    async def test_duplicate_username_creation(self, client: AsyncClient, admin_headers):
        """测试创建重复用户名的用户"""
        user_data = {
            "username": "admin",  # 已存在的用户名
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = await client.post(
            "/api/v1/users/",
            headers=admin_headers,
            json=user_data
        )
        
        assert response.status_code == 409  # 冲突错误 