"""
数据库会话管理
"""
from sqlalchemy import create_engine, false
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

from app.core.config import settings

# 创建异步数据库引擎
async_engine = create_async_engine(
    settings.DATABASE_URL,
    echo=settings.debug,
    future=false  # 启用SQL日志
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 为Alembic创建同步引擎（移除异步驱动前缀）
def get_sync_database_url():
    """获取同步数据库URL"""
    url = settings.DATABASE_URL
    # 移除异步驱动前缀，但保持正确的同步驱动
    if "+aiosqlite" in url:
        return url.replace("+aiosqlite", "")
    elif "+asyncpg" in url:
        return url.replace("+asyncpg", "+psycopg2")  # 使用psycopg2作为同步驱动
    elif "+aiomysql" in url:
        return url.replace("+aiomysql", "+pymysql")  # 使用pymysql作为同步驱动
    return url

# 创建同步引擎用于Alembic迁移
sync_engine = create_engine(
    get_sync_database_url(),
    echo=settings.debug
) 