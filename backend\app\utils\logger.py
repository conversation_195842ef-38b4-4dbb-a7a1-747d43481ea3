"""
日志工具模块
"""
import logging
import sys
from datetime import datetime
from typing import Any, Dict, Optional
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings


def setup_logger() -> logging.Logger:
    """
    设置应用日志器
    """
    logger = logging.getLogger(settings.app_name)
    logger.setLevel(getattr(logging, settings.log_level.upper()))

    # 避免重复添加handler
    if not logger.handlers:
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)

        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)

        logger.addHandler(console_handler)

    return logger


class OperationLogger:
    """
    操作日志记录器
    """
    
    def __init__(self):
        self.logger = setup_logger()
    
    async def log_operation(
        self,
        operation: str,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request_data: Optional[Dict[str, Any]] = None,
        result: str = "success",
        error_msg: Optional[str] = None
    ) -> None:
        """
        记录操作日志
        
        Args:
            operation: 操作类型
            user_id: 用户ID
            username: 用户名
            request_data: 请求数据
            result: 操作结果
            error_msg: 错误消息
        """
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "user_id": user_id,
            "username": username,
            "request_data": request_data,
            "result": result,
            "error_msg": error_msg
        }
        
        if result == "success":
            #self.logger.info(f"操作成功: {log_data}")
            pass
        else:
            self.logger.error(f"操作失败: {log_data}")
    
    async def log_login(
        self,
        username: str,
        ip_address: str,
        user_agent: str,
        success: bool = True,
        error_msg: Optional[str] = None
    ) -> None:
        """
        记录登录日志
        """
        await self.log_operation(
            operation="user_login",
            username=username,
            request_data={
                "ip_address": ip_address,
                "user_agent": user_agent
            },
            result="success" if success else "failed",
            error_msg=error_msg
        )
    
    async def log_crud_operation(
        self,
        operation: str,
        model_name: str,
        object_id: Optional[int] = None,
        user_id: Optional[int] = None,
        changes: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        记录CRUD操作日志
        """
        await self.log_operation(
            operation=f"{operation}_{model_name.lower()}",
            user_id=user_id,
            request_data={
                "model": model_name,
                "object_id": object_id,
                "changes": changes
            }
        )


# 创建全局操作日志器实例
operation_logger = OperationLogger() 