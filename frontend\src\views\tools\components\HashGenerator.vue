<template>
  <div class="hash-generator">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6f6;</i>
        <h2>MD5/SHA 哈希工具</h2>
      </div>
      <p class="tool-description">
        生成 MD5、SHA1、SHA256 等哈希值，支持文本和文件
      </p>
    </div>

    <div class="tool-content">
      <!-- 功能选择 -->
      <div class="function-tabs">
        <div
          v-for="tab in functionTabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeFunction === tab.key }"
          @click="activeFunction = tab.key"
        >
          <i class="iconfont-sys" v-html="tab.icon"></i>
          <span>{{ tab.name }}</span>
        </div>
      </div>

      <!-- 文本哈希 -->
      <div v-if="activeFunction === 'text'" class="text-section">
        <div class="input-section">
          <div class="input-header">
            <h3 class="input-title">输入文本</h3>
            <div class="input-actions">
              <ElButton size="small" @click="clearText">
                <i class="iconfont-sys">&#xe622;</i>
                清空
              </ElButton>
              <ElButton size="small" @click="pasteText">
                <i class="iconfont-sys">&#xe623;</i>
                粘贴
              </ElButton>
              <ElButton size="small" @click="loadSampleText">
                <i class="iconfont-sys">&#xe629;</i>
                示例
              </ElButton>
            </div>
          </div>
          
          <ElInput
            v-model="inputText"
            type="textarea"
            :rows="6"
            placeholder="请输入需要计算哈希的文本..."
            class="text-input"
            @input="handleTextHash"
          />
          
          <div class="text-info">
            <span>字符数：{{ inputText.length }}</span>
            <span>字节数：{{ getByteLength(inputText) }}</span>
          </div>
        </div>

        <!-- 哈希算法选择 -->
        <div class="algorithm-section">
          <div class="algorithm-header">
            <h3 class="algorithm-title">哈希算法</h3>
          </div>
          
          <div class="algorithm-options">
            <ElCheckbox
              v-for="algo in hashAlgorithms"
              :key="algo.key"
              v-model="algo.enabled"
              @change="handleTextHash"
              class="algorithm-checkbox"
            >
              {{ algo.name }}
            </ElCheckbox>
          </div>
        </div>

        <!-- 哈希结果 -->
        <div v-if="textHashResults.length > 0" class="results-section">
          <div class="results-header">
            <h3 class="results-title">哈希结果</h3>
            <ElButton size="small" @click="copyAllHashes">
              <i class="iconfont-sys">&#xe627;</i>
              复制全部
            </ElButton>
          </div>
          
          <div class="results-list">
            <div
              v-for="result in textHashResults"
              :key="result.algorithm"
              class="result-item"
            >
              <div class="result-label">{{ result.algorithm }}：</div>
              <div class="result-value">
                <code>{{ result.hash }}</code>
                <ElButton 
                  size="small" 
                  text 
                  @click="copyHash(result.hash)"
                  class="copy-btn"
                >
                  <i class="iconfont-sys">&#xe627;</i>
                </ElButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件哈希 -->
      <div v-if="activeFunction === 'file'" class="file-section">
        <div class="file-upload">
          <div class="upload-area" @drop="handleFileDrop" @dragover.prevent @dragenter.prevent>
            <ElUpload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileSelect"
              drag
              class="upload-component"
            >
              <div class="upload-content">
                <i class="upload-icon iconfont-sys">&#xe646;</i>
                <div class="upload-text">
                  <p>点击或拖拽文件到此处</p>
                  <p class="upload-hint">计算文件的哈希值</p>
                </div>
              </div>
            </ElUpload>
          </div>
        </div>

        <!-- 文件信息 -->
        <div v-if="fileInfo" class="file-info">
          <div class="info-header">
            <h3 class="info-title">文件信息</h3>
            <ElButton size="small" @click="clearFile">
              <i class="iconfont-sys">&#xe622;</i>
              清除文件
            </ElButton>
          </div>
          
          <div class="info-content">
            <div class="info-item">
              <label>文件名：</label>
              <span>{{ fileInfo.name }}</span>
            </div>
            <div class="info-item">
              <label>文件大小：</label>
              <span>{{ formatFileSize(fileInfo.size) }}</span>
            </div>
            <div class="info-item">
              <label>文件类型：</label>
              <span>{{ fileInfo.type || '未知' }}</span>
            </div>
            <div class="info-item">
              <label>最后修改：</label>
              <span>{{ formatDate(fileInfo.lastModified) }}</span>
            </div>
          </div>
        </div>

        <!-- 计算进度 -->
        <div v-if="calculating" class="progress-section">
          <div class="progress-header">
            <h3 class="progress-title">计算中...</h3>
          </div>
          
          <ElProgress :percentage="calculateProgress" :show-text="true" />
        </div>

        <!-- 文件哈希结果 -->
        <div v-if="fileHashResults.length > 0" class="file-results">
          <div class="results-header">
            <h3 class="results-title">文件哈希结果</h3>
            <div class="results-actions">
              <ElButton size="small" @click="copyAllFileHashes">
                <i class="iconfont-sys">&#xe627;</i>
                复制全部
              </ElButton>
              <ElButton size="small" @click="exportHashFile">
                <i class="iconfont-sys">&#xe62b;</i>
                导出
              </ElButton>
            </div>
          </div>
          
          <div class="results-list">
            <div
              v-for="result in fileHashResults"
              :key="result.algorithm"
              class="result-item"
            >
              <div class="result-label">{{ result.algorithm }}：</div>
              <div class="result-value">
                <code>{{ result.hash }}</code>
                <ElButton 
                  size="small" 
                  text 
                  @click="copyHash(result.hash)"
                  class="copy-btn"
                >
                  <i class="iconfont-sys">&#xe627;</i>
                </ElButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 哈希验证 -->
      <div v-if="activeFunction === 'verify'" class="verify-section">
        <div class="verify-input">
          <div class="input-row">
            <div class="input-group">
              <label class="input-label">原始文本/数据</label>
              <ElInput
                v-model="verifyText"
                type="textarea"
                :rows="4"
                placeholder="请输入原始文本..."
                @input="handleVerify"
              />
            </div>
            
            <div class="input-group">
              <label class="input-label">预期哈希值</label>
              <ElInput
                v-model="expectedHash"
                placeholder="请输入预期的哈希值..."
                @input="handleVerify"
              />
            </div>
          </div>

          <div class="algorithm-select">
            <label class="select-label">哈希算法：</label>
            <ElSelect v-model="verifyAlgorithm" @change="handleVerify">
              <ElOption
                v-for="algo in hashAlgorithms"
                :key="algo.key"
                :label="algo.name"
                :value="algo.key"
              />
            </ElSelect>
          </div>
        </div>

        <!-- 验证结果 -->
        <div v-if="verifyResult" class="verify-result">
          <div class="result-header">
            <h3 class="result-title">验证结果</h3>
          </div>
          
          <div class="verification-status" :class="{ success: verifyResult.match, error: !verifyResult.match }">
            <i class="status-icon iconfont-sys" v-html="verifyResult.match ? '&#xe64a;' : '&#xe64b;'"></i>
            <span class="status-text">
              {{ verifyResult.match ? '哈希值匹配' : '哈希值不匹配' }}
            </span>
          </div>
          
          <div class="hash-comparison">
            <div class="hash-item">
              <label>计算结果：</label>
              <code>{{ verifyResult.calculated }}</code>
            </div>
            <div class="hash-item">
              <label>预期值：</label>
              <code>{{ verifyResult.expected }}</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import CryptoJS from 'crypto-js'

defineOptions({ name: 'HashGenerator' })

// 功能选项
const functionTabs = [
  { key: 'text', name: '文本哈希', icon: '&#xe64c;' },
  { key: 'file', name: '文件哈希', icon: '&#xe64d;' },
  { key: 'verify', name: '哈希验证', icon: '&#xe64e;' }
]

// 哈希算法
const hashAlgorithms = ref([
  { key: 'md5', name: 'MD5', enabled: true },
  { key: 'sha1', name: 'SHA1', enabled: true },
  { key: 'sha256', name: 'SHA256', enabled: true },
  { key: 'sha512', name: 'SHA512', enabled: false },
  { key: 'sha3', name: 'SHA3-256', enabled: false }
])

// 响应式数据
const activeFunction = ref('text')

// 文本哈希
const inputText = ref('')
const textHashResults = ref<any[]>([])

// 文件哈希
const fileInfo = ref<any>(null)
const fileHashResults = ref<any[]>([])
const calculating = ref(false)
const calculateProgress = ref(0)

// 哈希验证
const verifyText = ref('')
const expectedHash = ref('')
const verifyAlgorithm = ref('md5')
const verifyResult = ref<any>(null)

// 示例文本
const sampleText = 'Hello, World! 这是一个测试文本。'

// 方法
const handleTextHash = () => {
  if (!inputText.value.trim()) {
    textHashResults.value = []
    return
  }

  const results: any[] = []
  const enabledAlgorithms = hashAlgorithms.value.filter(algo => algo.enabled)

  enabledAlgorithms.forEach(algo => {
    let hash = ''
    
    try {
      switch (algo.key) {
        case 'md5':
          hash = CryptoJS.MD5(inputText.value).toString()
          break
        case 'sha1':
          hash = CryptoJS.SHA1(inputText.value).toString()
          break
        case 'sha256':
          hash = CryptoJS.SHA256(inputText.value).toString()
          break
        case 'sha512':
          hash = CryptoJS.SHA512(inputText.value).toString()
          break
        case 'sha3':
          hash = CryptoJS.SHA3(inputText.value, { outputLength: 256 }).toString()
          break
      }
      
      if (hash) {
        results.push({
          algorithm: algo.name,
          hash: hash
        })
      }
    } catch (error) {
      console.error(`计算 ${algo.name} 失败:`, error)
    }
  })

  textHashResults.value = results
}

const handleFileSelect = (file: any) => {
  const selectedFile = file.raw
  processFile(selectedFile)
}

const handleFileDrop = (event: DragEvent) => {
  event.preventDefault()
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

const processFile = async (file: File) => {
  fileInfo.value = {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified
  }

  calculating.value = true
  calculateProgress.value = 0
  fileHashResults.value = []

  try {
    const enabledAlgorithms = hashAlgorithms.value.filter(algo => algo.enabled)
    const results: any[] = []

    // 读取文件内容
    const arrayBuffer = await file.arrayBuffer()
    const wordArray = CryptoJS.lib.WordArray.create(arrayBuffer)

    for (let i = 0; i < enabledAlgorithms.length; i++) {
      const algo = enabledAlgorithms[i]
      let hash = ''

      try {
        switch (algo.key) {
          case 'md5':
            hash = CryptoJS.MD5(wordArray).toString()
            break
          case 'sha1':
            hash = CryptoJS.SHA1(wordArray).toString()
            break
          case 'sha256':
            hash = CryptoJS.SHA256(wordArray).toString()
            break
          case 'sha512':
            hash = CryptoJS.SHA512(wordArray).toString()
            break
          case 'sha3':
            hash = CryptoJS.SHA3(wordArray, { outputLength: 256 }).toString()
            break
        }

        if (hash) {
          results.push({
            algorithm: algo.name,
            hash: hash
          })
        }
      } catch (error) {
        console.error(`计算文件 ${algo.name} 失败:`, error)
      }

      calculateProgress.value = Math.round(((i + 1) / enabledAlgorithms.length) * 100)
    }

    fileHashResults.value = results
    ElMessage.success('文件哈希计算完成')
  } catch (error) {
    ElMessage.error('文件处理失败')
    console.error('文件处理错误:', error)
  } finally {
    calculating.value = false
  }
}

const handleVerify = () => {
  if (!verifyText.value.trim() || !expectedHash.value.trim()) {
    verifyResult.value = null
    return
  }

  let calculatedHash = ''
  
  try {
    switch (verifyAlgorithm.value) {
      case 'md5':
        calculatedHash = CryptoJS.MD5(verifyText.value).toString()
        break
      case 'sha1':
        calculatedHash = CryptoJS.SHA1(verifyText.value).toString()
        break
      case 'sha256':
        calculatedHash = CryptoJS.SHA256(verifyText.value).toString()
        break
      case 'sha512':
        calculatedHash = CryptoJS.SHA512(verifyText.value).toString()
        break
      case 'sha3':
        calculatedHash = CryptoJS.SHA3(verifyText.value, { outputLength: 256 }).toString()
        break
    }

    const expectedLower = expectedHash.value.toLowerCase().trim()
    const calculatedLower = calculatedHash.toLowerCase()

    verifyResult.value = {
      calculated: calculatedHash,
      expected: expectedHash.value.trim(),
      match: expectedLower === calculatedLower
    }
  } catch (error) {
    ElMessage.error('哈希计算失败')
    verifyResult.value = null
  }
}

const getByteLength = (str: string): number => {
  return new Blob([str]).size
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const clearText = () => {
  inputText.value = ''
  textHashResults.value = []
}

const pasteText = async () => {
  try {
    const text = await navigator.clipboard.readText()
    inputText.value = text
    handleTextHash()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const loadSampleText = () => {
  inputText.value = sampleText
  handleTextHash()
  ElMessage.success('已加载示例文本')
}

const copyHash = async (hash: string) => {
  try {
    await navigator.clipboard.writeText(hash)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const copyAllHashes = async () => {
  if (textHashResults.value.length === 0) return
  
  const allHashes = textHashResults.value
    .map(result => `${result.algorithm}: ${result.hash}`)
    .join('\n')
  
  try {
    await navigator.clipboard.writeText(allHashes)
    ElMessage.success('复制全部哈希成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const clearFile = () => {
  fileInfo.value = null
  fileHashResults.value = []
  calculating.value = false
  calculateProgress.value = 0
}

const copyAllFileHashes = async () => {
  if (fileHashResults.value.length === 0) return
  
  const allHashes = fileHashResults.value
    .map(result => `${result.algorithm}: ${result.hash}`)
    .join('\n')
  
  try {
    await navigator.clipboard.writeText(allHashes)
    ElMessage.success('复制全部哈希成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const exportHashFile = () => {
  if (fileHashResults.value.length === 0 || !fileInfo.value) return
  
  const content = [
    `文件名: ${fileInfo.value.name}`,
    `文件大小: ${formatFileSize(fileInfo.value.size)}`,
    `计算时间: ${new Date().toLocaleString('zh-CN')}`,
    '',
    '哈希值:',
    ...fileHashResults.value.map(result => `${result.algorithm}: ${result.hash}`)
  ].join('\n')
  
  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${fileInfo.value.name}.hash.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('导出成功')
}
</script>

<style lang="scss" scoped>
.hash-generator {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .function-tabs {
      display: flex;
      gap: 8px;
      margin-bottom: 24px;
      padding: 4px;
      background: var(--el-fill-color-light);
      border-radius: 8px;

      .tab-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-regular);

        &:hover {
          background: var(--el-fill-color);
        }

        &.active {
          background: var(--el-color-primary);
          color: white;
        }

        i {
          font-size: 16px;
        }
      }
    }

    .text-section,
    .file-section,
    .verify-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 24px;
    }

    .text-section {
      .input-section {
        margin-bottom: 24px;

        .input-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .input-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }

          .input-actions {
            display: flex;
            gap: 8px;
          }
        }

        .text-input {
          :deep(.el-textarea__inner) {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
          }
        }

        .text-info {
          display: flex;
          gap: 16px;
          margin-top: 8px;
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }

      .algorithm-section {
        margin-bottom: 24px;

        .algorithm-header {
          margin-bottom: 12px;

          .algorithm-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .algorithm-options {
          display: flex;
          gap: 20px;
          flex-wrap: wrap;

          .algorithm-checkbox {
            margin-right: 0;
          }
        }
      }

      .results-section {
        .results-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;

          .results-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .results-list {
          .result-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 6px;
            margin-bottom: 8px;

            .result-label {
              width: 100px;
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              flex-shrink: 0;
            }

            .result-value {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;

              code {
                flex: 1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 13px;
                color: var(--el-color-primary);
                background: var(--el-fill-color-light);
                padding: 4px 8px;
                border-radius: 4px;
                word-break: break-all;
              }

              .copy-btn {
                flex-shrink: 0;
              }
            }
          }
        }
      }
    }

    .file-section {
      .file-upload {
        margin-bottom: 24px;

        .upload-area {
          .upload-component {
            width: 100%;

            :deep(.el-upload-dragger) {
              width: 100%;
              height: 200px;
              border: 2px dashed var(--el-border-color);
              border-radius: 8px;
              background: var(--el-fill-color-lighter);
              transition: all 0.3s ease;

              &:hover {
                border-color: var(--el-color-primary);
                background: var(--el-color-primary-light-9);
              }
            }

            .upload-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100%;

              .upload-icon {
                font-size: 48px;
                color: var(--el-color-primary);
                margin-bottom: 16px;
              }

              .upload-text {
                text-align: center;

                p {
                  margin: 0 0 4px 0;
                  font-size: 16px;
                  color: var(--el-text-color-primary);

                  &.upload-hint {
                    font-size: 14px;
                    color: var(--el-text-color-regular);
                  }
                }
              }
            }
          }
        }
      }

      .file-info {
        margin-bottom: 24px;

        .info-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .info-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .info-content {
          .info-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 4px;
            margin-bottom: 8px;

            label {
              width: 80px;
              font-size: 13px;
              font-weight: 500;
              color: var(--el-text-color-regular);
              flex-shrink: 0;
            }

            span {
              flex: 1;
              font-size: 13px;
              color: var(--el-text-color-primary);
              word-break: break-all;
            }
          }
        }
      }

      .progress-section {
        margin-bottom: 24px;

        .progress-header {
          margin-bottom: 12px;

          .progress-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }
      }

      .file-results {
        .results-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;

          .results-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }

          .results-actions {
            display: flex;
            gap: 8px;
          }
        }

        .results-list {
          .result-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 6px;
            margin-bottom: 8px;

            .result-label {
              width: 100px;
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              flex-shrink: 0;
            }

            .result-value {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;

              code {
                flex: 1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 13px;
                color: var(--el-color-primary);
                background: var(--el-fill-color-light);
                padding: 4px 8px;
                border-radius: 4px;
                word-break: break-all;
              }

              .copy-btn {
                flex-shrink: 0;
              }
            }
          }
        }
      }
    }

    .verify-section {
      .verify-input {
        margin-bottom: 24px;

        .input-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          margin-bottom: 16px;

          .input-group {
            .input-label {
              display: block;
              font-size: 14px;
              font-weight: 500;
              color: var(--el-text-color-primary);
              margin-bottom: 8px;
            }
          }
        }

        .algorithm-select {
          display: flex;
          align-items: center;
          gap: 12px;

          .select-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }
      }

      .verify-result {
        .result-header {
          margin-bottom: 16px;

          .result-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .verification-status {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          border-radius: 8px;
          margin-bottom: 16px;

          &.success {
            background: var(--el-color-success-light-9);
            border: 1px solid var(--el-color-success-light-5);

            .status-icon {
              color: var(--el-color-success);
            }

            .status-text {
              color: var(--el-color-success);
              font-weight: 600;
            }
          }

          &.error {
            background: var(--el-color-error-light-9);
            border: 1px solid var(--el-color-error-light-5);

            .status-icon {
              color: var(--el-color-error);
            }

            .status-text {
              color: var(--el-color-error);
              font-weight: 600;
            }
          }

          .status-icon {
            font-size: 20px;
          }

          .status-text {
            font-size: 16px;
          }
        }

        .hash-comparison {
          .hash-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 4px;
            margin-bottom: 8px;

            label {
              width: 80px;
              font-size: 13px;
              font-weight: 500;
              color: var(--el-text-color-regular);
              flex-shrink: 0;
            }

            code {
              flex: 1;
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 13px;
              color: var(--el-color-primary);
              background: var(--el-fill-color-light);
              padding: 4px 8px;
              border-radius: 4px;
              word-break: break-all;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .hash-generator {
    .tool-content {
      .function-tabs {
        .tab-item {
          flex-direction: column;
          gap: 4px;
          padding: 8px 12px;

          span {
            font-size: 12px;
          }
        }
      }

      .text-section {
        .algorithm-section {
          .algorithm-options {
            flex-direction: column;
            gap: 8px;
          }
        }

        .results-section {
          .results-list {
            .result-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;

              .result-label {
                width: auto;
              }

              .result-value {
                width: 100%;
              }
            }
          }
        }
      }

      .file-section {
        .file-upload {
          .upload-area {
            .upload-component {
              :deep(.el-upload-dragger) {
                height: 150px;
              }

              .upload-content {
                .upload-icon {
                  font-size: 36px;
                  margin-bottom: 12px;
                }

                .upload-text {
                  p {
                    font-size: 14px;

                    &.upload-hint {
                      font-size: 12px;
                    }
                  }
                }
              }
            }
          }
        }

        .file-info {
          .info-content {
            .info-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              label {
                width: auto;
              }
            }
          }
        }

        .file-results {
          .results-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .results-actions {
              width: 100%;
              justify-content: flex-start;
            }
          }

          .results-list {
            .result-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;

              .result-label {
                width: auto;
              }

              .result-value {
                width: 100%;
              }
            }
          }
        }
      }

      .verify-section {
        .verify-input {
          .input-row {
            grid-template-columns: 1fr;
          }

          .algorithm-select {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }

        .verify-result {
          .hash-comparison {
            .hash-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              label {
                width: auto;
              }
            }
          }
        }
      }
    }
  }
}
</style>
