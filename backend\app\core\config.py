"""
核心配置管理模块
使用 Pydantic Settings 管理应用配置
"""
from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "DpTestPlatform API"
    app_version: str = "1.0.0"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "mysql+aiomysql://root:eisoo.com123@*************:3306/dp_test?charset=UTF8MB4"
    
    # JWT 认证配置
    SECRET_KEY: str = "kPBDjVk0o3Y1wLxdODxBpjwEjo7-Euegg4kdnzFIRjc"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 1  # token过期时间: 60 minutes * 24 hours * 1 days = 1 days
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS 配置
    cors_origins: List[str] = [
        "*",
    ]
    cors_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
    cors_headers: List[str] = ["*"]

    
    # 日志配置
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"


# 全局配置实例
settings = Settings() 