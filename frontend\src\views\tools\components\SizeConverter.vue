<template>
  <div class="size-converter">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6f2;</i>
        <h2>数据大小转换工具</h2>
      </div>
      <p class="tool-description">
        支持字节、KB、MB、GB、TB 等数据大小单位之间的相互转换
      </p>
    </div>

    <div class="tool-content">
      <!-- 转换模式选择 -->
      <div class="mode-section">
        <div class="mode-tabs">
          <div
            v-for="mode in conversionModes"
            :key="mode.key"
            class="mode-tab"
            :class="{ active: activeMode === mode.key }"
            @click="activeMode = mode.key"
          >
            <i class="iconfont-sys" v-html="mode.icon"></i>
            <span>{{ mode.name }}</span>
          </div>
        </div>
      </div>

      <!-- 单位转换模式 -->
      <div v-if="activeMode === 'unit'" class="unit-conversion">
        <div class="conversion-row">
          <div class="input-group">
            <label class="input-label">输入数值</label>
            <ElInput
              v-model="inputValue"
              type="number"
              placeholder="请输入数值"
              class="value-input"
              @input="handleUnitConversion"
            />
          </div>
          
          <div class="unit-group">
            <label class="input-label">输入单位</label>
            <ElSelect v-model="inputUnit" @change="handleUnitConversion">
              <ElOption
                v-for="unit in dataUnits"
                :key="unit.value"
                :label="unit.label"
                :value="unit.value"
              />
            </ElSelect>
          </div>
          
          <div class="convert-icon">
            <i class="iconfont-sys">&#xe632;</i>
          </div>
          
          <div class="unit-group">
            <label class="input-label">目标单位</label>
            <ElSelect v-model="outputUnit" @change="handleUnitConversion">
              <ElOption
                v-for="unit in dataUnits"
                :key="unit.value"
                :label="unit.label"
                :value="unit.value"
              />
            </ElSelect>
          </div>
          
          <div class="output-group">
            <label class="input-label">转换结果</label>
            <ElInput
              v-model="outputValue"
              readonly
              placeholder="转换结果"
              class="value-input result-input"
            />
          </div>
        </div>
      </div>

      <!-- 全单位对照模式 -->
      <div v-if="activeMode === 'table'" class="table-conversion">
        <div class="input-section">
          <div class="input-row">
            <ElInput
              v-model="tableInputValue"
              type="number"
              placeholder="请输入数值"
              class="table-input"
              @input="handleTableConversion"
            />
            <ElSelect v-model="tableInputUnit" @change="handleTableConversion">
              <ElOption
                v-for="unit in dataUnits"
                :key="unit.value"
                :label="unit.label"
                :value="unit.value"
              />
            </ElSelect>
          </div>
        </div>
        
        <div class="conversion-table">
          <div class="table-header">
            <h3 class="table-title">全单位对照表</h3>
          </div>
          
          <div class="table-content">
            <div
              v-for="unit in dataUnits"
              :key="unit.value"
              class="table-row"
              :class="{ highlight: unit.value === tableInputUnit }"
            >
              <div class="unit-name">{{ unit.label }}</div>
              <div class="unit-value">{{ getConvertedValue(unit.value) }}</div>
              <div class="unit-description">{{ unit.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 计算器模式 -->
      <div v-if="activeMode === 'calculator'" class="calculator-conversion">
        <div class="calculator-input">
          <label class="input-label">数据大小计算</label>
          <ElInput
            v-model="calculatorExpression"
            placeholder="例如：1GB + 512MB - 100KB"
            class="calculator-input-field"
            @input="handleCalculatorInput"
            @keyup.enter="calculateExpression"
          />
          <ElButton type="primary" @click="calculateExpression" class="calculate-btn">
            <i class="iconfont-sys">&#xe626;</i>
            计算
          </ElButton>
        </div>
        
        <div v-if="calculatorResult" class="calculator-result">
          <div class="result-header">
            <h3 class="result-title">计算结果</h3>
          </div>
          
          <div class="result-content">
            <div class="result-item primary">
              <label>结果：</label>
              <span>{{ calculatorResult.formatted }}</span>
            </div>
            
            <div class="result-breakdown">
              <div class="breakdown-title">详细换算：</div>
              <div class="breakdown-list">
                <div
                  v-for="unit in dataUnits"
                  :key="unit.value"
                  class="breakdown-item"
                >
                  <span class="breakdown-unit">{{ unit.label }}:</span>
                  <span class="breakdown-value">{{ formatBytes(calculatorResult.bytes, unit.value) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 计算器错误提示 -->
        <div v-if="calculatorError" class="error-message">
          <i class="iconfont-sys">&#xe62a;</i>
          {{ calculatorError }}
        </div>
      </div>

      <!-- 常用大小参考 -->
      <div class="reference-section">
        <div class="section-header">
          <h3 class="section-title">常用大小参考</h3>
        </div>
        
        <div class="reference-grid">
          <div
            v-for="ref in sizeReferences"
            :key="ref.name"
            class="reference-item"
            @click="useReference(ref)"
          >
            <div class="reference-icon">
              <i class="iconfont-sys" v-html="ref.icon"></i>
            </div>
            <div class="reference-content">
              <div class="reference-name">{{ ref.name }}</div>
              <div class="reference-size">{{ ref.size }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'SizeConverter' })

// 转换模式
const conversionModes = [
  { key: 'unit', name: '单位转换', icon: '&#xe638;' },
  { key: 'table', name: '全单位对照', icon: '&#xe639;' },
  { key: 'calculator', name: '大小计算', icon: '&#xe63a;' }
]

// 数据单位定义
const dataUnits = [
  { value: 'byte', label: 'Byte (B)', description: '字节，最小存储单位', multiplier: 1 },
  { value: 'kb', label: 'KB', description: '千字节 = 1,024 字节', multiplier: 1024 },
  { value: 'mb', label: 'MB', description: '兆字节 = 1,024 KB', multiplier: 1024 * 1024 },
  { value: 'gb', label: 'GB', description: '吉字节 = 1,024 MB', multiplier: 1024 * 1024 * 1024 },
  { value: 'tb', label: 'TB', description: '太字节 = 1,024 GB', multiplier: 1024 * 1024 * 1024 * 1024 },
  { value: 'pb', label: 'PB', description: '拍字节 = 1,024 TB', multiplier: 1024 * 1024 * 1024 * 1024 * 1024 }
]

// 常用大小参考
const sizeReferences = [
  { name: '一个字符', size: '1 Byte', bytes: 1, icon: '&#xe63b;' },
  { name: '一个汉字', size: '2-4 Bytes', bytes: 3, icon: '&#xe63c;' },
  { name: '一张照片', size: '2-5 MB', bytes: 3 * 1024 * 1024, icon: '&#xe63d;' },
  { name: '一首歌曲', size: '3-5 MB', bytes: 4 * 1024 * 1024, icon: '&#xe63e;' },
  { name: '一部电影', size: '1-4 GB', bytes: 2 * 1024 * 1024 * 1024, icon: '&#xe63f;' },
  { name: 'DVD光盘', size: '4.7 GB', bytes: 4.7 * 1024 * 1024 * 1024, icon: '&#xe640;' },
  { name: 'U盘(16GB)', size: '16 GB', bytes: 16 * 1024 * 1024 * 1024, icon: '&#xe641;' },
  { name: '硬盘(1TB)', size: '1 TB', bytes: 1024 * 1024 * 1024 * 1024, icon: '&#xe642;' }
]

// 响应式数据
const activeMode = ref('unit')

// 单位转换模式
const inputValue = ref('')
const inputUnit = ref('mb')
const outputUnit = ref('gb')
const outputValue = ref('')

// 全单位对照模式
const tableInputValue = ref('1')
const tableInputUnit = ref('gb')

// 计算器模式
const calculatorExpression = ref('')
const calculatorResult = ref<any>(null)
const calculatorError = ref('')

// 方法
const handleUnitConversion = () => {
  if (!inputValue.value || isNaN(Number(inputValue.value))) {
    outputValue.value = ''
    return
  }

  const inputBytes = convertToBytes(Number(inputValue.value), inputUnit.value)
  const result = convertFromBytes(inputBytes, outputUnit.value)
  outputValue.value = formatNumber(result)
}

const handleTableConversion = () => {
  // 表格模式会自动更新，无需额外处理
}

const getConvertedValue = (targetUnit: string): string => {
  if (!tableInputValue.value || isNaN(Number(tableInputValue.value))) {
    return '0'
  }

  const inputBytes = convertToBytes(Number(tableInputValue.value), tableInputUnit.value)
  const result = convertFromBytes(inputBytes, targetUnit)
  return formatNumber(result)
}

const convertToBytes = (value: number, unit: string): number => {
  const unitObj = dataUnits.find(u => u.value === unit)
  return value * (unitObj?.multiplier || 1)
}

const convertFromBytes = (bytes: number, unit: string): number => {
  const unitObj = dataUnits.find(u => u.value === unit)
  return bytes / (unitObj?.multiplier || 1)
}

const formatNumber = (num: number): string => {
  if (num === 0) return '0'
  if (num < 0.001) return num.toExponential(3)
  if (num < 1) return num.toFixed(6).replace(/\.?0+$/, '')
  if (num < 1000) return num.toFixed(3).replace(/\.?0+$/, '')
  return num.toLocaleString()
}

const formatBytes = (bytes: number, unit: string): string => {
  const result = convertFromBytes(bytes, unit)
  return formatNumber(result)
}

const handleCalculatorInput = () => {
  calculatorError.value = ''
  calculatorResult.value = null
}

const calculateExpression = () => {
  if (!calculatorExpression.value.trim()) {
    ElMessage.warning('请输入计算表达式')
    return
  }

  try {
    const result = parseAndCalculate(calculatorExpression.value)
    calculatorResult.value = {
      bytes: result,
      formatted: formatBytesToBestUnit(result)
    }
    calculatorError.value = ''
    ElMessage.success('计算完成')
  } catch (error) {
    calculatorError.value = error instanceof Error ? error.message : '计算表达式错误'
    calculatorResult.value = null
  }
}

const parseAndCalculate = (expression: string): number => {
  // 简单的表达式解析器
  const tokens = expression.toLowerCase()
    .replace(/\s+/g, '')
    .replace(/([+\-*/()])/g, ' $1 ')
    .split(/\s+/)
    .filter(token => token.trim())

  const values: number[] = []
  const operators: string[] = []

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i]

    if (/^\d+(\.\d+)?$/.test(token)) {
      // 数字
      const nextToken = tokens[i + 1]
      if (nextToken && /^(b|byte|kb|mb|gb|tb|pb)$/.test(nextToken)) {
        const value = parseFloat(token)
        const unit = normalizeUnit(nextToken)
        values.push(convertToBytes(value, unit))
        i++ // 跳过单位token
      } else {
        values.push(parseFloat(token))
      }
    } else if (/^[+\-*/]$/.test(token)) {
      // 操作符
      while (operators.length > 0 && getPrecedence(operators[operators.length - 1]) >= getPrecedence(token)) {
        applyOperator(values, operators.pop()!)
      }
      operators.push(token)
    } else if (token === '(') {
      operators.push(token)
    } else if (token === ')') {
      while (operators.length > 0 && operators[operators.length - 1] !== '(') {
        applyOperator(values, operators.pop()!)
      }
      operators.pop() // 移除 '('
    }
  }

  while (operators.length > 0) {
    applyOperator(values, operators.pop()!)
  }

  if (values.length !== 1) {
    throw new Error('表达式格式错误')
  }

  return values[0]
}

const normalizeUnit = (unit: string): string => {
  const unitMap: { [key: string]: string } = {
    'b': 'byte',
    'byte': 'byte',
    'kb': 'kb',
    'mb': 'mb',
    'gb': 'gb',
    'tb': 'tb',
    'pb': 'pb'
  }
  return unitMap[unit] || 'byte'
}

const getPrecedence = (operator: string): number => {
  switch (operator) {
    case '+':
    case '-':
      return 1
    case '*':
    case '/':
      return 2
    default:
      return 0
  }
}

const applyOperator = (values: number[], operator: string) => {
  if (values.length < 2) {
    throw new Error('操作数不足')
  }

  const b = values.pop()!
  const a = values.pop()!

  switch (operator) {
    case '+':
      values.push(a + b)
      break
    case '-':
      values.push(a - b)
      break
    case '*':
      values.push(a * b)
      break
    case '/':
      if (b === 0) throw new Error('除数不能为零')
      values.push(a / b)
      break
    default:
      throw new Error(`未知操作符: ${operator}`)
  }
}

const formatBytesToBestUnit = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const units = ['byte', 'kb', 'mb', 'gb', 'tb', 'pb']
  let unitIndex = 0

  let value = bytes
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }

  const unit = dataUnits.find(u => u.value === units[unitIndex])
  return `${formatNumber(value)} ${unit?.label || 'B'}`
}

const useReference = (ref: any) => {
  if (activeMode.value === 'unit') {
    inputValue.value = '1'
    inputUnit.value = 'byte'
    outputUnit.value = 'mb'
    
    // 设置为参考大小的字节数
    const bytes = ref.bytes
    const inputBytes = convertToBytes(1, 'byte')
    inputValue.value = String(bytes)
    handleUnitConversion()
  } else if (activeMode.value === 'table') {
    tableInputValue.value = String(ref.bytes)
    tableInputUnit.value = 'byte'
    handleTableConversion()
  } else if (activeMode.value === 'calculator') {
    calculatorExpression.value = `${ref.bytes} byte`
  }
  
  ElMessage.success(`已使用参考值：${ref.name}`)
}
</script>

<style lang="scss" scoped>
.size-converter {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .mode-section {
      margin-bottom: 24px;

      .mode-tabs {
        display: flex;
        gap: 8px;
        padding: 4px;
        background: var(--el-fill-color-light);
        border-radius: 8px;

        .mode-tab {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          padding: 12px 16px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-regular);

          &:hover {
            background: var(--el-fill-color);
          }

          &.active {
            background: var(--el-color-primary);
            color: white;
          }

          i {
            font-size: 16px;
          }
        }
      }
    }

    .unit-conversion,
    .table-conversion,
    .calculator-conversion {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }

    .unit-conversion {
      .conversion-row {
        display: grid;
        grid-template-columns: 1fr auto 1fr auto 1fr;
        gap: 16px;
        align-items: end;

        .input-group,
        .unit-group,
        .output-group {
          .input-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 8px;
          }

          .value-input {
            :deep(.el-input__inner) {
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 14px;
            }

            &.result-input {
              :deep(.el-input__inner) {
                background: var(--el-fill-color-lighter);
                font-weight: 600;
                color: var(--el-color-primary);
              }
            }
          }
        }

        .convert-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          color: var(--el-color-primary);
          font-size: 18px;
        }
      }
    }

    .table-conversion {
      .input-section {
        margin-bottom: 24px;

        .input-row {
          display: flex;
          gap: 12px;
          align-items: center;

          .table-input {
            width: 200px;

            :deep(.el-input__inner) {
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 14px;
            }
          }
        }
      }

      .conversion-table {
        .table-header {
          margin-bottom: 16px;

          .table-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .table-content {
          .table-row {
            display: grid;
            grid-template-columns: 100px 150px 1fr;
            gap: 16px;
            padding: 12px 16px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 6px;
            margin-bottom: 8px;
            transition: all 0.3s ease;

            &:hover {
              background: var(--el-fill-color-light);
            }

            &.highlight {
              background: var(--el-color-primary-light-9);
              border-color: var(--el-color-primary);
            }

            .unit-name {
              font-weight: 600;
              color: var(--el-text-color-primary);
            }

            .unit-value {
              font-family: 'Consolas', 'Monaco', monospace;
              font-weight: 600;
              color: var(--el-color-primary);
            }

            .unit-description {
              font-size: 13px;
              color: var(--el-text-color-regular);
            }
          }
        }
      }
    }

    .calculator-conversion {
      .calculator-input {
        display: flex;
        gap: 12px;
        align-items: end;
        margin-bottom: 24px;

        .input-label {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
        }

        .calculator-input-field {
          flex: 1;

          :deep(.el-input__inner) {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
          }
        }

        .calculate-btn {
          height: 32px;
        }
      }

      .calculator-result {
        .result-header {
          margin-bottom: 16px;

          .result-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
          }
        }

        .result-content {
          .result-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: var(--el-color-success-light-9);
            border-radius: 6px;
            margin-bottom: 16px;

            &.primary {
              font-size: 16px;
              font-weight: 600;

              label {
                color: var(--el-text-color-primary);
              }

              span {
                color: var(--el-color-success);
                font-family: 'Consolas', 'Monaco', monospace;
              }
            }
          }

          .result-breakdown {
            .breakdown-title {
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              margin-bottom: 12px;
            }

            .breakdown-list {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
              gap: 8px;

              .breakdown-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 12px;
                background: var(--el-fill-color-light);
                border-radius: 4px;
                font-size: 13px;

                .breakdown-unit {
                  color: var(--el-text-color-regular);
                }

                .breakdown-value {
                  font-family: 'Consolas', 'Monaco', monospace;
                  font-weight: 600;
                  color: var(--el-color-primary);
                }
              }
            }
          }
        }
      }

      .error-message {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: var(--el-color-error-light-9);
        color: var(--el-color-error);
        border-radius: 4px;
        font-size: 13px;

        i {
          font-size: 14px;
        }
      }
    }

    .reference-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 20px;

      .section-header {
        margin-bottom: 16px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }
      }

      .reference-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 12px;

        .reference-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          border: 1px solid var(--el-border-color-light);
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--el-color-primary);
            background: var(--el-color-primary-light-9);
          }

          .reference-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--el-color-primary-light-9);
            border-radius: 6px;
            color: var(--el-color-primary);
            font-size: 16px;
          }

          .reference-content {
            flex: 1;

            .reference-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--el-text-color-primary);
              margin-bottom: 2px;
            }

            .reference-size {
              font-size: 12px;
              color: var(--el-text-color-regular);
              font-family: 'Consolas', 'Monaco', monospace;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .size-converter {
    .tool-content {
      .mode-section {
        .mode-tabs {
          .mode-tab {
            flex-direction: column;
            gap: 4px;
            padding: 8px 12px;

            span {
              font-size: 12px;
            }
          }
        }
      }

      .unit-conversion {
        .conversion-row {
          grid-template-columns: 1fr;
          gap: 16px;

          .convert-icon {
            transform: rotate(90deg);
          }
        }
      }

      .table-conversion {
        .table-content {
          .table-row {
            grid-template-columns: 1fr;
            gap: 8px;
          }
        }
      }

      .calculator-conversion {
        .calculator-input {
          flex-direction: column;
          align-items: stretch;
        }

        .result-breakdown {
          .breakdown-list {
            grid-template-columns: 1fr;
          }
        }
      }

      .reference-section {
        .reference-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
