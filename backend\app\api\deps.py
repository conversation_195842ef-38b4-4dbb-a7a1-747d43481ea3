"""
API 依赖注入模块
包含认证、权限检查、数据库会话等依赖
"""
from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.core.security import verify_token, create_credentials_exception
from app.repositories.user.user import UserRepository
from app.models.user.user import User

# 创建安全依赖
security = HTTPBearer()


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    获取当前用户依赖
    
    Args:
        db: 数据库会话
        credentials: 认证凭据
        
    Returns:
        当前用户
        
    Raises:
        HTTPException: 认证失败
    """
    # 提取token（去除Bearer前缀）
    token = credentials.credentials
    if token.startswith("Bearer "):
        token = token[7:]
    
    # 验证token
    payload = verify_token(token)
    if payload is None:
        raise create_credentials_exception("无效的认证令牌")
    
    username: str = payload.get("sub")
    if username is None:
        raise create_credentials_exception("无效的认证令牌")
    
    # 获取用户
    user_repo = UserRepository(db)
    user = await user_repo.get_by_username(username)
    if user is None:
        raise create_credentials_exception("用户不存在")
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    获取当前活跃用户依赖
    
    Args:
        current_user: 当前用户
        
    Returns:
        当前活跃用户
        
    Raises:
        HTTPException: 用户未激活
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账号已被禁用"
        )
    
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    获取当前超级用户依赖
    
    Args:
        current_user: 当前用户
        
    Returns:
        当前超级用户
        
    Raises:
        HTTPException: 权限不足
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级用户权限"
        )
    
    return current_user


async def get_optional_current_user(
    db: AsyncSession = Depends(get_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[User]:
    """
    获取可选的当前用户依赖（用于可选认证的接口）
 
    Args:
        db: 数据库会话
        credentials: 认证凭据
        
    Returns:
        当前用户或None
    """
    if not credentials:
        return None
    
    try:
        payload = verify_token(credentials.credentials)
        if payload is None:
            return None
        
        username: str = payload.get("sub")
        if username is None:
            return None
        
        # 获取用户
        user_repo = UserRepository(db)
        user = await user_repo.get_by_username(username)
        return user
    except Exception:
        return None


def require_permissions(required_permissions: list[str]):
    """
    权限检查装饰器工厂
    
    Args:
        required_permissions: 需要的权限列表
        
    Returns:
        权限检查依赖函数
    """
    async def permission_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        """
        权限检查依赖
        
        Args:
            current_user: 当前用户
            
        Returns:
            当前用户
            
        Raises:
            HTTPException: 权限不足
        """
        # 超级用户拥有所有权限
        if current_user.is_superuser:
            return current_user
        
        # 检查用户是否具有所需权限
        for permission in required_permissions:
            if not current_user.has_permission(permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission}"
                )
        
        return current_user
    
    return permission_checker


def require_roles(required_roles: list[str]):
    """
    角色检查装饰器工厂
    
    Args:
        required_roles: 需要的角色列表
        
    Returns:
        角色检查依赖函数
    """
    async def role_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        """
        角色检查依赖
        
        Args:
            current_user: 当前用户
            
        Returns:
            当前用户
            
        Raises:
            HTTPException: 角色不足
        """
        # 超级用户拥有所有角色
        if current_user.is_superuser:
            return current_user
        
        # 检查用户是否具有所需角色
        user_roles = current_user.role_names
        for role in required_roles:
            if role not in user_roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少角色: {role}"
                )
        
        return current_user
    
    return role_checker 