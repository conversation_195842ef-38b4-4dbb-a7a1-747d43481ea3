/**
 * 混沌测试故障分类和类型统一定义
 * 所有模块必须使用此定义，确保一致性
 */

// 故障分类定义
export interface FaultCategory {
  key: string
  label: string
  description: string
  faultTypes: FaultType[]
}

// 故障类型定义
export interface FaultType {
  key: string
  label: string
  description: string
  category: string
  defaultParams: Record<string, any>
}

// 统一的故障分类定义
export const FAULT_CATEGORIES: FaultCategory[] = [
  {
    key: 'system_resource',
    label: '系统资源',
    description: '系统CPU、内存等资源相关故障',
    faultTypes: [
      {
        key: 'cpu',
        label: 'CPU故障',
        description: '模拟CPU高负载或异常',
        category: 'system_resource',
        defaultParams: {
          'cpu-percent': 80,
          timeout: 60
        }
      },
      {
        key: 'memory',
        label: '内存故障',
        description: '模拟内存占用或泄漏',
        category: 'system_resource',
        defaultParams: {
          'mem-percent': 70,
          timeout: 60
        }
      }
    ]
  },
  {
    key: 'network_fault',
    label: '网络故障',
    description: '网络连接、延迟、丢包等故障',
    faultTypes: [
      {
        key: 'network',
        label: '网络故障',
        description: '模拟网络延迟、丢包等异常',
        category: 'network_fault',
        defaultParams: {
          action: 'delay',
          time: 100,
          interface: 'eth0'
        }
      }
    ]
  },
  {
    key: 'storage_fault',
    label: '存储故障',
    description: '磁盘IO、存储空间等故障',
    faultTypes: [
      {
        key: 'disk',
        label: '磁盘故障',
        description: '模拟磁盘IO异常或空间不足',
        category: 'storage_fault',
        defaultParams: {
          operations: ['read'],
          size: '100M',
          timeout: 60
        }
      }
    ]
  },
  {
    key: 'process_fault',
    label: '进程故障',
    description: '进程异常退出、挂起等故障',
    faultTypes: [
      {
        key: 'process',
        label: '进程故障',
        description: '模拟进程异常退出或挂起',
        category: 'process_fault',
        defaultParams: {
          action: 'kill',
          process: ''
        }
      }
    ]
  },
  {
    key: 'container_fault',
    label: '容器故障',
    description: 'Docker容器相关故障',
    faultTypes: [
      {
        key: 'docker',
        label: 'Docker故障',
        description: '模拟Docker容器异常',
        category: 'container_fault',
        defaultParams: {
          container: '',
          action: 'cpu'
        }
      }
    ]
  },
  {
    key: 'k8s_fault',
    label: 'Kubernetes故障',
    description: 'K8S集群和资源故障',
    faultTypes: [
      {
        key: 'k8s',
        label: 'K8S故障',
        description: '模拟Kubernetes资源异常',
        category: 'k8s_fault',
        defaultParams: {
          resource_type: 'pod',
          namespace: 'default',
          action: 'delete'
        }
      }
    ]
  },
  {
    key: 'application_fault',
    label: '应用故障',
    description: 'JVM、应用层面的故障',
    faultTypes: [
      {
        key: 'jvm',
        label: 'JVM故障',
        description: '模拟JVM应用异常',
        category: 'application_fault',
        defaultParams: {
          fault_type: 'delay',
          method: '',
          delay: 1000
        }
      }
    ]
  }
]

// 扁平化的故障类型映射（便于查找）
export const FAULT_TYPE_MAP: Record<string, FaultType> = {}
FAULT_CATEGORIES.forEach(category => {
  category.faultTypes.forEach(faultType => {
    FAULT_TYPE_MAP[faultType.key] = faultType
  })
})

// 分类映射（便于查找）
export const FAULT_CATEGORY_MAP: Record<string, FaultCategory> = {}
FAULT_CATEGORIES.forEach(category => {
  FAULT_CATEGORY_MAP[category.key] = category
})

// 工具函数
export const getFaultTypeLabel = (key: string): string => {
  return FAULT_TYPE_MAP[key]?.label || key
}

export const getFaultCategoryLabel = (key: string): string => {
  return FAULT_CATEGORY_MAP[key]?.label || key
}

export const getFaultTypesByCategory = (categoryKey: string): FaultType[] => {
  return FAULT_CATEGORY_MAP[categoryKey]?.faultTypes || []
}

export const getFaultTypeDefaultParams = (key: string): Record<string, any> => {
  return FAULT_TYPE_MAP[key]?.defaultParams || {}
}

// 故障类型标签颜色映射
export const getFaultTypeTagType = (key: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    cpu: 'danger',
    memory: 'warning',
    network: 'info',
    disk: 'success',
    process: 'primary',
    k8s: 'primary',
    jvm: 'warning',
    docker: 'info'
  }
  return typeMap[key] || 'info'
}


