"""
环境管理Pydantic模式
"""
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from datetime import datetime

from app.schemas.base import BaseResponseSchema


class EnvironmentBase(BaseModel):
    """环境基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="环境名称")
    type: str = Field(..., min_length=1, max_length=50, description="环境类型")
    description: Optional[str] = Field(None, max_length=1000, description="环境描述")
    host: Optional[str] = Field(None, max_length=255, description="主机地址")
    port: Optional[int] = Field(None, ge=1, le=65535, description="端口号")
    config: Optional[Dict[str, Any]] = Field(None, description="环境配置")
    tags: Optional[str] = Field(None, max_length=500, description="环境标签，逗号分隔")

    @validator('type')
    def validate_type(cls, v):
        """验证环境类型"""
        allowed_types = [
            'database', 'redis', 'ssh', 'k8s', 'api', 'kafka',
            'mongodb', 'elasticsearch', 'rabbitmq', 'minio','opensearch'
        ]
        if v.lower() not in allowed_types:
            raise ValueError(f'环境类型必须是以下之一: {", ".join(allowed_types)}')
        return v.lower()

    @validator('config')
    def validate_config(cls, v, values):
        """根据环境类型验证配置"""
        if not v:
            return v
        
        env_type = values.get('type')
        if not env_type:
            return v
            
        # 数据库类型必须的配置项
        if env_type == 'database':
            required_fields = ['db_type', 'database_name']
            for field in required_fields:
                if field not in v:
                    raise ValueError(f'数据库类型环境必须包含配置项: {field}')
        
        # Redis类型必须的配置项
        elif env_type == 'redis':
            if 'password' not in v:
                v['password'] = ''  # Redis密码可以为空
        
        # SSH类型必须的配置项
        elif env_type == 'ssh':
            required_fields = ['username']
            for field in required_fields:
                if field not in v:
                    raise ValueError(f'SSH类型环境必须包含配置项: {field}')
        
        return v


class EnvironmentCreate(EnvironmentBase):
    """创建环境请求模式"""
    pass


class EnvironmentUpdate(BaseModel):
    """更新环境请求模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="环境名称")
    type: Optional[str] = Field(None, min_length=1, max_length=50, description="环境类型")
    description: Optional[str] = Field(None, max_length=1000, description="环境描述")
    host: Optional[str] = Field(None, max_length=255, description="主机地址")
    port: Optional[int] = Field(None, ge=1, le=65535, description="端口号")
    config: Optional[Dict[str, Any]] = Field(None, description="环境配置")
    tags: Optional[str] = Field(None, max_length=500, description="环境标签，逗号分隔")

    @validator('type')
    def validate_type(cls, v):
        """验证环境类型"""
        if v is None:
            return v
        allowed_types = [
            'database', 'redis', 'ssh', 'k8s', 'api', 'kafka',
            'mongodb', 'elasticsearch', 'rabbitmq', 'minio'
        ]
        if v.lower() not in allowed_types:
            raise ValueError(f'环境类型必须是以下之一: {", ".join(allowed_types)}')
        return v.lower()


class EnvironmentResponse(EnvironmentBase):
    """环境响应模式"""
    id: int
    status: str = Field(description="连接状态")
    last_test_time: Optional[str] = Field(None, description="最后测试时间")
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    tag_list: List[str] = Field(default_factory=list, description="标签列表")

    class Config:
        from_attributes = True

    def __init__(self, **data):
        super().__init__(**data)
        # 自动生成tag_list
        if self.tags:
            self.tag_list = [tag.strip() for tag in self.tags.split(",") if tag.strip()]
        else:
            self.tag_list = []


class EnvironmentList(BaseResponseSchema):
    """环境列表响应模式"""
    data: List[EnvironmentResponse]
    total: int
    page: int
    size: int


class ConnectionTestRequest(BaseModel):
    """连接测试请求模式"""
    type: Optional[str] = Field(None, description="环境类型")
    config: Optional[Dict[str, Any]] = Field(None, description="连接配置")
    timeout: Optional[int] = Field(10, ge=1, le=60, description="超时时间(秒)")


class ConnectionTestResponse(BaseModel):
    """连接测试响应模式"""
    success: bool = Field(description="连接是否成功")
    message: str = Field(description="测试结果消息")
    duration: float = Field(description="连接耗时(秒)")
    details: Optional[Dict[str, Any]] = Field(None, description="连接详情") 