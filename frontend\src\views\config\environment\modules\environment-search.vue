<!-- 环境搜索栏 -->
<template>
  <ArtSearchBar
    v-model:filter="searchFormState"
    :items="formItems"
    @reset="handleReset"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
import type { SearchChangeParams, SearchFormItem } from '@/types'
import { EnvironmentService } from '@/api/envApi'
import type { SupportedType } from '@/api/envApi'

interface Emits {
  (e: 'search', params: Record<string, any>): void
  (e: 'reset'): void
}

const props = defineProps<{
  filter: Record<string, any>
}>()

const emit = defineEmits<Emits>()

const searchFormState = ref({ ...props.filter })
const supportedTypes = ref<SupportedType[]>([])

watch(
  () => props.filter,
  (newFilter) => {
    searchFormState.value = { ...newFilter }
  },
  { deep: true, immediate: true }
)

// 获取支持的环境类型
const fetchSupportedTypes = async () => {
  try {
    supportedTypes.value = await EnvironmentService.getSupportedTypes()
  } catch (error) {
    console.error('获取支持的环境类型失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  searchFormState.value = { ...props.filter }
  emit('reset')
}

// 搜索处理
const handleSearch = () => {
  emit('search', searchFormState.value)
}

const handleFormChange = (params: SearchChangeParams): void => {
  // 可以在这里处理表单联动逻辑
}

// --- 表单配置项 ---
const formItems: SearchFormItem[] = [
  {
    label: '关键词',
    prop: 'keyword',
    type: 'input',
    config: {
      clearable: true,
      placeholder: '请输入环境名称或描述'
    },
    onChange: handleFormChange
  },
  {
    label: '环境类型',
    prop: 'env_type',
    type: 'select',
    options: () => supportedTypes.value.map(type => ({
      label: type.name,
      value: type.type
    })),
    config: {
      clearable: true,
      placeholder: '请选择'
    },
    onChange: handleFormChange
  },
  {
    label: '连接状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '已连接', value: 'connected' },
      { label: '连接失败', value: 'failed' },
      { label: '未知', value: 'unknown' }
    ],
    config: {
      clearable: true,
      placeholder: '请选择'
    },
    onChange: handleFormChange
  },
  {
    label: '标签',
    prop: 'tags',
    type: 'input',
    config: {
      clearable: true,
      placeholder: '请输入标签关键词'
    },
    onChange: handleFormChange
  }
]

// 初始化
onMounted(() => {
  fetchSupportedTypes()
})
</script> 