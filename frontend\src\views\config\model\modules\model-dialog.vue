<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    align-center
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
      v-loading="isLoading"
      element-loading-text="处理中..."
    >
      <!-- 基础信息 -->
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="模型名称" prop="name">
            <ElInput v-model="formData.name" placeholder="请输入模型名称" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="平台类型" prop="platform">
            <ElSelect
              v-model="formData.platform"
              placeholder="请选择平台类型"
              style="width: 100%"
              @change="handlePlatformChange"
            >
              <ElOption
                v-for="(config, platform) in MODEL_PLATFORM_CONFIG"
                :key="platform"
                :label="config.text"
                :value="platform"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="模型描述" prop="description">
            <ElInput
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入模型描述"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 连接信息 -->
      <ElDivider content-position="left">连接配置</ElDivider>
      
      <ElRow :gutter="20">
        <ElCol :span="16">
          <ElFormItem label="API地址" prop="api_url">
            <ElInput v-model="formData.api_url" placeholder="请输入API地址" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem label="超时时间/秒" prop="timeout_seconds">
            <ElInputNumber
              v-model="formData.timeout_seconds"
              :min="1"
              :max="300"
              style="width: 100%"
            />

          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="API Key" prop="api_key">
            <ElInput
              v-model="formData.api_key"
              type="password"
              show-password
              placeholder="请输入API Key"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 模型配置 -->
      <ElDivider content-position="left">模型配置</ElDivider>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="模型名称" prop="model_name">
            <ElInput
              v-model="formData.model_name"
              placeholder="请输入实际模型名称"
            />
            <div style="margin-top: 4px; color: #999; font-size: 12px;">
              如：deepseek-chat、qwen-turbo、local-model
            </div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="最大令牌数" prop="max_tokens">
            <ElInputNumber
              v-model="formData.max_tokens"
              :min="1"
              :max="32768"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="系统提示词" prop="prompt">
            <ElInput
              v-model="formData.prompt"
              type="textarea"
              :rows="4"
              placeholder="请输入系统提示词（可选）"
            />
            <div style="margin-top: 4px; color: #999; font-size: 12px;">
              系统提示词会在每次调用模型时自动添加到对话开头
            </div>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton @click="handleTest" :loading="isTestLoading">测试连接</ElButton>
        <ElButton type="primary" @click="handleSubmit" :loading="isLoading">
          {{ dialogType === 'add' ? '创建' : '更新' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useModelStore } from '@/store/business/model'
import { ModelConfigService } from '@/api/modelApi'
import type { ModelConfigResponse, ModelConfigCreateRequest, ModelConfigUpdateRequest } from '@/types/api/model'
import { MODEL_PLATFORM_CONFIG, defaultModelConfigFormData } from '@/types/api/model'

// 组件属性
interface Props {
  visible: boolean
  type: Form.DialogType
  modelConfig?: ModelConfigResponse | null
}

// 组件事件
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'submit'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const modelStore = useModelStore()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const isLoading = ref(false)
const isTestLoading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.type === 'add' ? '新增模型配置' : '编辑模型配置'
})
const dialogType = computed(() => props.type)
// 表单数据
const formData = reactive<ModelConfigCreateRequest>({ ...defaultModelConfigFormData })



// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 1, max: 100, message: '模型名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台类型', trigger: 'change' }
  ],
  api_url: [
    { required: true, message: '请输入API地址', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/.+/, 
      message: 'API地址必须以http://或https://开头', 
      trigger: 'blur' 
    }
  ],
  timeout_seconds: [
    { required: true, message: '请输入超时时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 300, message: '超时时间必须在1-300秒之间', trigger: 'blur' }
  ],
  model_name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 1, max: 100, message: '模型名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  max_tokens: [
    { required: true, message: '请输入最大令牌数', trigger: 'blur' },
    { type: 'number', min: 1, max: 32768, message: '最大令牌数必须在1-32768之间', trigger: 'blur' }
  ]
}

// 平台变化处理
const handlePlatformChange = (platform: string) => {
  // 根据平台设置默认模型名称
  const defaultModelNames: Record<string, string> = {
    local: 'local-model',
    deepseek: 'deepseek-chat',
    qwen: 'qwen-turbo',
    ernie: 'ernie-bot',
    chatglm: 'chatglm-6b',
    doubao: 'doubao-pro',
    kimi: 'moonshot-v1',
    baichuan: 'baichuan2-7b',
    sensetime: 'sensechat'
  }

  if (defaultModelNames[platform]) {
    formData.model_name = defaultModelNames[platform]
  }
}

// 测试连接
const handleTest = async () => {
  try {
    // 先验证表单
    const valid = await formRef.value?.validate()
    if (!valid) return



    isTestLoading.value = true

    // 调用测试连接的API
    const result = await ModelConfigService.testModelConnection(formData)

    if (result.success) {
      ElMessage.success(result.message || '连接测试成功')
    } else {
      ElMessage.error(result.message || '连接测试失败')
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('连接测试失败')
  } finally {
    isTestLoading.value = false
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    // 验证表单
    const valid = await formRef.value?.validate()
    if (!valid) return
    

    
    isLoading.value = true
    
    if (props.type === 'add') {
      // 创建模型配置
      await modelStore.createModelConfig(formData)
    } else {
      // 更新模型配置
      const updateData: ModelConfigUpdateRequest = { ...formData }
      await modelStore.updateModelConfig(props.modelConfig!.id, updateData)
    }
    
    emit('submit')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  dialogVisible.value = false
}

// 对话框关闭处理
const handleDialogClosed = () => {
  // 重置表单
  formRef.value?.resetFields()
  Object.assign(formData, defaultModelConfigFormData)

}

// 监听模型配置变化
watch(() => props.modelConfig, (newModelConfig) => {
  if (newModelConfig && props.type === 'edit') {
    nextTick(() => {
      Object.assign(formData, {
        name: newModelConfig.name,
        platform: newModelConfig.platform,
        description: newModelConfig.description,
        api_url: newModelConfig.api_url,
        api_key: '', // 编辑时不显示原API Key
        timeout_seconds: newModelConfig.timeout_seconds,
        model_name: newModelConfig.model_name || '',
        max_tokens: newModelConfig.max_tokens || 2048,
        prompt: newModelConfig.prompt || ''
      })
    })
  }
}, { immediate: true })
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>
