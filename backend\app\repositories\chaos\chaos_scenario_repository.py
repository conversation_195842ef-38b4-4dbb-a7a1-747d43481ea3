"""
混沌测试故障场景数据访问层
"""
from typing import List, Optional, Dict, Any, Tuple

from sqlalchemy import and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.repositories.base import BaseRepository
from app.models.chaos.chaos_scenario import ChaosScenario
from app.schemas.chaos.chaos_scenario import ChaosScenarioCreate, ChaosScenarioUpdate


class ChaosScenarioRepository(BaseRepository[ChaosScenario, ChaosScenarioCreate, ChaosScenarioUpdate]):
    """
    故障场景模板数据访问层
    专注于场景模板相关的数据库操作
    """

    def __init__(self, db: AsyncSession):
        super().__init__(ChaosScenario, db)

    async def get_by_name(self, name: str) -> Optional[ChaosScenario]:
        """
        根据名称获取场景
        
        Args:
            name: 场景名称
            
        Returns:
            场景实例或None
        """
        return await self.get_by_field("name", name, unique=True)

    async def get_by_fault_type(self, fault_type: str) -> List[ChaosScenario]:
        """
        根据故障类型获取场景列表
        
        Args:
            fault_type: 故障类型
            
        Returns:
            场景列表
        """
        return await self.get_by_field("fault_type", fault_type, unique=False)


    async def get_custom_scenarios(self) -> List[ChaosScenario]:
        """
        获取自定义场景列表
        
        Returns:
            自定义场景列表
        """
        return await self.get_by_field("is_builtin", False, unique=False)

    async def get_active_scenarios(self) -> List[ChaosScenario]:
        """
        获取启用的场景列表
        
        Returns:
            启用的场景列表
        """
        return await self.get_by_field("is_active", True, unique=False)

    async def get_by_category(self, category: str) -> List[ChaosScenario]:
        """
        根据分类获取场景列表
        
        Args:
            category: 场景分类
            
        Returns:
            场景列表
        """
        return await self.get_by_field("category", category, unique=False)

    async def search_scenarios(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        keyword: Optional[str] = None,
        fault_type: Optional[str] = None,
        category: Optional[str] = None,
        is_builtin: Optional[bool] = None,
        is_active: Optional[bool] = None,
        tags: Optional[List[str]] = None,
        order_by: str = "usage_count",
        desc: bool = True
    ) -> Tuple[List[ChaosScenario], int]:
        """
        搜索场景
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            keyword: 搜索关键词
            fault_type: 故障类型
            category: 场景分类
            is_builtin: 是否内置
            is_active: 是否启用
            tags: 标签列表
            order_by: 排序字段
            desc: 是否降序
            
        Returns:
            (场景列表, 总数)
        """
        builder = self.query()

        # 关键词搜索
        if keyword:
            builder = builder.search(keyword, "name", "description")

        # 过滤条件
        filters = {}
        if fault_type:
            filters["fault_type"] = fault_type
        if category:
            filters["category"] = category
        if is_builtin is not None:
            filters["is_builtin"] = is_builtin
        if is_active is not None:
            filters["is_active"] = is_active

        if filters:
            builder = builder.filter_by(**filters)

        # 标签过滤
        if tags:
            tag_conditions = []
            for tag in tags:
                tag_conditions.append(ChaosScenario.tags.ilike(f"%{tag}%"))
            if tag_conditions:
                builder = builder.filter(or_(*tag_conditions))

        # 排序
        builder = builder.order_by(order_by, desc)

        # 分页查询
        return await builder.offset(skip).limit(limit).paginate(
            page=(skip // limit) + 1, per_page=limit
        )


    async def increment_usage_count(self, scenario_id: int) -> bool:
        """
        增加场景使用次数
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            是否更新成功
        """
        scenario = await self.get(scenario_id)
        if not scenario:
            return False

        scenario.usage_count = (scenario.usage_count or 0) + 1
        await self.db.commit()
        return True

    async def get_scenarios_by_tags(self, tags: List[str]) -> List[ChaosScenario]:
        """
        根据标签获取场景列表
        
        Args:
            tags: 标签列表
            
        Returns:
            场景列表
        """
        if not tags:
            return []

        conditions = []
        for tag in tags:
            conditions.append(ChaosScenario.tags.ilike(f"%{tag}%"))

        query = (
            select(ChaosScenario)
            .where(
                and_(
                    ChaosScenario.is_active == True,
                    or_(*conditions)
                )
            )
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def check_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查场景名称是否已存在
        
        Args:
            name: 场景名称
            exclude_id: 排除的场景ID（用于更新时检查）
            
        Returns:
            是否存在
        """
        query = select(ChaosScenario).where(ChaosScenario.name == name)
        
        if exclude_id:
            query = query.where(ChaosScenario.id != exclude_id)

        result = await self.db.execute(query)
        return result.scalar_one_or_none() is not None

    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取场景统计信息
        
        Returns:
            统计信息字典
        """
        # 总场景数
        total_query = select(func.count(ChaosScenario.id))
        total_result = await self.db.execute(total_query)
        total_count = total_result.scalar()

        # 内置vs自定义统计
        builtin_query = select(func.count(ChaosScenario.id)).where(ChaosScenario.is_builtin == True)
        builtin_result = await self.db.execute(builtin_query)
        builtin_count = builtin_result.scalar()

        # 按故障类型统计
        fault_type_query = (
            select(ChaosScenario.fault_type, func.count(ChaosScenario.id))
            .group_by(ChaosScenario.fault_type)
        )
        fault_type_result = await self.db.execute(fault_type_query)
        fault_type_stats = {row[0]: row[1] for row in fault_type_result.fetchall()}

        # 按分类统计
        category_query = (
            select(ChaosScenario.category, func.count(ChaosScenario.id))
            .where(ChaosScenario.category.isnot(None))
            .group_by(ChaosScenario.category)
        )
        category_result = await self.db.execute(category_query)
        category_stats = {row[0]: row[1] for row in category_result.fetchall()}

        return {
            "total_count": total_count,
            "builtin_count": builtin_count,
            "custom_count": total_count - builtin_count,
            "fault_type_stats": fault_type_stats,
            "category_stats": category_stats
        }
