"""
Redis连接客户端
支持Redis服务器的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional

from .base_client import BaseClient, ConnectionResult

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class RedisClient(BaseClient):
    """
    Redis连接客户端
    支持Redis服务器的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Redis客户端
        
        Args:
            config: Redis连接配置
                - host: Redis主机地址 (默认: localhost)
                - port: Redis端口 (默认: 6379)
                - password: Redis密码 (可选)
                - db: 数据库编号 (默认: 0)
                - username: Redis用户名 (可选，Redis 6.0+)
                - ssl: 是否使用SSL (默认: False)
        """
        super().__init__(config)
        
        if not REDIS_AVAILABLE:
            raise ImportError("Redis库未安装，请运行: pip install redis")
        
        # 设置默认值
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 6379)
        self.password = config.get('password', '')
        self.db = config.get('db', 0)
        self.username = config.get('username', '')
        self.ssl = config.get('ssl', False)
        
        self.redis_client = None

    def _create_redis_client(self, timeout: int = 10) -> redis.Redis:
        """
        创建Redis客户端
        
        Args:
            timeout: 连接超时时间
            
        Returns:
            redis.Redis: Redis客户端实例
        """
        connection_kwargs = {
            'host': self.host,
            'port': self.port,
            'db': self.db,
            'socket_timeout': timeout,
            'socket_connect_timeout': timeout,
            'ssl': self.ssl,
            'decode_responses': True,  # 自动解码响应
            'health_check_interval': 30  # 健康检查间隔
        }
        
        # 添加认证信息
        if self.password:
            connection_kwargs['password'] = self.password
        
        if self.username:
            connection_kwargs['username'] = self.username
        
        return redis.Redis(**connection_kwargs)

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立Redis连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 创建Redis客户端
            self.redis_client = self._create_redis_client(timeout)
            
            # 测试连接
            await self.redis_client.ping()
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到Redis服务器 {self.host}:{self.port}",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "db": self.db,
                    "ssl": self.ssl
                }
            )
            
        except redis.ConnectionError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "ConnectionError"}
            )
        except redis.AuthenticationError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis认证失败: {str(e)}",
                duration=duration,
                details={"error_type": "AuthenticationError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开Redis连接"""
        try:
            if self.redis_client:
                await self.redis_client.close()
                self.redis_client = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试Redis连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        start_time = time.time()
        
        try:
            # 如果没有建立连接，先建立连接
            if not self.redis_client:
                connect_result = await self.connect(timeout)
                if not connect_result.success:
                    return connect_result
            
            # 执行多个测试操作
            test_key = f"test:connection:{int(time.time())}"
            test_value = "connection_test"
            
            # 1. Ping测试
            ping_result = await self.redis_client.ping()
            
            # 2. 设置和获取测试
            await self.redis_client.set(test_key, test_value, ex=10)  # 10秒过期
            get_result = await self.redis_client.get(test_key)
            
            # 3. 删除测试键
            await self.redis_client.delete(test_key)
            
            # 4. 获取Redis信息
            info = await self.redis_client.info()
            
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message="Redis连接测试成功",
                duration=duration,
                details={
                    "ping_result": ping_result,
                    "set_get_test": get_result == test_value,
                    "redis_version": info.get('redis_version', '未知'),
                    "connected_clients": info.get('connected_clients', 0),
                    "used_memory_human": info.get('used_memory_human', '未知'),
                    "total_commands_processed": info.get('total_commands_processed', 0),
                    "keyspace_hits": info.get('keyspace_hits', 0),
                    "keyspace_misses": info.get('keyspace_misses', 0)
                }
            )
            
        except redis.ConnectionError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接测试失败: {str(e)}",
                duration=duration,
                details={"error_type": "ConnectionError"}
            )
        except redis.TimeoutError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接超时: {str(e)}",
                duration=duration,
                details={"error_type": "TimeoutError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis测试异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def execute_command(self, command: str, *args) -> Dict[str, Any]:
        """
        执行Redis命令
        
        Args:
            command: Redis命令
            *args: 命令参数
            
        Returns:
            Dict: 命令执行结果
        """
        if not self.redis_client:
            raise RuntimeError("Redis未连接")
        
        try:
            result = await self.redis_client.execute_command(command, *args)
            return {
                "success": True,
                "result": result,
                "command": f"{command} {' '.join(map(str, args))}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": f"{command} {' '.join(map(str, args))}"
            }

    async def get_info(self, section: Optional[str] = None) -> Dict[str, Any]:
        """
        获取Redis服务器信息
        
        Args:
            section: 信息段落 (可选)
            
        Returns:
            Dict: Redis服务器信息
        """
        if not self.redis_client:
            raise RuntimeError("Redis未连接")
        
        try:
            info = await self.redis_client.info(section)
            return {
                "success": True,
                "info": info
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            } 