/**
 * 头像显示工具函数
 */

// 导入默认头像
import defaultAvatarImg from '@/assets/img/user/avatar.webp'

/**
 * 获取完整的头像URL
 * @param avatarUrl 头像URL（可能是相对路径或完整URL）
 * @param defaultAvatar 默认头像路径
 * @returns 完整的头像URL
 */
export function getAvatarUrl(avatarUrl?: string | null, defaultAvatar = defaultAvatarImg): string {
  if (!avatarUrl) {
    return defaultAvatar
  }

  if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
    return avatarUrl
  }

  if (avatarUrl.startsWith('/')) {
    // 开发环境：使用相对路径，通过Vite代理访问
    // 生产环境：拼接API URL
    if (import.meta.env.DEV) {
      // 开发环境直接返回相对路径，让Vite代理处理
      return avatarUrl
    } else {
      // 生产环境拼接API URL
      const apiUrl = import.meta.env.VITE_API_URL
      if (apiUrl) {
        const newUrl = `${apiUrl.replace(/\/$/, '')}${avatarUrl}`
        return newUrl
      }
      return avatarUrl
    }
  }

  return defaultAvatar
}

/**
 * 获取用户头像URL（带默认头像处理）
 * @param user 用户信息对象
 * @returns 头像URL
 */
export function getUserAvatarUrl(user?: { avatar?: string | null }): string {
  return getAvatarUrl(user?.avatar)
} 