"""
基础 Pydantic 模式
"""
from typing import Any, Generic, List, Optional, TypeVar
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field

T = TypeVar('T')


class BaseSchema(BaseModel):
    """
    基础模式类
    """
    model_config = ConfigDict(from_attributes=True)


class BaseCreateSchema(BaseSchema):
    """
    创建模式基类
    """
    created_by: Optional[str] = Field(default=None, description="创建人")


class BaseUpdateSchema(BaseSchema):
    """
    更新模式基类
    """
    updated_by: Optional[str] = Field(default=None, description="更新人")


class BaseResponseSchema(BaseSchema):
    """
    响应模式基类
    """
    id: int = Field(description="主键ID")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[str] = Field(default=None, description="创建人")
    updated_by: Optional[str] = Field(default=None, description="更新人")


class APIResponse(BaseModel, Generic[T]):
    """
    API 统一响应格式
    """
    success: bool = Field(default=True, description="是否成功")
    code: int = Field(default=200, description="状态码")
    message: str = Field(default="操作成功", description="响应消息")
    data: Optional[T] = Field(default=None, description="响应数据")


class PaginationData(BaseModel, Generic[T]):
    """
    分页数据模式
    """
    records: List[T] = Field(description="数据列表")
    total: int = Field(description="总数")
    current: int = Field(description="当前页")
    size: int = Field(description="页大小")


class PaginationResponse(BaseModel, Generic[T]):
    """
    分页响应模式
    """
    success: bool = Field(default=True, description="是否成功")
    code: int = Field(default=200, description="状态码")
    message: str = Field(default="查询成功", description="响应消息")
    data: PaginationData[T] = Field(description="分页数据") 