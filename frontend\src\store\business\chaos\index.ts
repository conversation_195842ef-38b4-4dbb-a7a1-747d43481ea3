/**
 * 混沌测试模块主状态管理
 * 统一管理混沌测试相关的状态和操作
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useChaosTasksStore } from './tasks'
import { useChaosScenariosStore } from './scenarios'
import { useChaosExecutionsStore } from './executions'
import { useLoading } from '@/store/shared/loading'
import { globalCache } from '@/store/shared/cache'
import ChaosService from '@/api/chaosApi'
import type { 
  ChaosBladeInstallRequest,
  ChaosBladeStatusResponse 
} from '@/types/api/chaos'

export const useChaosStore = defineStore('chaos', () => {
  // ==================== 子模块状态 ====================
  
  // 获取子模块store实例
  const tasksStore = useChaosTasksStore()
  const scenariosStore = useChaosScenariosStore()
  const executionsStore = useChaosExecutionsStore()

  // ==================== ChaosBlade状态 ====================
  
  const chaosBladeStatus = ref<ChaosBladeStatusResponse[]>([])
  const chaosBladeLoading = useLoading()

  // ==================== 计算属性 ====================
  
  // 聚合统计信息
  const overallStats = computed(() => ({
    totalTasks: tasksStore.totalTasks,
    runningTasks: tasksStore.runningTasks.length,
    totalScenarios: scenariosStore.totalScenarios,
    totalExecutions: executionsStore.totalExecutions,
    recentExecutions: executionsStore.recentExecutions
  }))

  // 是否有任何加载状态
  const isAnyLoading = computed(() =>
    tasksStore.loading.isLoading ||
    scenariosStore.loading.isLoading ||
    executionsStore.loading.isLoading ||
    chaosBladeLoading.isLoading
  )

  // ==================== ChaosBlade管理方法 ====================
  
  /**
   * 安装ChaosBlade
   */
  const installChaosBlade = async (data: ChaosBladeInstallRequest) => {
    return await chaosBladeLoading.withLoading(async () => {
      const response = await ChaosService.installChaosBlade(data)
      return response
    })
  }

  /**
   * 检查ChaosBlade状态
   */
  const checkChaosBladeStatus = async (envId: number) => {
    return await chaosBladeLoading.withLoading(async () => {
      const response = await ChaosService.checkChaosBladeStatus(envId)
      chaosBladeStatus.value = response
      
      // 缓存状态信息
      globalCache.set(`chaosblade_status_${envId}`, response, 2 * 60 * 1000) // 2分钟缓存
      
      return response
    })
  }

  /**
   * 测试ChaosBlade连接
   */
  const testChaosBladeConnection = async (envId: number) => {
    return await chaosBladeLoading.withLoading(async () => {
      const response = await ChaosService.testChaosBladeConnection(envId)
      return response
    })
  }

  // /**
  //  * 执行测试命令 - 已废弃，前端不再使用
  //  */
  // const executeTestCommand = async (hostInfo: any, faultConfig: any) => {
  //   return await chaosBladeLoading.withLoading(async () => {
  //     const response = await ChaosService.executeTestCommand(hostInfo, faultConfig)
  //     return response
  //   })
  // }

  /**
   * 获取ChaosBlade版本信息
   */
  const getChaosBladeVersion = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('chaosblade_version')
    if (cached) {
      return cached
    }

    return await chaosBladeLoading.withLoading(async () => {
      const response = await ChaosService.getChaosBladeVersion()

      // 缓存版本信息（较长时间）
      globalCache.set('chaosblade_version', response, 30 * 60 * 1000) // 30分钟缓存

      return response
    })
  }

  /**
   * 卸载ChaosBlade
   */
  const uninstallChaosBlade = async (data: ChaosBladeInstallRequest) => {
    return await chaosBladeLoading.withLoading(async () => {
      const response = await ChaosService.uninstallChaosBlade(data)

      // 清除相关缓存
      globalCache.remove('chaosblade_status')
      globalCache.remove('chaosblade_version')

      return response
    })
  }

  // ==================== 统一操作方法 ====================
  
  /**
   * 初始化所有数据
   */
  const initializeAll = async () => {
    await Promise.allSettled([
      tasksStore.fetchTasks({}),
      scenariosStore.fetchScenarios({}),
      executionsStore.fetchExecutions({})
    ])
  }

  /**
   * 刷新所有数据
   */
  const refreshAll = async () => {
    // 清除相关缓存
    globalCache.clear()
    
    // 重新加载数据
    await initializeAll()
  }

  /**
   * 重置所有状态
   */
  const resetAll = () => {
    tasksStore.resetState()
    scenariosStore.resetState()
    executionsStore.resetState()
    chaosBladeStatus.value = []
    chaosBladeLoading.reset()
  }

  // ==================== 导出 ====================
  
  return {
    // 子模块
    tasksStore,
    scenariosStore,
    executionsStore,
    
    // ChaosBlade状态
    chaosBladeStatus: readonly(chaosBladeStatus),
    chaosBladeLoading,
    
    // 计算属性
    overallStats,
    isAnyLoading,
    
    // ChaosBlade方法
    installChaosBlade,
    checkChaosBladeStatus,
    testChaosBladeConnection,
    // executeTestCommand, // 已废弃
    getChaosBladeVersion,
    uninstallChaosBlade,
    
    // 统一操作
    initializeAll,
    refreshAll,
    resetAll
  }
})
