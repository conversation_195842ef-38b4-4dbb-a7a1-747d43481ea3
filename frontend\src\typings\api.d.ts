/**
 * namespace: Api
 *
 * 所有接口相关类型定义
 * 在.vue文件使用会报错，需要在 eslint.config.mjs 中配置 globals: { Api: 'readonly' }
 */
declare namespace Api {
  /** 基础类型 */
  namespace Http {
    /** 基础响应 */
    interface BaseResponse<T = any> {
      // 状态码
      code: number
      // 消息
      msg: string
      // 数据
      data: T
    }
  }

  /** 通用类型 */
  namespace Common {
    /** 分页参数 */
    interface PaginatingParams {
      /** 当前页码 */
      current: number
      /** 每页条数 */
      size: number
      /** 总条数 */
      total: number
    }

    /** 通用搜索参数 */
    type PaginatingSearchParams = Pick<PaginatingParams, 'current' | 'size'>

    /** 启用状态 */
    type EnableStatus = '1' | '2'

    /** 基础响应 */
    interface BaseResponse<T = any> {
      success: boolean
      code: number
      message: string
      data: T
    }
  }

  /** 认证类型 */
  namespace Auth {
    /** 登录参数 */
    interface LoginParams {
      username: string
      password: string
    }

    /** 登录响应 */
    interface LoginResponse {
      token: string
      refreshToken: string
    }
  }

  /** 用户类型 */
  namespace User {
    /** 用户信息 */
    interface UserInfo {
      userId: number
      username: string
      email?: string
      nickname?: string
      avatar?: string
      roles: string[]
      buttons: string[]
      status?: string
    }
    /** 用户列表数据 */
    interface UserListData {
      records: UserListItem[]
      current: number
      size: number
      total: number
    }

    /** 用户列表项 */
    interface UserListItem {
      id: number
      avatar: string
      create_by: string
      create_at: string
      update_by: string
      update_at: string
      status: '1' | '2' | '3' | '4' // 1: 在线 2: 离线 3: 异常 4: 注销
      username: string
      nickname: string
      email: string
      userRoles: string[]
    }

    /** 用户创建参数 */
    interface UserCreateParams {
      username: string
      nickname?: string
      email?: string
      password: string
      role_ids?: number[]
    }

    /** 用户更新参数 */
    interface UserUpdateParams {
      username?: string
      nickname?: string
      email?: string
      role_ids?: number[]
      is_active?: boolean
    }

    /** 角色信息 */
    interface RoleInfo {
      id: number
      roleCode: string
      roleName: string
      description?: string
      status: string
    }
  }

  /** 环境管理类型 */
  namespace Environment {
    // 重新导出环境相关类型
    type EnvironmentType = import('@/types/api/environment').EnvironmentType
    type EnvironmentStatus = import('@/types/api/environment').EnvironmentStatus
    type EnvironmentBase = import('@/types/api/environment').EnvironmentBase
    type EnvironmentCreateRequest = import('@/types/api/environment').EnvironmentCreateRequest
    type EnvironmentUpdateRequest = import('@/types/api/environment').EnvironmentUpdateRequest
    type EnvironmentResponse = import('@/types/api/environment').EnvironmentResponse
    type EnvironmentListItem = import('@/types/api/environment').EnvironmentListItem
    type EnvironmentListResponse = import('@/types/api/environment').EnvironmentListResponse
    type EnvironmentListParams = import('@/types/api/environment').EnvironmentListParams
    type ConnectionTestRequest = import('@/types/api/environment').ConnectionTestRequest
    type ConnectionTestResponse = import('@/types/api/environment').ConnectionTestResponse
    type SupportedEnvironmentType = import('@/types/api/environment').SupportedEnvironmentType
  }
}

