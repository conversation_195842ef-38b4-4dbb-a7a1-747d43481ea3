"""
混沌测试故障场景业务服务
"""
from typing import Dict, Any, List, Optional, Type
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.chaos.chaos_scenario_repository import ChaosScenarioRepository
from app.models.chaos.chaos_scenario import ChaosScenario
from app.schemas.chaos.chaos_scenario import (
    ChaosScenarioCreate, ChaosScenarioUpdate, ChaosScenarioResponse, ChaosScenarioListResponse,
    ChaosScenarioSearchParams, ChaosScenarioStatistics, ChaosScenarioValidateRequest,
    ChaosScenarioValidateResponse, ChaosScenarioTemplate, ChaosScenarioImportRequest,
    ChaosScenarioImportResponse, ChaosScenarioExportResponse
)
from app.schemas.base import PaginationData
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.logger import setup_logger

logger = setup_logger()


class ChaosScenarioService(BaseService[ChaosScenario, ChaosScenarioCreate, ChaosScenarioUpdate, ChaosScenarioResponse]):
    """
    故障场景模板业务服务
    继承BaseService，提供场景模板管理的核心业务逻辑
    """

    def __init__(self, db: AsyncSession):
        self.repository = ChaosScenarioRepository(db)
        super().__init__(db, self.repository)

    @property
    def model_class(self) -> Type[ChaosScenario]:
        return ChaosScenario

    @property
    def response_schema_class(self) -> Type[ChaosScenarioResponse]:
        return ChaosScenarioResponse

    # ==================== 钩子方法实现 ====================

    async def _validate_before_create(self, create_data: ChaosScenarioCreate, **kwargs) -> None:
        """创建前业务验证"""
        # 验证场景名称是否重复
        existing_scenario = await self.repository.get_by_name(create_data.name)
        if existing_scenario:
            raise_validation_error(f"场景名称 '{create_data.name}' 已存在")

        # 验证参数结构定义
        self._validate_param_schema(create_data.param_schema)

        # 验证默认参数与结构定义的一致性
        errors = self._validate_params_against_schema(create_data.default_params, create_data.param_schema)
        if errors:
            raise_validation_error(f"默认参数验证失败: {errors}")

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前数据处理"""
        # 设置默认值
        create_dict["is_builtin"] = False
        create_dict["is_active"] = True
        create_dict["usage_count"] = 0
        
        return create_dict

    async def _process_after_create(self, obj: ChaosScenario, create_data) -> None:
        """创建后处理"""
        logger.info(f"故障场景创建成功: {obj.name} (ID: {obj.id})")

    def _convert_to_response(self, obj: ChaosScenario) -> ChaosScenarioResponse:
        """转换为响应对象"""
        return ChaosScenarioResponse(
            id=obj.id,
            name=obj.name,
            fault_type=obj.fault_type,
            description=obj.description,
            default_params=obj.default_params or {},
            param_schema=obj.param_schema or {},
            is_builtin=obj.is_builtin,
            is_active=obj.is_active,
            category=obj.category,
            usage_count=obj.usage_count or 0,
            tags=obj.tags,
            created_at=obj.created_at,
            updated_at=obj.updated_at,
            created_by=obj.created_by,
            updated_by=obj.updated_by,
            tag_list=obj.tag_list
        )

    # ==================== 业务方法 ====================

    async def create_scenario(self, scenario_data: ChaosScenarioCreate, current_user_id: int) -> ChaosScenarioResponse:
        """创建故障场景"""
        return await self.create(scenario_data, str(current_user_id))

    async def get_scenario_by_id(self, scenario_id: int) -> ChaosScenarioResponse:
        """获取场景详情"""
        return await self.get_by_id(scenario_id)

    async def search_scenarios(self, params: ChaosScenarioSearchParams) -> PaginationData[ChaosScenarioListResponse]:
        """搜索场景"""
        skip = (params.page - 1) * params.size
        
        scenarios, total = await self.repository.search_scenarios(
            skip=skip,
            limit=params.size,
            keyword=params.keyword,
            fault_type=params.fault_type,
            category=params.category,
            is_builtin=params.is_builtin,
            is_active=params.is_active,
            tags=params.tags,
            order_by=params.order_by,
            desc=params.desc
        )

        # 转换为列表响应格式
        scenario_list = []
        for scenario in scenarios:
            scenario_list.append(ChaosScenarioListResponse(
                id=scenario.id,
                name=scenario.name,
                fault_type=scenario.fault_type,
                description=scenario.description,
                category=scenario.category,
                is_builtin=scenario.is_builtin,
                is_active=scenario.is_active,
                usage_count=scenario.usage_count or 0,
                tag_list=scenario.tag_list,
                created_at=scenario.created_at,
                created_by=scenario.created_by
            ))

        return PaginationData(
            records=scenario_list,
            total=total,
            current=params.page,
            size=params.size
        )


    async def validate_scenario_params(self, request: ChaosScenarioValidateRequest) -> ChaosScenarioValidateResponse:
        """验证场景参数"""
        scenario = await self.repository.get(request.scenario_id)
        if not scenario:
            raise_not_found(f"场景ID {request.scenario_id} 不存在")

        # 验证参数
        errors = scenario.validate_params(request.params)
        
        # 合并默认参数
        merged_params = scenario.merge_with_defaults(request.params)

        return ChaosScenarioValidateResponse(
            valid=len(errors) == 0,
            errors=errors,
            merged_params=merged_params
        )

    async def increment_usage(self, scenario_id: int) -> bool:
        """增加场景使用次数"""
        return await self.repository.increment_usage_count(scenario_id)


    async def import_scenarios(self, request: ChaosScenarioImportRequest, current_user_id: int) -> ChaosScenarioImportResponse:
        """导入场景模板"""
        success_count = 0
        failed_count = 0
        skipped_count = 0
        errors = []
        imported_scenarios = []

        for template in request.scenarios:
            try:
                # 检查是否已存在
                existing = await self.repository.get_by_name(template.name)
                if existing and not request.overwrite_existing:
                    skipped_count += 1
                    continue

                # 创建场景数据
                scenario_data = ChaosScenarioCreate(
                    name=template.name,
                    fault_type=template.fault_type,
                    description=template.description,
                    default_params=template.default_params,
                    param_schema=template.param_schema,
                    category=template.category,
                    tags=",".join(template.tags) if template.tags else None,
                    created_by=str(current_user_id)
                )

                if existing and request.overwrite_existing:
                    # 更新现有场景
                    update_data = ChaosScenarioUpdate(
                        description=template.description,
                        default_params=template.default_params,
                        param_schema=template.param_schema,
                        category=template.category,
                        tags=",".join(template.tags) if template.tags else None,
                        updated_by=str(current_user_id)
                    )
                    scenario = await self.update(existing.id, update_data, str(current_user_id))
                else:
                    # 创建新场景
                    scenario = await self.create_scenario(scenario_data, current_user_id)

                imported_scenarios.append(ChaosScenarioListResponse(
                    id=scenario.id,
                    name=scenario.name,
                    fault_type=scenario.fault_type,
                    description=scenario.description,
                    category=scenario.category,
                    is_builtin=scenario.is_builtin,
                    is_active=scenario.is_active,
                    usage_count=scenario.usage_count,
                    tag_list=scenario.tag_list,
                    created_at=scenario.created_at,
                    created_by=scenario.created_by
                ))
                success_count += 1

            except Exception as e:
                failed_count += 1
                errors.append(f"导入场景 '{template.name}' 失败: {str(e)}")
                logger.error(f"导入场景失败: {str(e)}")

        return ChaosScenarioImportResponse(
            success_count=success_count,
            failed_count=failed_count,
            skipped_count=skipped_count,
            errors=errors,
            imported_scenarios=imported_scenarios
        )

    async def export_scenarios(self, scenario_ids: Optional[List[int]] = None) -> ChaosScenarioExportResponse:
        """导出场景模板"""
        if scenario_ids:
            scenarios = await self.repository.find_by_ids(scenario_ids)
        else:
            scenarios = await self.repository.get_active_scenarios()

        templates = []
        for scenario in scenarios:
            templates.append(ChaosScenarioTemplate(
                name=scenario.name,
                fault_type=scenario.fault_type,
                description=scenario.description or "",
                category=scenario.category or "",
                default_params=scenario.default_params or {},
                param_schema=scenario.param_schema or {},
                tags=scenario.tag_list
            ))

        return ChaosScenarioExportResponse(
            scenarios=templates,
            export_time=datetime.now(),
            total_count=len(templates)
        )

    async def initialize_builtin_scenarios(self) -> None:
        """初始化内置场景模板"""
        builtin_scenarios = self._get_builtin_scenario_templates()
        
        for template in builtin_scenarios:
            try:
                # 检查是否已存在
                existing = await self.repository.get_by_name(template["name"])
                if existing:
                    continue

                # 创建内置场景
                scenario = ChaosScenario(
                    name=template["name"],
                    fault_type=template["fault_type"],
                    description=template["description"],
                    default_params=template["default_params"],
                    param_schema=template["param_schema"],
                    is_builtin=True,
                    is_active=True,
                    category=template.get("category"),
                    tags=template.get("tags"),
                    usage_count=0,
                    created_by="system"
                )

                self.repository.db.add(scenario)
                await self.repository.db.commit()
                logger.info(f"初始化内置场景: {template['name']}")

            except Exception as e:
                logger.error(f"初始化内置场景失败: {str(e)}")

    # ==================== 私有方法 ====================

    def _validate_param_schema(self, param_schema: Dict[str, Any]) -> None:
        """验证参数结构定义"""
        if not isinstance(param_schema, dict):
            raise_validation_error("参数结构定义必须为字典格式")

        for param_name, param_def in param_schema.items():
            if not isinstance(param_def, dict):
                raise_validation_error(f"参数 {param_name} 的定义必须为字典格式")
            
            if "type" not in param_def:
                raise_validation_error(f"参数 {param_name} 必须包含type字段")

    def _validate_params_against_schema(self, params: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, str]:
        """根据结构定义验证参数"""
        errors = {}
        
        for param_name, param_def in schema.items():
            if param_def.get("required", False) and param_name not in params:
                errors[param_name] = "参数必填"
                continue
                
            if param_name in params:
                value = params[param_name]
                param_type = param_def.get("type")
                
                # 类型验证
                if param_type == "integer" and not isinstance(value, int):
                    errors[param_name] = "参数类型必须为整数"
                elif param_type == "float" and not isinstance(value, (int, float)):
                    errors[param_name] = "参数类型必须为数字"
                elif param_type == "string" and not isinstance(value, str):
                    errors[param_name] = "参数类型必须为字符串"
                
                # 范围验证
                if "min" in param_def and value < param_def["min"]:
                    errors[param_name] = f"参数值不能小于{param_def['min']}"
                if "max" in param_def and value > param_def["max"]:
                    errors[param_name] = f"参数值不能大于{param_def['max']}"
        
        return errors

    def _get_builtin_scenario_templates(self) -> List[Dict[str, Any]]:
        """从JSON文件获取内置场景模板定义"""
        import json
        import os
        from pathlib import Path

        # 获取JSON文件路径
        current_dir = Path(__file__).parent.parent.parent.parent  # 回到backend目录
        json_file = current_dir / "chaos_scenarios_builtin.json"

        try:
            if json_file.exists():
                with open(json_file, 'r', encoding='utf-8') as f:
                    scenarios = json.load(f)
                logger.info(f"成功加载 {len(scenarios)} 个内置场景模板")
                return scenarios
            else:
                logger.warning(f"内置场景配置文件不存在: {json_file}")
                return []
        except Exception as e:
            logger.error(f"加载内置场景配置文件失败: {str(e)}")
            return []
