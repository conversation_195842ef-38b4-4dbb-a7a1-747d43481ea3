"""
Kafka连接客户端
支持Kafka集群的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional, List

from .base_client import BaseClient, ConnectionResult

try:
    from kafka import KafkaProducer, KafkaConsumer
    from kafka.errors import KafkaError
    KAFKA_AVAILABLE = True
except ImportError:
    KAFKA_AVAILABLE = False


class KafkaClient(BaseClient):
    """
    Kafka连接客户端
    支持Kafka集群的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Kafka客户端
        
        Args:
            config: Kafka连接配置
                - bootstrap_servers: Kafka服务器列表 (必需)
                - security_protocol: 安全协议 (默认: PLAINTEXT)
                - sasl_mechanism: SASL机制 (可选)
                - sasl_plain_username: SASL用户名 (可选)
                - sasl_plain_password: SASL密码 (可选)
                - ssl_cafile: SSL CA文件路径 (可选)
                - ssl_certfile: SSL证书文件路径 (可选)
                - ssl_keyfile: SSL密钥文件路径 (可选)
        """
        super().__init__(config)
        
        if not KAFKA_AVAILABLE:
            raise ImportError("Kafka库未安装，请运行: pip install kafka-python")
        
        # 验证必要配置
        if 'bootstrap_servers' not in config:
            raise ValueError("bootstrap_servers 是必需的")
        
        # 设置默认值
        self.bootstrap_servers = config['bootstrap_servers']
        if isinstance(self.bootstrap_servers, str):
            self.bootstrap_servers = [self.bootstrap_servers]
        
        self.security_protocol = config.get('security_protocol', 'PLAINTEXT')
        self.sasl_mechanism = config.get('sasl_mechanism', 'PLAIN')
        self.sasl_plain_username = config.get('sasl_plain_username', '')
        self.sasl_plain_password = config.get('sasl_plain_password', '')
        
        # SSL配置
        self.ssl_cafile = config.get('ssl_cafile', '')
        self.ssl_certfile = config.get('ssl_certfile', '')
        self.ssl_keyfile = config.get('ssl_keyfile', '')
        
        self.producer = None
        self.consumer = None

    def _get_connection_config(self) -> Dict[str, Any]:
        """
        获取连接配置
        
        Returns:
            Dict: 连接配置字典
        """
        connection_config = {
            'bootstrap_servers': self.bootstrap_servers,
            'security_protocol': self.security_protocol,
            'request_timeout_ms': 10000,
            'api_version': (0, 10, 1)
        }
        
        # 添加SASL配置
        if self.security_protocol in ['SASL_PLAINTEXT', 'SASL_SSL']:
            connection_config.update({
                'sasl_mechanism': self.sasl_mechanism,
                'sasl_plain_username': self.sasl_plain_username,
                'sasl_plain_password': self.sasl_plain_password
            })
        
        # 添加SSL配置
        if self.security_protocol in ['SSL', 'SASL_SSL']:
            ssl_config = {}
            if self.ssl_cafile:
                ssl_config['ssl_cafile'] = self.ssl_cafile
            if self.ssl_certfile:
                ssl_config['ssl_certfile'] = self.ssl_certfile
            if self.ssl_keyfile:
                ssl_config['ssl_keyfile'] = self.ssl_keyfile
            connection_config.update(ssl_config)
        
        return connection_config

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立Kafka连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 获取连接配置
            connection_config = self._get_connection_config()
            
            # 创建生产者进行连接测试
            self.producer = KafkaProducer(**connection_config)
            
            # 获取集群元数据来验证连接
            metadata = self.producer.list_topics(timeout=timeout)
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到Kafka集群 {self.bootstrap_servers}",
                duration=duration,
                details={
                    "bootstrap_servers": self.bootstrap_servers,
                    "security_protocol": self.security_protocol,
                    "topics_count": len(metadata.topics) if metadata else 0,
                    "brokers_count": len(metadata.brokers) if metadata else 0
                }
            )
            
        except KafkaError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kafka连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "KafkaError", "error_code": getattr(e, 'errno', None)}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kafka连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开Kafka连接"""
        try:
            if self.producer:
                self.producer.close()
                self.producer = None
            if self.consumer:
                self.consumer.close()
                self.consumer = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试Kafka连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        start_time = time.time()
        
        try:
            # 如果没有建立连接，先建立连接
            if not self.producer:
                connect_result = await self.connect(timeout)
                if not connect_result.success:
                    return connect_result
            
            # 执行连接测试
            metadata = self.producer.list_topics(timeout=timeout)
            
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"Kafka连接测试成功",
                duration=duration,
                details={
                    "bootstrap_servers": self.bootstrap_servers,
                    "topics": list(metadata.topics.keys()) if metadata else [],
                    "brokers": [f"{broker.host}:{broker.port}" for broker in metadata.brokers.values()] if metadata else []
                }
            )
            
        except KafkaError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kafka连接测试失败: {str(e)}",
                duration=duration,
                details={"error_type": "KafkaError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kafka连接测试异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def get_topics(self) -> List[str]:
        """
        获取主题列表
        
        Returns:
            List[str]: 主题名称列表
        """
        if not self.producer:
            raise RuntimeError("Kafka未连接")
        
        try:
            metadata = self.producer.list_topics()
            return list(metadata.topics.keys()) if metadata else []
        except Exception as e:
            raise RuntimeError(f"获取主题列表失败: {str(e)}")

    async def create_topic(self, topic_name: str, num_partitions: int = 1, replication_factor: int = 1) -> bool:
        """
        创建主题
        
        Args:
            topic_name: 主题名称
            num_partitions: 分区数
            replication_factor: 副本因子
            
        Returns:
            bool: 是否创建成功
        """
        # 注意：kafka-python库不直接支持创建主题
        # 这里只是一个占位符，实际实现需要使用kafka-admin或其他工具
        raise NotImplementedError("创建主题功能需要使用kafka-admin库")

    async def send_message(self, topic: str, message: str, key: Optional[str] = None) -> bool:
        """
        发送消息
        
        Args:
            topic: 主题名称
            message: 消息内容
            key: 消息键 (可选)
            
        Returns:
            bool: 是否发送成功
        """
        if not self.producer:
            raise RuntimeError("Kafka未连接")
        
        try:
            future = self.producer.send(
                topic, 
                value=message.encode('utf-8'),
                key=key.encode('utf-8') if key else None
            )
            # 等待发送完成
            record_metadata = future.get(timeout=10)
            return True
        except Exception as e:
            raise RuntimeError(f"发送消息失败: {str(e)}")
