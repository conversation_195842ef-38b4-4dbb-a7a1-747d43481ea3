"""
通用 Pydantic 模式
"""
from typing import Optional
from pydantic import BaseModel, Field


class PaginationParams(BaseModel):
    """
    分页参数模式
    """
    current: int = Field(default=1, ge=1, description="当前页码")
    size: int = Field(default=20, ge=1, le=100, description="每页条数")


class SearchParams(PaginationParams):
    """
    搜索参数模式
    """
    keyword: Optional[str] = Field(default=None, description="搜索关键词")


class EnableStatus(BaseModel):
    """
    启用状态模式
    """
    status: str = Field(pattern="^[1-2]$", description="状态: 1-启用, 2-禁用") 