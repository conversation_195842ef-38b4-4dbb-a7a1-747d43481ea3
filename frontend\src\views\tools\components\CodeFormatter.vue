<template>
  <div class="code-formatter">
    <div class="tool-header">
      <div class="tool-title">
        <i class="tool-icon iconfont-sys">&#xe6ed;</i>
        <h2>代码格式化工具</h2>
      </div>
      <p class="tool-description">
        支持多种编程语言的代码格式化、美化和压缩
      </p>
    </div>

    <div class="tool-content">
      <!-- 语言选择和操作选项 -->
      <div class="config-section">
        <div class="config-row">
          <div class="config-item">
            <label class="config-label">编程语言</label>
            <ElSelect v-model="selectedLanguage" @change="handleLanguageChange">
              <ElOption
                v-for="lang in supportedLanguages"
                :key="lang.value"
                :label="lang.label"
                :value="lang.value"
              />
            </ElSelect>
          </div>
          
          <div class="config-item">
            <label class="config-label">操作类型</label>
            <ElSelect v-model="operationType" @change="handleOperationChange">
              <ElOption label="格式化（美化）" value="format" />
              <ElOption label="压缩（最小化）" value="minify" />
              <ElOption label="仅验证语法" value="validate" />
            </ElSelect>
          </div>
        </div>

        <!-- 格式化选项 -->
        <div v-if="operationType === 'format'" class="format-options">
          <div class="option-row">
            <div class="option-item">
              <label class="option-label">缩进大小</label>
              <ElInputNumber v-model="formatOptions.indentSize" :min="2" :max="8" />
            </div>
            <div class="option-item">
              <label class="option-label">缩进类型</label>
              <ElSelect v-model="formatOptions.indentType">
                <ElOption label="空格" value="space" />
                <ElOption label="制表符" value="tab" />
              </ElSelect>
            </div>
            <div class="option-item">
              <ElCheckbox v-model="formatOptions.insertFinalNewline">
                文件末尾插入换行
              </ElCheckbox>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <div class="section-header">
          <h3 class="section-title">输入代码</h3>
          <div class="section-actions">
            <ElButton size="small" @click="clearInput">
              <i class="iconfont-sys">&#xe622;</i>
              清空
            </ElButton>
            <ElButton size="small" @click="pasteInput">
              <i class="iconfont-sys">&#xe623;</i>
              粘贴
            </ElButton>
            <ElButton size="small" @click="loadSample">
              <i class="iconfont-sys">&#xe629;</i>
              示例
            </ElButton>
          </div>
        </div>
        
        <ElInput
          v-model="inputCode"
          type="textarea"
          :rows="12"
          :placeholder="getPlaceholder()"
          class="code-textarea"
          @input="handleInputChange"
        />
        
        <!-- 语法错误提示 -->
        <div v-if="syntaxError" class="error-message">
          <i class="iconfont-sys">&#xe62a;</i>
          {{ syntaxError }}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <ElButton
          type="primary"
          size="large"
          @click="handleProcess"
          :loading="processing"
          class="process-button"
        >
          <i class="iconfont-sys">&#xe626;</i>
          {{ getActionButtonText() }}
        </ElButton>
      </div>

      <!-- 结果展示区域 -->
      <div v-if="result" class="result-section">
        <div class="section-header">
          <h3 class="section-title">处理结果</h3>
          <div class="section-actions">
            <ElButton size="small" @click="copyResult">
              <i class="iconfont-sys">&#xe627;</i>
              复制结果
            </ElButton>
            <ElButton v-if="operationType !== 'validate'" size="small" @click="downloadResult">
              <i class="iconfont-sys">&#xe62b;</i>
              下载
            </ElButton>
          </div>
        </div>
        
        <div class="result-content">
          <!-- 验证结果 -->
          <div v-if="operationType === 'validate'" class="validation-result">
            <div class="validation-status" :class="{ success: result.valid, error: !result.valid }">
              <i class="iconfont-sys" v-html="result.valid ? '&#xe62c;' : '&#xe62d;'"></i>
              <span>{{ result.valid ? '语法正确' : '语法错误' }}</span>
            </div>
            <div v-if="!result.valid && result.error" class="validation-error">
              {{ result.error }}
            </div>
            <div v-if="result.valid && result.stats" class="code-stats">
              <div class="stat-item">
                <label>代码行数：</label>
                <span>{{ result.stats.lines }}</span>
              </div>
              <div class="stat-item">
                <label>字符数：</label>
                <span>{{ result.stats.characters }}</span>
              </div>
              <div class="stat-item">
                <label>文件大小：</label>
                <span>{{ formatFileSize(result.stats.size) }}</span>
              </div>
            </div>
          </div>

          <!-- 格式化/压缩结果 -->
          <ElInput
            v-else
            v-model="result.output"
            type="textarea"
            :rows="12"
            readonly
            class="result-textarea"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'CodeFormatter' })

// 支持的编程语言
const supportedLanguages = [
  { label: 'JavaScript', value: 'javascript' },
  { label: 'TypeScript', value: 'typescript' },
  { label: 'JSON', value: 'json' },
  { label: 'HTML', value: 'html' },
  { label: 'CSS', value: 'css' },
  { label: 'SQL', value: 'sql' },
  { label: 'XML', value: 'xml' },
  { label: 'Python', value: 'python' },
  { label: 'Java', value: 'java' }
]

// 响应式数据
const selectedLanguage = ref('javascript')
const operationType = ref('format')
const inputCode = ref('')
const processing = ref(false)
const syntaxError = ref('')
const result = ref<any>(null)

// 格式化选项
const formatOptions = ref({
  indentSize: 2,
  indentType: 'space',
  insertFinalNewline: true
})

// 示例代码
const sampleCodes: { [key: string]: string } = {
  javascript: `function fibonacci(n) {
if (n <= 1) return n;
return fibonacci(n - 1) + fibonacci(n - 2);
}

const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(x => x * 2);
console.log(doubled);`,
  
  json: `{
"name": "John Doe",
"age": 30,
"city": "New York",
"hobbies": ["reading", "swimming", "coding"],
"address": {
"street": "123 Main St",
"zipCode": "10001"
}
}`,
  
  html: `<!DOCTYPE html>
<html>
<head>
<title>Sample Page</title>
</head>
<body>
<div class="container">
<h1>Hello World</h1>
<p>This is a sample HTML document.</p>
</div>
</body>
</html>`,
  
  css: `.container {
display: flex;
justify-content: center;
align-items: center;
height: 100vh;
background-color: #f0f0f0;
}

.button {
padding: 10px 20px;
border: none;
border-radius: 5px;
background-color: #007bff;
color: white;
cursor: pointer;
}`
}

// 方法
const getPlaceholder = () => {
  const placeholders: { [key: string]: string } = {
    javascript: '请输入 JavaScript 代码...',
    typescript: '请输入 TypeScript 代码...',
    json: '请输入 JSON 数据...',
    html: '请输入 HTML 代码...',
    css: '请输入 CSS 代码...',
    sql: '请输入 SQL 语句...',
    xml: '请输入 XML 代码...',
    python: '请输入 Python 代码...',
    java: '请输入 Java 代码...'
  }
  return placeholders[selectedLanguage.value] || '请输入代码...'
}

const handleLanguageChange = () => {
  syntaxError.value = ''
  result.value = null
}

const handleOperationChange = () => {
  result.value = null
}

const handleInputChange = () => {
  syntaxError.value = ''
  result.value = null
}

const handleProcess = () => {
  if (!inputCode.value.trim()) {
    ElMessage.warning('请输入代码')
    return
  }

  processing.value = true
  
  try {
    switch (operationType.value) {
      case 'format':
        result.value = {
          output: formatCode(inputCode.value, selectedLanguage.value)
        }
        break
        
      case 'minify':
        result.value = {
          output: minifyCode(inputCode.value, selectedLanguage.value)
        }
        break
        
      case 'validate':
        const validation = validateCode(inputCode.value, selectedLanguage.value)
        result.value = validation
        break
    }
    
    ElMessage.success('处理完成')
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '处理失败')
    result.value = null
  } finally {
    processing.value = false
  }
}

const formatCode = (code: string, language: string): string => {
  // 简单的代码格式化实现
  const indent = formatOptions.value.indentType === 'space' 
    ? ' '.repeat(formatOptions.value.indentSize)
    : '\t'
  
  switch (language) {
    case 'json':
      return JSON.stringify(JSON.parse(code), null, formatOptions.value.indentSize)
      
    case 'javascript':
    case 'typescript':
      return formatJavaScript(code, indent)
      
    case 'html':
    case 'xml':
      return formatXML(code, indent)
      
    case 'css':
      return formatCSS(code, indent)
      
    default:
      return formatGeneric(code, indent)
  }
}

const minifyCode = (code: string, language: string): string => {
  switch (language) {
    case 'json':
      return JSON.stringify(JSON.parse(code))
      
    case 'javascript':
    case 'typescript':
      return code.replace(/\s+/g, ' ').replace(/;\s*}/g, '}').trim()
      
    case 'css':
      return code.replace(/\s+/g, ' ').replace(/;\s*}/g, '}').replace(/{\s*/g, '{').trim()
      
    default:
      return code.replace(/\s+/g, ' ').trim()
  }
}

const validateCode = (code: string, language: string) => {
  try {
    switch (language) {
      case 'json':
        JSON.parse(code)
        break
        
      case 'javascript':
      case 'typescript':
        // 简单的语法检查
        new Function(code)
        break
    }
    
    const stats = {
      lines: code.split('\n').length,
      characters: code.length,
      size: new Blob([code]).size
    }
    
    return { valid: true, stats }
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : '语法错误'
    }
  }
}

const formatJavaScript = (code: string, indent: string): string => {
  // 简单的 JavaScript 格式化
  return code
    .replace(/\{/g, ' {\n')
    .replace(/\}/g, '\n}')
    .replace(/;/g, ';\n')
    .split('\n')
    .map(line => line.trim())
    .filter(line => line)
    .map((line, index, arr) => {
      const level = calculateIndentLevel(line, arr, index)
      return indent.repeat(level) + line
    })
    .join('\n')
}

const formatXML = (code: string, indent: string): string => {
  // 简单的 XML/HTML 格式化
  return code
    .replace(/></g, '>\n<')
    .split('\n')
    .map(line => line.trim())
    .filter(line => line)
    .map((line, index, arr) => {
      const level = calculateXMLIndentLevel(line, arr, index)
      return indent.repeat(level) + line
    })
    .join('\n')
}

const formatCSS = (code: string, indent: string): string => {
  // 简单的 CSS 格式化
  return code
    .replace(/\{/g, ' {\n')
    .replace(/\}/g, '\n}\n')
    .replace(/;/g, ';\n')
    .split('\n')
    .map(line => line.trim())
    .filter(line => line)
    .map((line, index, arr) => {
      const level = line.includes('{') || line.includes('}') ? 0 : 1
      return indent.repeat(level) + line
    })
    .join('\n')
}

const formatGeneric = (code: string, indent: string): string => {
  // 通用格式化
  return code
    .split('\n')
    .map(line => line.trim())
    .filter(line => line)
    .join('\n')
}

const calculateIndentLevel = (line: string, arr: string[], index: number): number => {
  // 简单的缩进级别计算
  let level = 0
  for (let i = 0; i < index; i++) {
    if (arr[i].includes('{')) level++
    if (arr[i].includes('}')) level--
  }
  if (line.includes('}')) level--
  return Math.max(0, level)
}

const calculateXMLIndentLevel = (line: string, arr: string[], index: number): number => {
  // 简单的 XML 缩进级别计算
  let level = 0
  for (let i = 0; i < index; i++) {
    if (arr[i].match(/<[^\/][^>]*[^\/]>/)) level++
    if (arr[i].match(/<\/[^>]*>/)) level--
  }
  if (line.match(/<\/[^>]*>/)) level--
  return Math.max(0, level)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getActionButtonText = () => {
  const texts: { [key: string]: string } = {
    format: '格式化',
    minify: '压缩',
    validate: '验证'
  }
  return texts[operationType.value] || '处理'
}

const clearInput = () => {
  inputCode.value = ''
  result.value = null
  syntaxError.value = ''
}

const pasteInput = async () => {
  try {
    const text = await navigator.clipboard.readText()
    inputCode.value = text
    handleInputChange()
    ElMessage.success('粘贴成功')
  } catch (error) {
    ElMessage.error('粘贴失败，请手动输入')
  }
}

const loadSample = () => {
  const sample = sampleCodes[selectedLanguage.value] || sampleCodes.javascript
  inputCode.value = sample
  handleInputChange()
  ElMessage.success('已加载示例代码')
}

const copyResult = async () => {
  if (!result.value) return
  
  const text = operationType.value === 'validate' 
    ? `代码验证结果：${result.value.valid ? '语法正确' : '语法错误'}`
    : result.value.output
    
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadResult = () => {
  if (!result.value || !result.value.output) return
  
  const extensions: { [key: string]: string } = {
    javascript: 'js',
    typescript: 'ts',
    json: 'json',
    html: 'html',
    css: 'css',
    sql: 'sql',
    xml: 'xml',
    python: 'py',
    java: 'java'
  }
  
  const ext = extensions[selectedLanguage.value] || 'txt'
  const filename = `formatted_code_${Date.now()}.${ext}`
  
  const blob = new Blob([result.value.output], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('下载成功')
}
</script>

<style lang="scss" scoped>
.code-formatter {
  .tool-header {
    margin-bottom: 32px;

    .tool-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tool-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 12px;
      }

      h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }

    .tool-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }

  .tool-content {
    .config-section,
    .input-section,
    .action-section,
    .result-section {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }

        .section-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .config-section {
      .config-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;

        .config-item {
          .config-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 8px;
          }
        }
      }

      .format-options {
        border-top: 1px solid var(--el-border-color-lighter);
        padding-top: 16px;

        .option-row {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 16px;
          align-items: end;

          .option-item {
            .option-label {
              display: block;
              font-size: 13px;
              color: var(--el-text-color-regular);
              margin-bottom: 6px;
            }
          }
        }
      }
    }

    .code-textarea,
    .result-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }

    .error-message {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      padding: 8px 12px;
      background: var(--el-color-error-light-9);
      color: var(--el-color-error);
      border-radius: 4px;
      font-size: 13px;

      i {
        font-size: 14px;
      }
    }

    .action-section {
      text-align: center;

      .process-button {
        min-width: 120px;
        height: 40px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .validation-result {
      .validation-status {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;

        &.success {
          background: var(--el-color-success-light-9);
          color: var(--el-color-success);
        }

        &.error {
          background: var(--el-color-error-light-9);
          color: var(--el-color-error);
        }

        i {
          font-size: 18px;
        }
      }

      .validation-error {
        padding: 12px 16px;
        background: var(--el-color-error-light-9);
        color: var(--el-color-error);
        border-radius: 6px;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 13px;
        margin-bottom: 16px;
      }

      .code-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;

        .stat-item {
          padding: 12px;
          background: var(--el-fill-color-light);
          border-radius: 6px;
          text-align: center;

          label {
            display: block;
            font-size: 12px;
            color: var(--el-text-color-regular);
            margin-bottom: 4px;
          }

          span {
            font-size: 18px;
            font-weight: 600;
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
}

// 响应式设计
@media only screen and (max-width: 768px) {
  .code-formatter {
    .tool-content {
      .config-section {
        .config-row {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        .format-options {
          .option-row {
            grid-template-columns: 1fr;
          }
        }
      }

      .validation-result {
        .code-stats {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    }
  }
}
</style>
