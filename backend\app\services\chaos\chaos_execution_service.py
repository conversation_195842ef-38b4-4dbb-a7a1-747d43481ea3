"""
混沌测试执行记录业务服务
"""
from typing import Dict, Any, List, Optional, Type
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.chaos.chaos_execution_repository import ChaosExecutionRepository
from app.repositories.chaos.chaos_task_repository import ChaosTaskRepository
from app.models.chaos.chaos_execution import ChaosExecution
from app.schemas.chaos.chaos_execution import (
    ChaosExecutionCreate, ChaosExecutionUpdate, ChaosExecutionResponse, ChaosExecutionListResponse,
    ChaosExecutionSearchParams, ChaosExecutionDetailResponse,
    ChaosExecutionLogRequest, ChaosExecutionLogResponse, ChaosExecutionRetryRequest,
    ChaosExecutionBatchRequest
)
from app.schemas.base import PaginationData
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.logger import setup_logger
from .chaosblade_service import ChaosBladeService

logger = setup_logger()


class ChaosExecutionService(BaseService[ChaosExecution, ChaosExecutionCreate, ChaosExecutionUpdate, ChaosExecutionResponse]):
    """
    混沌测试执行记录业务服务
    继承BaseService，提供执行记录管理的核心业务逻辑
    """

    def __init__(self, db: AsyncSession):
        self.repository = ChaosExecutionRepository(db)
        self.task_repository = ChaosTaskRepository(db)
        self.chaosblade_service = ChaosBladeService()
        super().__init__(db, self.repository)

    @property
    def model_class(self) -> Type[ChaosExecution]:
        return ChaosExecution

    @property
    def response_schema_class(self) -> Type[ChaosExecutionResponse]:
        return ChaosExecutionResponse

    # ==================== 钩子方法实现 ====================

    async def _validate_before_create(self, create_data: ChaosExecutionCreate, **kwargs) -> None:
        """创建前业务验证"""
        # 验证任务是否存在
        task = await self.task_repository.get(create_data.task_id)
        if not task:
            raise_validation_error(f"任务ID {create_data.task_id} 不存在")

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前数据处理"""
        # 设置默认状态
        create_dict["status"] = "pending"
        create_dict["retry_count"] = 0
        create_dict["is_auto_destroyed"] = False
        
        return create_dict

    async def _process_after_create(self, obj: ChaosExecution, create_data) -> None:
        """创建后处理"""
        logger.info(f"执行记录创建成功: Task {obj.task_id}, Host {obj.host_id} (ID: {obj.id})")

    def _convert_to_response(self, obj: ChaosExecution) -> ChaosExecutionResponse:
        """转换为响应对象"""
        # 获取关联信息
        task_name = None
        host_name = None
        try:
            if hasattr(obj, 'task') and obj.task:
                task_name = obj.task.name
            if obj.host_info and isinstance(obj.host_info, dict):
                host_name = obj.host_info.get('name') or obj.host_info.get('host')
        except Exception:
            pass

        return ChaosExecutionResponse(
            id=obj.id,
            task_id=obj.task_id,
            host_id=obj.host_id,
            host_info=obj.host_info,
            start_time=obj.start_time,
            end_time=obj.end_time,
            status=obj.status,
            chaos_uid=obj.chaos_uid,
            command=obj.command,
            blade_version=obj.blade_version,
            output=obj.output,
            error_message=obj.error_message,
            exit_code=obj.exit_code,
            fault_config=obj.fault_config,
            duration_seconds=obj.duration_seconds,
            retry_count=obj.retry_count,
            is_auto_destroyed=obj.is_auto_destroyed,
            destroy_time=obj.destroy_time,
            destroy_output=obj.destroy_output,
            created_at=obj.created_at,
            updated_at=obj.updated_at,
            created_by=obj.created_by,
            updated_by=obj.updated_by,
            task_name=task_name,
            host_name=host_name,
            is_running=obj.is_running,
            is_completed=obj.is_completed,
            is_successful=obj.is_successful,
            has_chaos_uid=obj.has_chaos_uid
        )

    # ==================== 业务方法 ====================

    async def create_execution(self, execution_data: ChaosExecutionCreate, current_user_id: int) -> ChaosExecutionResponse:
        """创建执行记录"""
        return await self.create(execution_data, str(current_user_id))

    async def get_execution_by_id(self, execution_id: int) -> ChaosExecutionDetailResponse:
        """获取执行记录详情"""
        execution = await self.repository.get_with_task(execution_id)
        if not execution:
            raise_not_found(f"执行记录ID {execution_id} 不存在")
        
        response = self._convert_to_response(execution)
        return ChaosExecutionDetailResponse(**response.model_dump())

    async def search_executions(self, params: ChaosExecutionSearchParams) -> PaginationData[ChaosExecutionListResponse]:
        """搜索执行记录"""
        skip = (params.page - 1) * params.size

        executions, total = await self.repository.search_executions(
            skip=skip,
            limit=params.size,
            task_name=params.task_name,
            status=params.status,
            order_by=params.order_by,
            desc=params.desc
        )

        # 转换为列表响应格式
        execution_list = []
        for execution in executions:
            task_name = None
            host_name = None
            try:
                if hasattr(execution, 'task') and execution.task:
                    task_name = execution.task.name
                if execution.host_info and isinstance(execution.host_info, dict):
                    host_name = execution.host_info.get('name') or execution.host_info.get('host')
            except Exception:
                pass

            execution_list.append(ChaosExecutionListResponse(
                id=execution.id,
                task_id=execution.task_id,
                task_name=task_name,
                host_id=execution.host_id,
                host_name=host_name,
                status=execution.status,
                chaos_uid=execution.chaos_uid,
                start_time=execution.start_time,
                end_time=execution.end_time,
                duration_seconds=execution.duration_seconds,
                retry_count=execution.retry_count,
                is_auto_destroyed=execution.is_auto_destroyed,
                exit_code=execution.exit_code,
                has_error=bool(execution.error_message),
                created_at=execution.created_at
            ))

        return PaginationData(
            records=execution_list,
            total=total,
            current=params.page,
            size=params.size
        )

    async def get_task_executions(self, task_id: int, page: int = 1, size: int = 20) -> PaginationData[ChaosExecutionListResponse]:
        """获取任务的执行记录"""
        skip = (page - 1) * size
        executions, total = await self.repository.get_task_executions(
            task_id, skip=skip, limit=size
        )

        execution_list = []
        for execution in executions:
            execution_list.append(ChaosExecutionListResponse(
                id=execution.id,
                task_id=execution.task_id,
                task_name=getattr(execution.task, 'name', None) if hasattr(execution, 'task') else None,
                host_id=execution.host_id,
                host_name=execution.host_info.get('name') if execution.host_info else None,
                status=execution.status,
                chaos_uid=execution.chaos_uid,
                start_time=execution.start_time,
                end_time=execution.end_time,
                duration_seconds=execution.duration_seconds,
                retry_count=execution.retry_count,
                is_auto_destroyed=execution.is_auto_destroyed,
                exit_code=execution.exit_code,
                has_error=bool(execution.error_message),
                created_at=execution.created_at
            ))

        return PaginationData(
            records=execution_list,
            total=total,
            current=page,
            size=size
        )

    async def get_execution_log(self, request: ChaosExecutionLogRequest) -> ChaosExecutionLogResponse:
        """获取执行日志"""
        execution = await self.repository.get(request.execution_id)
        if not execution:
            raise_not_found(f"执行记录ID {request.execution_id} 不存在")

        # 根据日志类型返回相应内容
        content = ""
        if request.log_type == "output":
            content = execution.output or ""
        elif request.log_type == "error":
            content = execution.error_message or ""
        elif request.log_type == "command":
            content = execution.command or ""
        elif request.log_type == "destroy":
            content = execution.destroy_output or ""

        return ChaosExecutionLogResponse(
            execution_id=request.execution_id,
            log_type=request.log_type,
            content=content,
            timestamp=datetime.now()
        )

    async def retry_execution(self, request: ChaosExecutionRetryRequest, current_user_id: int) -> ChaosExecutionResponse:
        """重试执行"""
        execution = await self.repository.get_with_task(request.execution_id)
        if not execution:
            raise_not_found(f"执行记录ID {request.execution_id} 不存在")

        if execution.is_running:
            raise_validation_error("正在运行的执行记录无法重试")

        try:
            # 增加重试次数
            execution.increment_retry()
            
            # 重置状态
            execution.status = "pending"
            execution.start_time = None
            execution.end_time = None
            execution.output = None
            execution.error_message = None
            execution.exit_code = None
            execution.chaos_uid = None
            execution.command = None
            
            await self.repository.db.commit()

            # 重新执行故障注入
            # 这里应该调用任务服务的执行方法
            logger.info(f"重试执行记录 {request.execution_id}，重试次数: {execution.retry_count}")

            return self._convert_to_response(execution)

        except Exception as e:
            logger.error(f"重试执行失败: {str(e)}")
            raise_validation_error(f"重试执行失败: {str(e)}")

    async def cancel_execution(self, execution_id: int, current_user_id: int) -> bool:
        """取消执行"""
        execution = await self.repository.get(execution_id)
        if not execution:
            raise_not_found(f"执行记录ID {execution_id} 不存在")

        if not execution.is_running:
            raise_validation_error("只能取消正在运行的执行记录")

        try:
            # 如果有chaos_uid，先销毁故障注入
            if execution.chaos_uid:
                # 这里需要获取主机信息并调用销毁命令
                # host_info = await self._get_host_info(execution)
                # await self.chaosblade_service.destroy_fault(host_info, execution.chaos_uid)
                pass

            # 更新状态为已取消
            execution.cancel_execution("用户取消")
            await self.repository.db.commit()

            logger.info(f"执行记录 {execution_id} 已取消，操作者: {current_user_id}")
            return True

        except Exception as e:
            logger.error(f"取消执行失败: {str(e)}")
            raise_validation_error(f"取消执行失败: {str(e)}")

    async def batch_operation(self, request: ChaosExecutionBatchRequest, current_user_id: int) -> Dict[str, Any]:
        """批量操作执行记录"""
        results = {
            "success_count": 0,
            "failed_count": 0,
            "errors": []
        }

        for execution_id in request.execution_ids:
            try:
                if request.action == "cancel":
                    await self.cancel_execution(execution_id, current_user_id)
                elif request.action == "destroy":
                    await self._destroy_execution(execution_id)
                elif request.action == "retry":
                    retry_request = ChaosExecutionRetryRequest(execution_id=execution_id)
                    await self.retry_execution(retry_request, current_user_id)
                elif request.action == "delete":
                    await self.delete(execution_id)
                
                results["success_count"] += 1

            except Exception as e:
                results["failed_count"] += 1
                results["errors"].append(f"执行记录 {execution_id}: {str(e)}")

        return results





    # ==================== 私有方法 ====================

    async def _destroy_execution(self, execution_id: int) -> None:
        """销毁执行记录的故障注入"""
        execution = await self.repository.get(execution_id)
        if not execution:
            return

        if execution.chaos_uid and not execution.is_auto_destroyed:
            try:
                # 获取主机信息
                host_info = await self._get_host_info_by_execution(execution)
                if not host_info:
                    logger.warning(f"执行记录 {execution_id} 缺少主机信息，无法销毁故障注入")
                    return

                # 调用ChaosBlade销毁命令
                result = await self.chaosblade_service.destroy_fault(host_info, execution.chaos_uid)

                if result.get("success", False):
                    # 销毁成功，更新记录
                    await self.repository.mark_as_destroyed(execution_id, result.get("output", "Destroyed successfully"))
                    logger.info(f"执行记录 {execution_id} 的故障注入已销毁")
                else:
                    # 销毁失败，记录错误
                    error_msg = result.get("error", "销毁失败")
                    logger.error(f"销毁执行记录 {execution_id} 的故障注入失败: {error_msg}")
                    # 即使销毁失败，也标记为已尝试销毁
                    await self.repository.mark_as_destroyed(execution_id, f"Destroy failed: {error_msg}")

            except Exception as e:
                logger.error(f"销毁执行记录 {execution_id} 的故障注入失败: {str(e)}")
                raise

    async def _get_host_info_by_execution(self, execution: 'ChaosExecution') -> Dict[str, Any]:
        """根据执行记录获取主机信息"""
        # 如果执行记录中保存了主机信息，直接使用
        if execution.host_info:
            return execution.host_info

        # 如果没有保存主机信息，从环境管理获取
        try:
            from app.services.env.env import EnvironmentService
            env_service = EnvironmentService(self.db)
            environment = await env_service.get_environment(execution.host_id)  # 使用host_id作为env_id

            if environment:
                return {
                    "host": environment.host,
                    "port": environment.port or 22,
                    "username": environment.config.get("username", "root") if environment.config else "root",
                    "password": environment.config.get("password") if environment.config else None,
                    "private_key_path": environment.config.get("private_key_path") if environment.config else None
                }
        except Exception as e:
            logger.error(f"获取执行记录 {execution.id} 的主机信息失败: {str(e)}")

        # 默认返回空字典
        return {}
