"""
Kubernetes连接客户端
支持Kubernetes集群的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional, List

from .base_client import BaseClient, ConnectionResult

try:
    from kubernetes_asyncio import client, config
    from kubernetes_asyncio.client.rest import ApiException
    K8S_AVAILABLE = True
except ImportError:
    K8S_AVAILABLE = False


class K8sClient(BaseClient):
    """
    Kubernetes连接客户端
    支持Kubernetes集群的连接和测试
    """

    def __init__(self, config_dict: Dict[str, Any]):
        """
        初始化Kubernetes客户端
        
        Args:
            config_dict: Kubernetes连接配置
                - kubeconfig_path: kubeconfig文件路径 (可选)
                - context: 上下文名称 (可选)
                - host: API服务器地址 (可选)
                - token: 访问令牌 (可选)
                - cert_file: 客户端证书文件路径 (可选)
                - key_file: 客户端密钥文件路径 (可选)
                - ca_cert_file: CA证书文件路径 (可选)
                - verify_ssl: 是否验证SSL (默认: True)
        """
        super().__init__(config_dict)
        
        if not K8S_AVAILABLE:
            raise ImportError("kubernetes_asyncio库未安装，请运行: pip install kubernetes_asyncio")
        
        # 设置默认值
        self.kubeconfig_path = config_dict.get('kubeconfig_path', '')
        self.context = config_dict.get('context', '')
        self.host = config_dict.get('host', '')
        self.token = config_dict.get('token', '')
        self.cert_file = config_dict.get('cert_file', '')
        self.key_file = config_dict.get('key_file', '')
        self.ca_cert_file = config_dict.get('ca_cert_file', '')
        self.verify_ssl = config_dict.get('verify_ssl', True)
        
        self.api_client = None
        self.core_v1_api = None
        self.apps_v1_api = None

    async def _load_config(self) -> None:
        """
        加载Kubernetes配置
        """
        try:
            if self.kubeconfig_path:
                # 从kubeconfig文件加载配置
                await config.load_kube_config(
                    config_file=self.kubeconfig_path,
                    context=self.context if self.context else None
                )
            elif self.host and self.token:
                # 使用令牌认证
                configuration = client.Configuration()
                configuration.host = self.host
                configuration.api_key = {"authorization": f"Bearer {self.token}"}
                configuration.verify_ssl = self.verify_ssl
                
                if self.ca_cert_file:
                    configuration.ssl_ca_cert = self.ca_cert_file
                
                client.Configuration.set_default(configuration)
            elif self.host and self.cert_file and self.key_file:
                # 使用证书认证
                configuration = client.Configuration()
                configuration.host = self.host
                configuration.cert_file = self.cert_file
                configuration.key_file = self.key_file
                configuration.verify_ssl = self.verify_ssl
                
                if self.ca_cert_file:
                    configuration.ssl_ca_cert = self.ca_cert_file
                
                client.Configuration.set_default(configuration)
            else:
                # 尝试从集群内部加载配置
                config.load_incluster_config()
        except Exception as e:
            raise RuntimeError(f"加载Kubernetes配置失败: {str(e)}")

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立Kubernetes连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 加载配置
            await self._load_config()
            
            # 创建API客户端
            self.api_client = client.ApiClient()
            self.core_v1_api = client.CoreV1Api(self.api_client)
            self.apps_v1_api = client.AppsV1Api(self.api_client)
            
            # 测试连接 - 获取版本信息
            version_api = client.VersionApi(self.api_client)
            version_info = await version_api.get_code()
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到Kubernetes集群",
                duration=duration,
                details={
                    "kubernetes_version": version_info.git_version,
                    "platform": version_info.platform,
                    "go_version": version_info.go_version,
                    "build_date": version_info.build_date
                }
            )
            
        except ApiException as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kubernetes API异常: {e.reason}",
                duration=duration,
                details={"error_type": "ApiException", "status": e.status}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kubernetes连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开Kubernetes连接"""
        try:
            if self.api_client:
                await self.api_client.close()
                self.api_client = None
                self.core_v1_api = None
                self.apps_v1_api = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试Kubernetes连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        start_time = time.time()
        
        try:
            # 如果没有建立连接，先建立连接
            if not self.core_v1_api:
                connect_result = await self.connect(timeout)
                if not connect_result.success:
                    return connect_result
            
            # 获取节点列表来测试连接
            nodes = await self.core_v1_api.list_node()
            
            # 获取命名空间列表
            namespaces = await self.core_v1_api.list_namespace()
            
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"Kubernetes连接测试成功",
                duration=duration,
                details={
                    "nodes_count": len(nodes.items),
                    "namespaces_count": len(namespaces.items),
                    "node_names": [node.metadata.name for node in nodes.items[:5]],  # 只显示前5个节点
                    "namespace_names": [ns.metadata.name for ns in namespaces.items[:10]]  # 只显示前10个命名空间
                }
            )
            
        except ApiException as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kubernetes连接测试失败: {e.reason}",
                duration=duration,
                details={"error_type": "ApiException", "status": e.status}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kubernetes连接测试异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def get_nodes(self) -> List[Dict[str, Any]]:
        """
        获取节点列表
        
        Returns:
            List[Dict]: 节点信息列表
        """
        if not self.core_v1_api:
            raise RuntimeError("Kubernetes未连接")
        
        try:
            nodes = await self.core_v1_api.list_node()
            return [
                {
                    "name": node.metadata.name,
                    "status": "Ready" if any(
                        condition.type == "Ready" and condition.status == "True"
                        for condition in node.status.conditions
                    ) else "NotReady",
                    "roles": list(node.metadata.labels.keys()) if node.metadata.labels else [],
                    "version": node.status.node_info.kubelet_version,
                    "os": node.status.node_info.operating_system,
                    "architecture": node.status.node_info.architecture
                }
                for node in nodes.items
            ]
        except Exception as e:
            raise RuntimeError(f"获取节点列表失败: {str(e)}")

    async def get_namespaces(self) -> List[str]:
        """
        获取命名空间列表
        
        Returns:
            List[str]: 命名空间名称列表
        """
        if not self.core_v1_api:
            raise RuntimeError("Kubernetes未连接")
        
        try:
            namespaces = await self.core_v1_api.list_namespace()
            return [ns.metadata.name for ns in namespaces.items]
        except Exception as e:
            raise RuntimeError(f"获取命名空间列表失败: {str(e)}")

    async def get_pods(self, namespace: str = "default") -> List[Dict[str, Any]]:
        """
        获取Pod列表
        
        Args:
            namespace: 命名空间名称
            
        Returns:
            List[Dict]: Pod信息列表
        """
        if not self.core_v1_api:
            raise RuntimeError("Kubernetes未连接")
        
        try:
            pods = await self.core_v1_api.list_namespaced_pod(namespace=namespace)
            return [
                {
                    "name": pod.metadata.name,
                    "namespace": pod.metadata.namespace,
                    "status": pod.status.phase,
                    "ready": f"{sum(1 for container in pod.status.container_statuses if container.ready)}/{len(pod.status.container_statuses)}" if pod.status.container_statuses else "0/0",
                    "restarts": sum(container.restart_count for container in pod.status.container_statuses) if pod.status.container_statuses else 0,
                    "node": pod.spec.node_name,
                    "created": pod.metadata.creation_timestamp.isoformat() if pod.metadata.creation_timestamp else None
                }
                for pod in pods.items
            ]
        except Exception as e:
            raise RuntimeError(f"获取Pod列表失败: {str(e)}")

    async def get_services(self, namespace: str = "default") -> List[Dict[str, Any]]:
        """
        获取Service列表
        
        Args:
            namespace: 命名空间名称
            
        Returns:
            List[Dict]: Service信息列表
        """
        if not self.core_v1_api:
            raise RuntimeError("Kubernetes未连接")
        
        try:
            services = await self.core_v1_api.list_namespaced_service(namespace=namespace)
            return [
                {
                    "name": svc.metadata.name,
                    "namespace": svc.metadata.namespace,
                    "type": svc.spec.type,
                    "cluster_ip": svc.spec.cluster_ip,
                    "external_ip": svc.status.load_balancer.ingress[0].ip if svc.status.load_balancer and svc.status.load_balancer.ingress else None,
                    "ports": [f"{port.port}:{port.target_port}/{port.protocol}" for port in svc.spec.ports] if svc.spec.ports else []
                }
                for svc in services.items
            ]
        except Exception as e:
            raise RuntimeError(f"获取Service列表失败: {str(e)}")
