<template>
  <div class="monitor-chart">
    <div class="chart-header">
      <div class="chart-title">
        <span class="title-text">{{ title }}</span>
        <el-tag 
          :type="getValueTagType(currentValue)" 
          size="small"
          class="current-value"
        >
          {{ formatValue(currentValue) }}
        </el-tag>
      </div>
      <div class="chart-stats">
        <span class="stat-item">平均: {{ formatValue(avgValue) }}</span>
        <span class="stat-item">最大: {{ formatValue(maxValue) }}</span>
      </div>
    </div>
    
    <div class="chart-container">
      <ArtLineChart
        :data="chartData"
        :x-axis-data="timestamps"
        :height="height"
        :colors="[color]"
        :loading="loading"
        :is-empty="isEmpty"
        :smooth="true"
        :show-area-color="true"
        :line-width="2"
        :symbol="'none'"
        :show-tooltip="true"
        :show-legend="false"
        :show-axis-label="true"
        :show-split-line="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ArtLineChart from '@/components/core/charts/art-line-chart/index.vue'
import { MonitorService } from '@/api/monitorApi'

interface Props {
  title: string
  data: number[]
  timestamps: string[]
  unit: string
  color?: string
  height?: string
  loading?: boolean
  isEmpty?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  color: '#409EFF',
  height: '200px',
  loading: false,
  isEmpty: false
})

// 计算当前值
const currentValue = computed(() => {
  return props.data.length > 0 ? props.data[props.data.length - 1] : 0
})

// 计算平均值
const avgValue = computed(() => {
  if (props.data.length === 0) return 0
  const sum = props.data.reduce((acc, val) => acc + val, 0)
  return sum / props.data.length
})

// 计算最大值
const maxValue = computed(() => {
  return props.data.length > 0 ? Math.max(...props.data) : 0
})

// 格式化数值显示
const formatValue = (value: number): string => {
  return MonitorService.formatMetricValue(value, props.unit)
}

// 获取数值标签类型
const getValueTagType = (value: number): string => {
  if (props.unit === '%') {
    if (value >= 90) return 'danger'
    if (value >= 70) return 'warning'
    return 'success'
  }
  return 'primary'
}

// 转换数据格式为图表组件需要的格式
const chartData = computed(() => {
  return props.data
})
</script>

<style scoped lang="scss">
.monitor-chart {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  height: 100%;
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .title-text {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
      
      .current-value {
        font-weight: 600;
      }
    }
    
    .chart-stats {
      display: flex;
      gap: 12px;
      
      .stat-item {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }
  
  .chart-container {
    height: calc(100% - 50px);
    min-height: 150px;
  }
}

@media (max-width: 768px) {
  .monitor-chart {
    .chart-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      
      .chart-stats {
        gap: 8px;
      }
    }
  }
}
</style>
