<template>
  <ElDialog
    v-model="dialogVisible"
    title="模型统计信息"
    width="800px"
    align-center
    @opened="handleDialogOpened"
  >
    <div v-loading="isLoading" element-loading-text="加载中...">
      <!-- 总体统计 -->
      <ElRow :gutter="20" class="stats-overview">
        <ElCol :span="6">
          <ElCard shadow="never" class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ modelStats?.total_models || 0 }}</div>
              <div class="stats-label">总模型数</div>
            </div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard shadow="never" class="stats-card">
            <div class="stats-item">
              <div class="stats-value enabled">{{ modelStats?.total_enabled || 0 }}</div>
              <div class="stats-label">启用模型</div>
            </div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard shadow="never" class="stats-card">
            <div class="stats-item">
              <div class="stats-value healthy">{{ modelStats?.total_healthy || 0 }}</div>
              <div class="stats-label">健康模型</div>
            </div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard shadow="never" class="stats-card">
            <div class="stats-item">
              <div class="stats-value ratio">
                {{ healthyRatio }}%
              </div>
              <div class="stats-label">健康率</div>
            </div>
          </ElCard>
        </ElCol>
      </ElRow>

      <!-- 平台统计 -->
      <ElDivider content-position="left">平台分布</ElDivider>
      
      <ElTable :data="platformStatsData" style="width: 100%">
        <ElTableColumn prop="platform" label="平台类型" width="120">
          <template #default="{ row }">
            <ElTag :type="getPlatformTagType(row.platform)"> 
              {{ getPlatformDisplayName(row.platform) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="total" label="总数" width="80" align="center" />
        <ElTableColumn prop="enabled" label="启用" width="80" align="center">
          <template #default="{ row }">
            <span class="enabled-text">{{ row.enabled }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="healthy" label="健康" width="80" align="center">
          <template #default="{ row }">
            <span class="healthy-text">{{ row.healthy }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="健康率" width="100" align="center">
          <template #default="{ row }">
            <span>{{ calculateHealthyRatio(row.healthy, row.total) }}%</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="状态分布" min-width="200">
          <template #default="{ row }">
            <div class="status-bar">
              <div 
                class="status-segment healthy" 
                :style="{ width: `${(row.healthy / row.total) * 100}%` }"
              ></div>
              <div 
                class="status-segment enabled" 
                :style="{ width: `${((row.enabled - row.healthy) / row.total) * 100}%` }"
              ></div>
              <div 
                class="status-segment disabled" 
                :style="{ width: `${((row.total - row.enabled) / row.total) * 100}%` }"
              ></div>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 图例 -->
      <div class="legend">
        <div class="legend-item">
          <div class="legend-color healthy"></div>
          <span>健康</span>
        </div>
        <div class="legend-item">
          <div class="legend-color enabled"></div>
          <span>启用但未知健康状态</span>
        </div>
        <div class="legend-item">
          <div class="legend-color disabled"></div>
          <span>停用</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="refreshStats" :loading="isLoading">刷新</ElButton>
        <ElButton type="primary" @click="dialogVisible = false">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useModelStore } from '@/store/business/model/index'
import { MODEL_PLATFORM_CONFIG } from '@/types/api/model'

// 组件属性
interface Props {
  visible: boolean
}

// 组件事件
interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const modelStore = useModelStore()

// 加载状态
const isLoading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 模型统计数据
const modelStats = computed(() => modelStore.modelStats)

// 平台统计数据
const platformStatsData = computed(() => {
  return [...(modelStats.value?.platform_stats || [])]
})

// 健康率计算
const healthyRatio = computed(() => {
  if (!modelStats.value || modelStats.value.total_models === 0) return 0
  return Math.round((modelStats.value.total_healthy / modelStats.value.total_models) * 100)
})

// 获取平台标签类型
const getPlatformTagType = (platform: string) => {
  const config = MODEL_PLATFORM_CONFIG[platform as keyof typeof MODEL_PLATFORM_CONFIG]
  return config?.type || 'primary'
}

// 获取平台显示名称
const getPlatformDisplayName = (platform: string) => {
  return MODEL_PLATFORM_CONFIG[platform as keyof typeof MODEL_PLATFORM_CONFIG]?.text || platform
}

// 计算健康率
const calculateHealthyRatio = (healthy: number, total: number) => {
  if (total === 0) return 0
  return Math.round((healthy / total) * 100)
}

// 刷新统计数据
const refreshStats = async () => {
  try {
    isLoading.value = true
    await modelStore.fetchModelStats()
  } catch (error) {
    console.error('刷新统计数据失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 对话框打开处理
const handleDialogOpened = () => {
  refreshStats()
}
</script>

<style scoped>
.stats-overview {
  margin-bottom: 24px;
}

.stats-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.stats-item {
  text-align: center;
  padding: 16px;
}

.stats-value {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stats-value.enabled {
  color: #67c23a;
}

.stats-value.healthy {
  color: #409eff;
}

.stats-value.ratio {
  color: #e6a23c;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.enabled-text {
  color: #67c23a;
  font-weight: bold;
}

.healthy-text {
  color: #409eff;
  font-weight: bold;
}

.status-bar {
  display: flex;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  background-color: #f5f7fa;
}

.status-segment {
  height: 100%;
}

.status-segment.healthy {
  background-color: #67c23a;
}

.status-segment.enabled {
  background-color: #e6a23c;
}

.status-segment.disabled {
  background-color: #f56c6c;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
  padding: 16px;

  border-radius: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.legend-color.healthy {
  background-color: #67c23a;
}

.legend-color.enabled {
  background-color: #e6a23c;
}

.legend-color.disabled {
  background-color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
