/**
 * 统一错误处理工具
 * 用于避免重复显示错误消息
 */
import { ElMessage } from 'element-plus'
import { HttpError } from './http/error'

/**
 * 错误处理选项
 */
interface ErrorHandlerOptions {
  /** 是否显示错误消息 */
  showMessage?: boolean
  /** 自定义错误消息 */
  customMessage?: string
  /** 是否记录错误日志 */
  logError?: boolean
  /** 错误回调函数 */
  onError?: (error: any) => void
}

/**
 * 统一错误处理函数
 * @param error 错误对象
 * @param options 处理选项
 */
export function handleError(error: any, options: ErrorHandlerOptions = {}) {
  const {
    showMessage = true,
    customMessage,
    logError = true,
    onError
  } = options

  let errorMessage = customMessage || '操作失败'

  // 处理不同类型的错误
  if (error instanceof HttpError) {
    errorMessage = customMessage || error.message
  } else if (error?.response?.data?.message) {
    errorMessage = customMessage || error.response.data.message
  } else if (error?.message) {
    errorMessage = customMessage || error.message
  }

  // 显示错误消息
  if (showMessage) {
    ElMessage.error(errorMessage)
  }

  // 记录错误日志
  if (logError) {
    console.error('[Error Handler]', {
      error,
      message: errorMessage,
      timestamp: new Date().toISOString()
    })
  }

  // 执行错误回调
  if (onError) {
    onError(error)
  }

  return {
    error,
    message: errorMessage
  }
}

/**
 * 静默错误处理（不显示消息）
 * @param error 错误对象
 * @param onError 错误回调
 */
export function handleSilentError(error: any, onError?: (error: any) => void) {
  return handleError(error, {
    showMessage: false,
    onError
  })
}

/**
 * 业务操作错误处理
 * @param error 错误对象
 * @param operation 操作名称
 * @param onError 错误回调
 */
export function handleBusinessError(
  error: any, 
  operation: string, 
  onError?: (error: any) => void
) {
  return handleError(error, {
    customMessage: `${operation}失败`,
    onError
  })
}

/**
 * API调用错误处理装饰器
 * @param options 错误处理选项
 */
export function withErrorHandler(options: ErrorHandlerOptions = {}) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const originalMethod = descriptor.value!

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args)
      } catch (error) {
        handleError(error, options)
        throw error
      }
    } as T

    return descriptor
  }
}

/**
 * 检查是否为取消错误
 * @param error 错误对象
 */
export function isCancelError(error: any): boolean {
  return error?.message === 'cancel' || error === 'cancel'
}

/**
 * 安全的异步操作执行器
 * @param asyncFn 异步函数
 * @param options 错误处理选项
 */
export async function safeExecute<T>(
  asyncFn: () => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<{ data?: T; error?: any; success: boolean }> {
  try {
    const data = await asyncFn()
    return { data, success: true }
  } catch (error) {
    // 如果是取消错误，不处理
    if (isCancelError(error)) {
      return { error, success: false }
    }

    const result = handleError(error, options)
    return { error: result.error, success: false }
  }
}
