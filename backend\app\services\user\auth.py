"""
认证业务服务类
"""
from datetime import timed<PERSON><PERSON>, datetime
from typing import Any, Dict
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.security import create_access_token, create_refresh_token, verify_token
from app.core.dependencies import ServiceDependency
from app.core.exceptions import (
    raise_unauthorized_error, 
    raise_validation_error, 
    raise_conflict_error
)
from app.repositories.user.user import UserRepository
from app.schemas.user.auth import LoginRequest, RegisterRequest, RefreshTokenRequest
from app.schemas.user.user import UserCreate
from app.utils.logger import operation_logger


class AuthService(ServiceDependency):
    """
    认证业务服务类
    """
    
    def __init__(self, db: AsyncSession):
        """
        初始化认证服务
        
        Args:
            db: 数据库会话
        """
        super().__init__(db)
        self.user_repo = UserRepository(db)
    
    async def login(
        self,
        login_data: LoginRequest,
        request: Request
    ) -> Dict[str, Any]:
        """
        用户登录业务逻辑
        
        Args:
            login_data: 登录数据
            request: HTTP请求对象
            
        Returns:
            登录响应数据
        """
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 用户认证
        user = await self.user_repo.authenticate_user(
            username=login_data.username, 
            password=login_data.password
        )
        
        if not user:
            # 记录登录失败日志
            await operation_logger.log_login(
                username=login_data.username,
                ip_address=client_ip,
                user_agent=user_agent,
                success=False,
                error_msg="用户名或密码错误"
            )
            raise_unauthorized_error("用户名或密码错误")
        
        if not user.is_active:
            await operation_logger.log_login(
                username=login_data.username,
                ip_address=client_ip,
                user_agent=user_agent,
                success=False,
                error_msg="用户账号已被禁用"
            )
            raise_validation_error("用户账号已被禁用")
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "user_id": user.id}, 
            expires_delta=access_token_expires
        )
        
        # 创建刷新令牌
        refresh_token = create_refresh_token(
            data={"sub": user.username, "user_id": user.id}
        )
        
        # 更新最后登录时间
        await self.user_repo.update_last_login(user)
        
        # 记录登录成功日志
        await operation_logger.log_login(
            username=login_data.username,
            ip_address=client_ip,
            user_agent=user_agent,
            success=True
        )
        
        # 返回纯业务数据，不包装响应
        return {
            "token": access_token, 
            "refreshToken": refresh_token
        }
    
    async def register(
        self,
        register_data: RegisterRequest
    ) -> Dict[str, Any]:
        """
        用户注册业务逻辑
        
        Args:
            register_data: 注册数据
            
        Returns:
            注册响应数据
        """
        # 检查用户名是否已存在
        if await self.user_repo.is_username_taken(register_data.username):
            raise_conflict_error("用户名已存在")
        
        # 检查邮箱是否已存在
        if await self.user_repo.is_email_taken(register_data.email):
            raise_conflict_error("邮箱已被注册")
        
        # 创建用户
        user_create = UserCreate(
            username=register_data.username,
            password=register_data.password,
            email=register_data.email,
            nickname=register_data.nickname or register_data.username
        )
        
        user = await self.user_repo.create_user(user_create)
        
        # 记录注册日志
        await operation_logger.log_operation(
            operation="user_register",
            username=register_data.username,
            request_data={"email": register_data.email}
        )
        
        # 返回纯业务数据
        return {"user_id": user.id, "username": user.username}
    
    async def refresh_token(
        self,
        token_data: RefreshTokenRequest
    ) -> Dict[str, Any]:
        """
        刷新令牌业务逻辑
        
        Args:
            token_data: 刷新令牌数据
            
        Returns:
            新的访问令牌
        """
        # 验证刷新令牌
        payload = verify_token(token_data.refresh_token, token_type="refresh")
        if payload is None:
            raise_unauthorized_error("无效的刷新令牌")
        
        username: str = payload.get("sub")
        if username is None:
            raise_unauthorized_error("无效的刷新令牌")
        
        # 验证用户
        user = await self.user_repo.get_by_username(username)
        if user is None:
            raise_unauthorized_error("用户不存在")
        
        if not user.is_active:
            raise_validation_error("用户账号已被禁用")
        
        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "user_id": user.id},
            expires_delta=access_token_expires
        )
        
        # 创建新的刷新令牌（可选，为了更好的安全性）
        new_refresh_token = create_refresh_token(
            data={"sub": user.username, "user_id": user.id}
        )
        
        # 返回纯业务数据
        return {
            "token": access_token,
            "refreshToken": new_refresh_token
        }
    
    async def logout(self, user) -> Dict[str, Any]:
        """
        用户登出业务逻辑
        
        Args:
            user: 当前用户
            
        Returns:
            登出响应数据
        """
        # 记录登出日志
        await operation_logger.log_operation(
            operation="user_logout",
            user_id=user.id,
            username=user.username
        )
        
        # 这里可以实现token黑名单等逻辑
        # 返回纯业务数据
        return {"message": "登出成功"} 